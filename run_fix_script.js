const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const config = require('./src/config/config');

async function runFixScript() {
  let connection;
  
  try {
    // Create connection using your existing config
    connection = await mysql.createConnection({
      host: config.mysql_config.HOST,
      port: config.mysql_config.PORT,
      user: config.mysql_config.USER,
      password: config.mysql_config.PASSWORD,
      database: config.mysql_config.DB,
      multipleStatements: true // Allow multiple SQL statements
    });

    console.log('Connected to MySQL database');

    // Read the SQL file
    const sqlScript = fs.readFileSync(path.join(__dirname, 'fix_duplicate_constraints.sql'), 'utf8');
    
    // Split the script into individual statements (excluding comments and empty lines)
    const statements = sqlScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('USE'));

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          console.log(`Executing statement ${i + 1}: ${statement.substring(0, 50)}...`);
          await connection.execute(statement);
          console.log(`✓ Statement ${i + 1} executed successfully`);
        } catch (error) {
          if (error.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
            console.log(`⚠ Index already dropped or doesn't exist: ${statement.substring(0, 50)}...`);
          } else {
            console.error(`✗ Error executing statement ${i + 1}:`, error.message);
          }
        }
      }
    }

    // Verify the fix by checking remaining indexes
    console.log('\nVerifying fix...');
    const [rows] = await connection.execute(
      "SHOW INDEX FROM users WHERE Column_name = 'email'"
    );
    
    console.log(`\nRemaining email indexes: ${rows.length}`);
    rows.forEach(row => {
      console.log(`- ${row.Key_name}`);
    });

    if (rows.length === 1) {
      console.log('\n✅ Fix completed successfully! Only one email unique constraint remains.');
    } else {
      console.log('\n⚠ Warning: Multiple email constraints still exist. Manual cleanup may be needed.');
    }

  } catch (error) {
    console.error('Error running fix script:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the script
runFixScript();
