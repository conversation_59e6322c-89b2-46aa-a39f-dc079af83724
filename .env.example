# Port number of the service
PORT=9697

# URL of the client to configure in emails
CLIENT_URL=client-production-url

#MYSQL DB Details
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=Password@123
MYSQL_DB=smshub_db
MYSQL_POOL_MAX=5
MYSQL_POOL_MIN=0
MYSQL_ACQUIRE_TIME=30000
MYSQL_IDLE_TIME=10000

# JWT configurations
JWT_SECRET=thisisasamplesecret # JWT secret key
# Number of minutes after which an access token expires
JWT_ACCESS_EXPIRATION_MINUTES=30
# Number of days after which a refresh token expires
JWT_REFRESH_EXPIRATION_DAYS=30

# SMTP configuration options for the email service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=password
EMAIL_FROM=SMShub Support <<EMAIL>>

#Data layer configuration
DATA_SERVICE_URL=http://*************:8000

    
IMAGE_ENDPOINT=http://localhost:9697/media/

#LDAP details
LDAP_ENABLED=true
LDAP_SERVER_URL=ldap://127.0.0.1:3890
LDAP_USER_GROUP=users
LDAP_USER_DC=mtr,com

#Periodic Report Email
HOURLY_REPORTS=Hourly Traffic Analysis
DAILY_REPORTS=Hub Traffic Summary(DR),Customer Processing Fee Invoice Detail,Customer Termination Credit Details
REPORTS_EMAIL=<EMAIL>,<EMAIL>

#Log file 
LOG_LEVEL=debug
LOG_FILE_NAME=smshub_be
MAX_SIZE=1G
MAX_RETENTION_DAYS=180d

DEFAULT_TIMEZONE=Asia/Kolkata

UNSOLVED_ALERT_NOTIFY_ENABLED=false