# SMShub RBAC and reports

Before you can start ,you'll need to set up a few things first. 

1. Install Node.js : 
- Head over to [nodejs.org](nodejs.org) and download and install the latest version.
- Install Node Package Manager (NPM) - NPM is a huge repository of code modules that you can download and use in your projects. This app relies on several third-party libraries, so go ahead and download and install [NPM](https://www.npmjs.com/get-npm).
2. Create an empty database :
- You'll need an empty Mysql database for storing your application data 
Mysql config should be updated in .env file

3. Install Server :
	- Open up a new terminal window and navigate to the server folder inside the RBAC directory and run the following command: "npm install"
	- Copy the .env.example file to .env using: "cp .env.example .env"
	- Open the .env file, enter your Mysql settings .
	- In the terminal, run "yarn dev" or "npm start" to start the development server : This will start the SMShub backend server.

## Configurations 
All the configurations for the platform are defined in config folder
Config folder contains below config files

Src/config 
config.js
reportConfig.js
roles.js
globalConfig.js

System level configurations are defined in the env file and below are the parameters

.env file

| Parameter Name                  | Description                                                     | Value                                     |
|---------------------------------|-----------------------------------------------------------------|-------------------------------------------|
| PORT                            | Port number to be used by service                               | 9697                                      |
| CLIENT_URL                      | URL of the front end                                             | [https://smshub-fe.vercel.app](https://smshub-fe.vercel.app) |
| MYSQL_HOST                      | Mysql server hostname                                            | 127.0.0.1                                 |
| MYSQL_PORT                      | Mysql server port number                                         | 3306                                      |
| MYSQL_USER                      | Username to connect to MySQL database                            | root                                      |
| MYSQL_PASSWORD                  | Password to connect to MySQL database                            | Password@123                              |
| MYSQL_DB                        | Database name to be used                                         | smshub_db                                |
| MYSQL_POOL_MAX                  | Maximum size of MySQL connection pool                            | 5                                         |
| MYSQL_POOL_MIN                  | Minimum size of MySQL connection pool                            | 0                                         |
| MYSQL_ACQUIRE_TIME              | Time to wait for a connection to become available in milliseconds| 30000                                     |
| MYSQL_IDLE_TIME                 | Time a connection can be idle before being released in milliseconds | 10000                                  |
| JWT_SECRET                      | Secret to create JWT token                                       | thisisasamplesecret                      |
| JWT_ACCESS_EXPIRATION_MINUTES   | Token expiry time in minutes for access token                    | 300                                       |
| JWT_REFRESH_EXPIRATION_DAYS     | Token expiry time in days for refresh token                      | 30                                        |
| SMTP_HOST                       | SMTP hostname                                                    | smtp.gmail.com                           |
| SMTP_PORT                       | SMTP port number                                                 | 587                                       |
| SMTP_USERNAME                   | SMTP username                                                    | <EMAIL>                             |
| SMTP_PASSWORD                   | SMTP password                                                    | Password                                  |
| EMAIL_FROM                      | Email from address and name                                      | SMShub Support <<EMAIL>>            |
| DATA_SERVICE_URL                | Data layer access URL                                            | [http://**************:8080](http://**************:8080) |
| IMAGE_ENDPOINT                  | Path for accessing uploaded profile images                       | [http://localhost:9697/media/](http://localhost:9697/media/) |
| LDAP_ENABLED                    | Whether LDAP is enabled or not                                   | true                                      |
| LDAP_SERVER_URL                 | LDAP server URL                                                  | ldap://127.0.0.1:389                     |
| LDAP_USER_GROUP                 | LDAP user group                                                  | users                                     |
| LDAP_USER_DC                    | LDAP user domain component                                       | mtr,com                                   |
| HOURLY_REPORTS                  | List of reports to be sent hourly                                | Hourly Traffic Analysis                  |
| DAILY_REPORTS                   | List of reports to be sent daily                                 | Hub Traffic Summary(DR), Customer Processing Fee Invoice Detail, Customer Termination Credit Details |
| REPORTS_EMAIL                   | Email list to which reports need to be sent                      | <EMAIL>, <EMAIL>      |
| LOG_LEVEL                       | Log level for logging                                             | debug                                     |
| LOG_FILE_NAME                   | Name of the log file                                              | smshub_be                                |
| MAX_SIZE                        | Maximum size of the log file                                      | 1G                                        |
| MAX_RETENTION_DAYS              | Maximum retention days for log file                               | 180d                                      |


All the configurations are defined in config folder

Config/config.js - Contains all the general configurations read from .env file

Below configurations have default values which can be updated in config file

| Parameter                  | Description                                   | Value     |
|----------------------------|-----------------------------------------------|-----------|
| enableLoggingToFile        | Logs are written to file                      | 1         |
| enableLoggingToConsole     | Logs are written to console                   | 1         |
| otpLength                  | Otp generated length for 2FA verification    | 6         |




ReportConfig.js - Configurations required to fetch data from data layer are defined here

| Parameter                   | Description                                                        | Value                                                                 |
|-----------------------------|--------------------------------------------------------------------|-----------------------------------------------------------------------|
| maxPageLimit                | Default page limit value while fetching reports data               | 100000                                                                |
| reportClassification       | Different classification of reports and subgroups                  |   See below                                                           |
| reportsMapping              | Details required to fetch specific report from data layer           | See below                                                              |
| customerList                | Details to get customer list from data layer graphql layer          | {name: "getCustomerDetails",responseFields: {"billing_contact": "name","id":"id"}}|
| supplierList                | Details to get supplier list from data layer graphql layer          |  {name: "getSupplierDetails", responseFields: {"billing_contact": "name","id": "id"}}|
| destinationDetails         | Details to get destination details list from data layer graphql layer | {name: "getOperatorDetails",responseFields: {"country_name": "countryName","operator_name": " operatorName"}}|
| derivedReportFields       | List of Derived fields                                             | Total Submission, Successful Submission, Submission Failure, etc.      |
| panelVisualizationTypes   | Visualization types supported by Panels                            | Line Graph, Bar Graph, Pie Chart, Table Report, MultiAxis Graph       |
| panelProperties             | Details of panels supported for each visualization type            | See below                                                              |
| columnMapping             | Name mapping for data layer API and UI                             |  { "Datetime": "timestamp","A Number": "a_number","B Number": "b_number"} |
| rawCdr                      | Configurations to fetch raw cdr from data layer                   | {name: "fetchRawCdr",responseFields: ["customer_name","status","a_number","b_number","destination"]}|
| customerRoleFields       | Listing all fields specific to customer role                        | A Number, Customer Bind, Customer Name, Customer Protocol, etc.        |
| supplierRoleFields       | Listing all fields specific to supplier role                         | A Number, Supplier Bind, Supplier Name, Supplier Protocol, etc.         |

 ### Report Classification

```json
{
    "Hub Traffic": [
        {
            "name": "Customer wise destination wise Traffic Report",
            "viewBy": ["minute", "hour", "day", "week"]
        },
        {
            "name": "Customer wise destination wise Error Report",
            "viewBy": ["minute", "hour", "day", "week"]
        }
    ],
    "Performance": [
        {
            "name": "Customer Performance Report",
            "viewBy": ["minute", "hour", "day", "week", "month"]
        }
    ],
    "Billing": [
        {
            "name": "Customer Message Fee Invoice Detail",
            "viewBy": ["day"]
        }
    ],
    "RA": [],
    "Cust Outgoing": [],
    "Incoming Reports": []
}


```
### Reports Mapping

	//Report name and corresponding configurations
        "Aggregate Traffic Analysis": {
            name: "getAggregateTrafficAnalysis",  // Name as defined in data layer for graphql query
            normalizedName: "AggregateTrafficAnalysis",  // Normalized report name used to download tsv file using rest API shared by data layer
            defaultFilters: {  // Default interval supported by report
                viewBy: "minute" 
            },
            responseFields: { // These are the fields supported by report
		//name as per graphql query : Actual name to be displayed in UI
		//Same column order followed in UI
                "timestamp": "Date",
                "source_operator_code": "Source Operator Code",
                "source_operator_name": "Source Operator Name",
                "destination_operator_code": "Destination Operator Code",
                "destination_operator_name": "Destination Operator Name",
                "status": "Status",
                "number_of_records": "Count"
            },
	//Not all fields can be used for searching since some are aggregate values. Defining fields on which search is performed for specific report
            searchFields: [

                "source_operator_code",
                "source_operator_name",
                "destination_operator_code",
                "destination_operator_name",
                "status"
            ]
        }

### Panel properties
Details of panels supported for each visualization type
	
	{
	        "Line Graph": {
		//Filters supported by each type along with conditions supported.
    // For some fields data will be populated as dropdown values. Same is represented using datatype parameter
	            filters: [
	                {
	                    field: "Customer interface",
	                    condition: [
	                        "Equal",
	                        "Not Equal"
	                    ]
	                },
	                {
	                    field: "Customer Name",
	                    condition: [
	                        "Equal",
	                        "Not Equal"
	                    ],
	                    datatype: "dropdown"
	                }                
	            ],
		//Intervals supported in each graph type
	            "interval": [
	                "Minute",
	                "Hour",
	                "Day",
	                "Week"
	            ],
	
		//X-axis and Y-axis fields  shown for each type
	            columns: {
	                "derivedFields": [
	                    "Total Submission",
	                    "Successful Submission",
	                    "Submission Failure",
	                    "Total Delivery",
	                    "Successful Delivery",
	                    "Delivery Failure",
	                    "Pending Delivery",
	                    "Submission Efficiency",
	                    "Delivery Efficiency"
	                ]
	            }
	
	        }
	}

Global configurations - globalconfig.js file contains certain parameters which are used both by UI and middleware

| Parameter                     | Description                               | Value                   |
|-------------------------------|-------------------------------------------|-------------------------|
| MAX_DASHBOARDS                | Maximum dashboard allowed for a user      | 10                      |
| MAX_PANELS_PER_DASHBOARD      | Maximum panels that can be added to dashboard | 5                    |
| MAX_Y_AXIS_FIELDS             | Maximum fields allowed to select in panels | 4                      |
| MAX_CARDS_PER_DASHBOARD       | Maximum cards allowed in dashboard        | 4                       |
| FIELDS_WITH_MULTIPLE_CONDITIONS | Fields with high cardinality which requires more conditions | ["A Number", "Destination IMSI"]        |
| MAX_EMAIL_ATTACHMENT_SIZE     | Maximum file size for attachment (in MB)  | 7                       |
| MAX_TIME_RANGE_RAW_CDR        | Maximum time range allowed while CDR search (in min) | 30                |

