-- MySQL dump 10.13  Distrib 8.0.30, for macos10.15 (x86_64)
--
-- Host: localhost    Database: smshub_db
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `smshub_db`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `smshub_db` /*!40100 DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `smshub_db`;

--
-- Table structure for table `auditlogs`
--

DROP TABLE IF EXISTS `auditlogs`;


CREATE TABLE `auditlogs` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `username` varchar(255) NOT NULL,
  `roleName` varchar(255) DEFAULT NULL,
  `event` varchar(255) DEFAULT NULL,
  `action` varchar(300) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `userId` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;



--
-- Table structure for table `cards`
--

DROP TABLE IF EXISTS `cards`;

CREATE TABLE `cards` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `reportField` varchar(255) NOT NULL,
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cards_name_created_by` (`name`,`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;



--
-- Table structure for table `dashboards`
--

DROP TABLE IF EXISTS `dashboards`;

CREATE TABLE `dashboards` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `panels` json DEFAULT NULL,
  `cards` json DEFAULT NULL,
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dashboards_name_created_by` (`name`,`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;




--
-- Table structure for table `email_groups`
--

DROP TABLE IF EXISTS `email_groups`;

CREATE TABLE `email_groups` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `description` varchar(256) DEFAULT NULL,
  `members` json DEFAULT NULL,
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `subGroups` json DEFAULT NULL,
   `isSubGroup` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
 UNIQUE KEY `email_groups_name_created_by` (`name`,`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;


--
-- Table structure for table `panels`
--

DROP TABLE IF EXISTS `panels`;

CREATE TABLE `panels` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `visualizationType` varchar(64) NOT NULL,
  `filters` json DEFAULT NULL,
  `dataColumns` json DEFAULT NULL,
  `timePeriod` varchar(255) DEFAULT NULL,
  `interval` varchar(255) DEFAULT NULL,
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `panels_name_created_by` (`name`,`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;



--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;

CREATE TABLE `roles` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `description` varchar(256) DEFAULT NULL,
  `resources` json DEFAULT NULL,
  `staticReports` json DEFAULT NULL,
  `cards` json DEFAULT NULL,
  `dynamicDashboard` json DEFAULT NULL,
  `dashboardCount` smallint unsigned DEFAULT '0',
  `panelCount` smallint unsigned DEFAULT '0',
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `isSuperAdminRole` tinyint(1) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_created_by` (`name`,`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
INSERT INTO `roles` VALUES('6ca3e793-1252-488c-ab7b-602ea1ce2ad3','Default user role','Default User role','{\"Logs Management\" : 1, \"Default Dashboard\": 1}','{\"Billing\": 0, \"Latency Report\": 0, \"Incoming Report\": 0, \"Network Reports\": 0, \"Outgoing Report\": 0, \"Hourly Traffic Analysis\": 0, \"Hub Traffic Summary(DR)\": 0, \"Country Wise Performance\": 0, \"Network Rejection Report\": 0, \"Prepaid Settlement Report\": 0, \"Aggregate Traffic Analysis\": 0, \"Customer Performance Report\": 0, \"Prime Wise Termination Cost\": 0, \"Customer Wise Traffic Report\": 0}',NULL,NULL,0,0,'f7488bd3-f9af-4b26-b6f9-cd6c32a9e24c',0,1,'2024-02-18 02:07:19','2024-02-18 02:07:19'),('b3ceced6-e333-4d86-b76e-697f07e4a0f3','Super admin role','Super admin role','{\"Dashboard Management\": 15, \"Card Management\": 15,\"Logs Management\" : 1, \"Role Management\": 15, \"User Management\": 15, \"Alert Management\": 15,\"Panel Management\": 15, \"Group Management\": 15, \"Report Management\": 15,\"CDR Search\": 17}',NULL,NULL,NULL,0,0,'f7488bd3-f9af-4b26-b6f9-cd6c32a9e24c',1,1,'2024-02-18 02:02:06','2024-02-18 02:02:06'),('2a81be8f-4714-4e1d-8f7e-d2743e514d77','Supplier_role','Supplier_role','{\"CDR Search\": 0, \"Card Management\": 15, \"Logs Management\": 1, \"Alert Management\": 0, \"Group Management\": 15, \"Panel Management\": 15, \"Default Dashboard\": 1, \"Report Management\": 1, \"Dashboard Management\": 15}','{\"Latency Report\": 0, \"Hourly Traffic Analysis\": 0, \"Hub Traffic Summary(DR)\": 0, \"Country Wise Performance\": 0, \"Aggregate Traffic Analysis\": 0, \"Customer Performance Report\": 0, \"Customer Wise Traffic Report\": 0, \"Processing Fee Invoice Summary\": 0, \"Hourly Traffic Failure Analysis\": 0, \"Destination wise Latency Reports\": 0, \"Customer Message Fee Invoice Detail\": 0, \"Customer Termination Charges Detail\": 0, \"Customer Termination Credit Details\": 0, \"Customer Wise Destination Performance\": 0, \"Supplier Wise Destination Performance\": 0, \"Customer Performance Report (Next Hop)\": 0, \"Customer Processing Fee Invoice Detail\": 0, \"Hourly Traffic Analysis (Supplier Wise)\": 0, \"Summary of Customer Termination Charges\": 0, \"Customer Msg Fee Invoice Details Zero Rated\": 0, \"Customer wise destination wise Error Report\": 0, \"Supplier wise destination wise Error Report\": 17, \"Customer wise destination wise Latency Report\": 0, \"Customer wise destination wise Traffic Report\": 0, \"Supplier wise destination wise Latency Report\": 17, \"Customer Termination Credit Details Zero Rated\": 0, \"Customer Supplier Destination Wise Error Report\": 0, \"Hourly Customer Destination wise Traffic Report\": 0, \"Hourly Supplier Destination wise Traffic Report\": 17, \"Customer Wise Destination Performance (Next Hop)\": 0, \"Supplier Wise Destination Performance (Next Hop)\": 0}','{}','{\"string\": 0, \"dgdfgdf\": 0, \"create 123 \": 0, \"test_dashboard\": 0, \"bar dashboard 123\": 0, \"8th dashbaord test\": 0, \"9thdashbaord_panel\": 0, \"Test_dashboard_sanity\": 0, \"_test vdashboard_test dashboard_test dashboard_test doard_test dashboard_test  dashboard_test dashboard_test dashboard_test dashboard_testdashboard_testdashboard_test dashboard_test v dashboard_test b v b v dashboard_testdashboard_testdashboard_testv  \": 0}',10,5,'d634f076-fcae-4e79-8d6a-eb92c1b1b19f',0,1,'2024-04-08 09:16:28','2024-04-08 09:16:28'),('03319fcd-6e6c-4051-a3d4-f6e2b7da0d5f','Customer_role','Customer_role','{\"CDR Search\": 0, \"Card Management\": 15, \"Logs Management\": 1, \"Alert Management\": 0, \"Group Management\": 15, \"Panel Management\": 15, \"Default Dashboard\": 1, \"Report Management\": 1, \"Dashboard Management\": 15}','{\"Latency Report\": 0, \"Hourly Traffic Analysis\": 0, \"Hub Traffic Summary(DR)\": 0, \"Country Wise Performance\": 0, \"Aggregate Traffic Analysis\": 0, \"Customer Performance Report\": 0, \"Customer Wise Traffic Report\": 0, \"Processing Fee Invoice Summary\": 0, \"Hourly Traffic Failure Analysis\": 0, \"Destination wise Latency Reports\": 0, \"Customer Message Fee Invoice Detail\": 0, \"Customer Termination Charges Detail\": 0, \"Customer Termination Credit Details\": 0, \"Customer Wise Destination Performance\": 0, \"Supplier Wise Destination Performance\": 0, \"Customer Performance Report (Next Hop)\": 0, \"Customer Processing Fee Invoice Detail\": 0, \"Hourly Traffic Analysis (Supplier Wise)\": 0, \"Summary of Customer Termination Charges\": 0, \"Customer Msg Fee Invoice Details Zero Rated\": 0, \"Customer wise destination wise Error Report\": 17, \"Supplier wise destination wise Error Report\": 0, \"Customer wise destination wise Latency Report\": 17, \"Customer wise destination wise Traffic Report\": 17, \"Supplier wise destination wise Latency Report\": 0, \"Customer Termination Credit Details Zero Rated\": 0, \"Customer Supplier Destination Wise Error Report\": 0, \"Hourly Customer Destination wise Traffic Report\": 17, \"Hourly Supplier Destination wise Traffic Report\": 0, \"Customer Wise Destination Performance (Next Hop)\": 0, \"Supplier Wise Destination Performance (Next Hop)\": 0}','{}','{\"string\": 0, \"dgdfgdf\": 0, \"create 123 \": 0, \"test_dashboard\": 0, \"bar dashboard 123\": 0, \"8th dashbaord test\": 0, \"9thdashbaord_panel\": 0, \"Test_dashboard_sanity\": 0, \"_test vdashboard_test dashboard_test dashboard_test doard_test dashboard_test  dashboard_test dashboard_test dashboard_test dashboard_testdashboard_testdashboard_test dashboard_test v dashboard_test b v b v dashboard_testdashboard_testdashboard_testv  \": 0}',10,5,'d634f076-fcae-4e79-8d6a-eb92c1b1b19f',0,1,'2024-04-08 09:16:28','2024-04-08 09:16:28'),('3c2fc0ee-9cbf-48e7-a84e-63fb3bf4805b','Customer_Supplier_role','Customer_Supplier_role','{\"CDR Search\": 0, \"Card Management\": 15, \"Logs Management\": 1, \"Alert Management\": 0, \"Group Management\": 15, \"Panel Management\": 15, \"Default Dashboard\": 1, \"Report Management\": 1, \"Dashboard Management\": 15}','{\"Latency Report\": 0, \"Hourly Traffic Analysis\": 0, \"Hub Traffic Summary(DR)\": 0, \"Country Wise Performance\": 0, \"Aggregate Traffic Analysis\": 0, \"Customer Performance Report\": 0, \"Customer Wise Traffic Report\": 0, \"Processing Fee Invoice Summary\": 0, \"Hourly Traffic Failure Analysis\": 0, \"Destination wise Latency Reports\": 0, \"Customer Message Fee Invoice Detail\": 0, \"Customer Termination Charges Detail\": 0, \"Customer Termination Credit Details\": 0, \"Customer Wise Destination Performance\": 0, \"Supplier Wise Destination Performance\": 0, \"Customer Performance Report (Next Hop)\": 0, \"Customer Processing Fee Invoice Detail\": 0, \"Hourly Traffic Analysis (Supplier Wise)\": 0, \"Summary of Customer Termination Charges\": 0, \"Customer Msg Fee Invoice Details Zero Rated\": 0, \"Customer wise destination wise Error Report\": 17, \"Supplier wise destination wise Error Report\": 17, \"Customer wise destination wise Latency Report\": 17, \"Customer wise destination wise Traffic Report\": 17, \"Supplier wise destination wise Latency Report\": 17, \"Customer Termination Credit Details Zero Rated\": 0, \"Customer Supplier Destination Wise Error Report\": 0, \"Hourly Customer Destination wise Traffic Report\": 17, \"Hourly Supplier Destination wise Traffic Report\": 17, \"Customer Wise Destination Performance (Next Hop)\": 0, \"Supplier Wise Destination Performance (Next Hop)\": 0}','{}','{\"string\": 0, \"dgdfgdf\": 0, \"create 123 \": 0, \"test_dashboard\": 0, \"bar dashboard 123\": 0, \"8th dashbaord test\": 0, \"9thdashbaord_panel\": 0, \"Test_dashboard_sanity\": 0, \"_test vdashboard_test dashboard_test dashboard_test doard_test dashboard_test  dashboard_test dashboard_test dashboard_test dashboard_testdashboard_testdashboard_test dashboard_test v dashboard_test b v b v dashboard_testdashboard_testdashboard_testv  \": 0}',10,5,'d634f076-fcae-4e79-8d6a-eb92c1b1b19f',0,1,'2024-04-08 09:16:28','2024-04-08 09:16:28');

UNLOCK TABLES;

--
-- Table structure for table `tokens`
--

DROP TABLE IF EXISTS `tokens`;

CREATE TABLE `tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `token` varchar(255) NOT NULL,
  `type` enum('refresh','resetPassword','emailVerification') NOT NULL,
  `expires` datetime NOT NULL,
  `blacklisted` tinyint(1) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `userId` char(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `userId` (`userId`),
  CONSTRAINT `tokens_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;



--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `name` varchar(256) NOT NULL,
  `email` varchar(256) NOT NULL,
  `isSuperAdmin` tinyint(1) DEFAULT '0',
  `countryCode` varchar(10) DEFAULT NULL,
  `phoneNumber` varchar(20) DEFAULT NULL,
  `defaultDashboard` varchar(255) DEFAULT NULL,
  `isEmailVerified` tinyint(1) DEFAULT '0',
  `password` varchar(255) DEFAULT NULL,
  `profileImage` varchar(255) DEFAULT NULL,
  `customerList` json DEFAULT NULL,
  `supplierList` json DEFAULT NULL,
  `createdBy` char(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `isUserActive` tinyint(1) DEFAULT '1',
  `isLdapUser` tinyint(1) DEFAULT '0',
  `lastLoggedin` datetime(6) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `roleId` char(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
   `type` enum('Customer','Supplier','Both') DEFAULT 'Both',
   `oldPasswords` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
 
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('d634f076-fcae-4e79-8d6a-eb92c1b1b19f','{{fe.admin_username}}','{{ fe.admin_user }}',1,NULL,NULL,NULL,1,'$2a$10$b1niaWUF0FZ07bmEo5kPze/gKhw6fIbZ9f91O61qraQ44/tg5gNtG',NULL,NULL,NULL,NULL,1,0,'2024-02-23 09:46:40.290000','2024-02-23 05:36:49','2024-02-24 07:11:05','b3ceced6-e333-4d86-b76e-697f07e4a0f3','Both',NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-24 12:46:12
