const app = require("./app");
const config = require("./config/config");
const logger = require("./config/logger");

const { sequelize, models } = require("./models");
const {
  adminRole,
  adminUser,
  defaultUserRole
} = require("./config/roles");
const { padding } = require("./utils/padding");

const { User, Role } = models;
let server;
const eraseDatabaseOnSync = false;

if (config.postgres || config.mysql_config) {
  sequelize
    .sync({
      force: eraseDatabaseOnSync,
      alter: process.env.NODE_ENV === 'development' ? true : false
    })
    .then(async () => {
      var adminuser = await User.findOne({ where: { email: adminUser.email } });
      if (!adminuser) {
        adminuser = await User.create(adminUser);
        logger.info("Admin user created successfully");
        adminRole.createdBy = adminuser.id;
      } else adminRole.createdBy = adminuser.id;
      try {
        var doc = await Role.findOne({ where: { name: adminRole.name } });
      } catch (err) {
        logger.error("Error in default role creation ", err);
      }
      if (!doc) {
        let role = await Role.create(adminRole);
        logger.info("Admin role created successfully");
        // let user = await User.findOne({where :{email: adminUser.email}});
        Object.assign(adminuser, { roleId: role.id });
        adminuser.save();
      }

      let defaultRole = await Role.findOne({
        where: { name: defaultUserRole.name },
      });

      if (!defaultRole) {
        defaultUserRole.createdBy = adminuser.id;
        let role = await Role.create(defaultUserRole);
        logger.info("Default role created successfully");
      }

     
      server = app.listen(config.port, () => {
        logger.info(`Listening to port ${config.port}`);
      });
    });
}

const exitHandler = () => {
  if (server) {
    server.close(() => {
      logger.info("Server closed");
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
};

const unexpectedErrorHandler = (error) => {
  logger.info(error);
  exitHandler();
};

process.on("uncaughtException", unexpectedErrorHandler);
process.on("unhandledRejection", unexpectedErrorHandler);

process.on("SIGTERM", () => {
  logger.info("SIGTERM received");
  if (server) {
    server.close();
  }
});
