const Joi = require('joi');
const { password } = require('./custom.validation');


const createSelectedColumns = {
  body: Joi.object().keys({
    reportName: Joi.string().required(),
    reportType: Joi.string().valid("static", "dynamic").required(),
    reportId: Joi.string().optional(),
    selectedColumns: Joi.array().items(Joi.string()).required(),
  }),
}


const getSelectedColumns = {
  query: Joi.object().keys({
    reportName: Joi.string().required(),
    reportType: Joi.string().valid("static", "dynamic").required(),
    reportId: Joi.string().optional(),
    limit: Joi.number().integer().optional(),
    page: Joi.number().integer().optional(),
  }),
};



module.exports = {
  createSelectedColumns,
  getSelectedColumns,
};