const Joi = require('joi');
const { password } = require('./custom.validation'),

 register = {
  body: Joi.object().keys({
    email: Joi.string().required().
email(),
    password: Joi.string().required().
custom(password),
    name: Joi.string().required(),
  }).
unknown(),
},

 login = {
  body: Joi.object().keys({
    email: Joi.string().required(),
    password: Joi.string().required(),
    timezone : Joi.string()
  }),
},

 logout = {
  body: Joi.object().keys({}),
  cookies: Joi.object().
    keys({
      refreshToken: Joi.string().required(),
    }).
    unknown(true),
},

 refreshTokens = {
  cookies: Joi.object().
  keys({
    refreshToken: Joi.string().required(),
  }).
  unknown(true)
},

 forgotPassword = {
  body: Joi.object().keys({
    email: Joi.string().email().
required(),
  }),
},

 resetPassword = {
  
  body: Joi.object().keys({
    token: Joi.string().required(),
    password: Joi.string().required().
custom(password),
  }),
},

 otpVerification = {
 
  body: Joi.object().keys({
    otp: Joi.string(),
    email: Joi.string(),
    timezone : Joi.string()
  })
},

 emailVerification = {
  query: Joi.object().keys({
    token: Joi.string().required(),
  }),
  body: Joi.object().keys({}),
};

module.exports = {
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  emailVerification,
  otpVerification
};