const Joi = require('joi');

const staticReports = {
  body: Joi.object().keys({
    reportName: Joi.string().required(),
    startDate: Joi.string().required(),
    endDate: Joi.string().required(),
    timezone: Joi.string().required(),
    download: Joi.number(),
    type: Joi.string().allow(""),
    sendEmail: Joi.number(),
    mailList: Joi.array().items(Joi.string().email()),
    groupList: Joi.array().items(Joi.string()),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
    search: Joi.string().allow(""),
    defaultViewBy: Joi.string().valid("day", "week", "month"),
    filters: Joi.object()
  }),
};

const getReports = {
  body: Joi.object().keys({
    reportName: Joi.string().required().
email(),
    filters: Joi.object()
  }),
};

const getFilterFields = {
  query: Joi.object().keys({
    reportName: Joi.string().required(),
    filterFlag: Joi.string(),
    visualizationType: Joi.string()
  }),
}

module.exports = {
  staticReports,
  getReports,
  getFilterFields
};