const Joi = require('joi');
const reportConfig = require('../config/reportConfig');

  createPanel = {
    body: Joi.object().keys({
      name: Joi.string().required().
        min(2).
        max(256).pattern(new RegExp(/[a-z]+/i)).
        messages({
          'string.pattern.base': 'Name must contain atleast one alphabet',
        }),
      visualizationType: Joi.string().required().
        valid(...Object.values(reportConfig.panelVisualizationTypes)).
        required().
        messages({
          'string.empty': 'Visualization type cannot be empty',
          'any.required': 'Visualization type parameter is required',
          'any.only': `Visualization type must be one of [${Object.values(reportConfig.panelVisualizationTypes)}]`
        }),
      filters: Joi.array()
    }).
      unknown()
  },

  getPanels = {
    query: Joi.object().keys({
      search: Joi.string(),
      limit: Joi.number().integer(),
      page: Joi.number().integer(),
      type: Joi.string(),
      createdBy: Joi.string()
    }),
  },

  getPanel = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
  },

  updatePanel = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
    body: Joi.object().
      keys({
        name: Joi.string().min(2).
          max(256).pattern(new RegExp(/[a-z]+/i)).
          messages({
            'string.pattern.base': 'Name must contain atleast one alphabet',
          }),
        visualizationType: Joi.string().
          valid(...Object.values(reportConfig.panelVisualizationTypes)).
          required().
          messages({
            'string.empty': 'Visualization type cannot be empty',
            'any.only': `Visualization type must be one of [${Object.values(reportConfig.panelVisualizationTypes)}]`
          }),
        filters: Joi.array()
      }).
      unknown().
      min(1),
  },

  deletePanel = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
  };

module.exports = {
  createPanel,
  getPanel,
  getPanels,
  updatePanel,
  deletePanel
};