const Joi = require('joi');
const { password } = require('./custom.validation'),

 createUser = {
  body: Joi.object().keys({
    email: Joi.string().required().
email().min(2).max(256),
    password: Joi.string().required().
custom(password),
    name: Joi.string().required().min(2).max(256),
    role: Joi.string().guid().
required()
  }),
},

 getUsers = {
  query: Joi.object().keys({
    name: Joi.string(),
    role: Joi.string(),
    sortBy: Joi.string(),
    search: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
},

 getUser = {
  params: Joi.object().keys({
    userId: Joi.string(),
  }),
},

 updateUser = {
  params: Joi.object().keys({
    userId: Joi.required(),
  }),
  body: Joi.object().
    keys({
      name: Joi.string().min(2).max(256),
      phoneNumber: Joi.string(),
      defaultDashboard: Joi.string()

    }).
unknown().
    min(1),
},

 deleteUser = {
  params: Joi.object().keys({
    userId: Joi.string(),
  }),
};

module.exports = {
  createUser,
  getUsers,
  getUser,
  updateUser,
  deleteUser,
};