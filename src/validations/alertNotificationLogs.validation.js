const Joi = require("joi");
const { alertConfig } = require("../config/alertConfig");

const getAlertsNotificationHistory = {
  query: Joi.object().keys({
    status: Joi.string().optional(),
    alert_type: Joi.string().optional(),
    search: Joi.string().optional(),
    sortBy: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
  }),
};

const getAlertsNotificationHistoryById = {
  params: Joi.object().keys({
    id: Joi.number().integer().required(),
  }),
};

const updateAlertsNotificationHistoryStatus = {
  params: Joi.object().keys({
    id: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    status: Joi.string()
      .valid(
        alertConfig.alertsHistoryStatus.ANALYZING,
        alertConfig.alertsHistoryStatus.OPEN,
        alertConfig.alertsHistoryStatus.CLOSED,
        alertConfig.alertsHistoryStatus.FIXED,
        alertConfig.alertsHistoryStatus.DELETED
      )
      .optional(),
    description: Joi.string().optional(),
  }).min(1), // At least one field must be provided
};

module.exports = {
  getAlertsNotificationHistory,
  getAlertsNotificationHistoryById,
  updateAlertsNotificationHistoryStatus,
};
