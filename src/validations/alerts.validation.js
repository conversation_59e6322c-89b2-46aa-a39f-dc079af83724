const { alertConfig, alertApiFields } = require("../config/alertConfig");
const logger = require("../config/logger");

const Joi = require("joi"),
  getGroups = {
    body: Joi.object().keys({
      reportName: Joi.string().required().email(),
      filters: Joi.object(),
    }),
  };

const createGroup = {
  body: Joi.object().keys({
    members: Joi.array()
      .items(Joi.string().email().min(2).max(256))
      .messages({
        "string.email": "Members should be valid email",
        // 'array.includesRequiredUnknowns' : "Members should contain atleast one email"
      })
      .required(),
    description: Joi.string(),
    isSubGroup: Joi.boolean(),
    subGroups: Joi.array().items(Joi.string()),
    name: Joi.string()
      .required()
      .min(2)
      .max(256)
      .pattern(new RegExp(/[a-z]+/i))
      .messages({
        "string.pattern.base": "Name must contain atleast one alphabet",
      }),
  }),
};

const updateGroup = {
  body: Joi.object().keys({
    members: Joi.array().items(Joi.string().email().min(2).max(256)).messages({
      "string.email": "Members should be valid email",
      // 'array.includesRequiredUnknowns' : "Members should contain atleastone email"
    }),
    subGroups: Joi.array().items(Joi.string()),
    isSubGroup: Joi.boolean(),
    description: Joi.string(),
    name: Joi.string()
      .min(2)
      .max(256)
      .pattern(new RegExp(/[a-z]+/i))
      .messages({
        "string.pattern.base": "Name must contain atleast one alphabet",
      }),
  }),
};

/** Create Alert */
const allowedFields = [
  alertApiFields.total_submitted,
  alertApiFields.submission_success,
  alertApiFields.submission_error,
  alertApiFields.delivery_success,
  alertApiFields.delivery_error,
];

const operatorRegex = "(<=|>=|<|>|&lt;=|&gt;=|&lt;|&gt;)";
const valueRegex = "\\d+";

const alertDynamicFilterRegex = new RegExp(
  `^(${allowedFields.join("|")})\\s${operatorRegex}\\s(${valueRegex})$`
);

const createAlertValidation = {
  body: Joi.object()
    .keys({
      timediff: Joi.number().integer().min(0).max(10).default(1).optional(), // For future purpose..
      timerange: Joi.number().integer().min(1).max(10).required(), // last day, last 3 days, last 7 days
      evaluationTimeframe: Joi.number().integer().min(1).max(60).required(), // 60 -> last 60 min, 15 -> last 15 min
      runEvery: Joi.number().integer().min(1).max(100).required(), // minutes
      customers: Joi.array().items(Joi.string()).default([]),
      suppliers: Joi.array().items(Joi.string()).default([]),
      destinations: Joi.array().items(Joi.string()).default([]),
      filters: Joi.array().items(
        // Joi.string().pattern(
        //   // /^(\b(total_submitted|submission_success|submission_error|delivery_success)\b)\s(<=|>=|<|>|&lt;=|&gt;=|&lt;|&gt;)\s(\d+)$/
        //   alertDynamicFilterRegex
        // )
      ),
      alertName: Joi.string().required(),
      alertType: Joi.string()
        .required()
        .valid(...Object.values(alertConfig.types)),
      // contactPoint: Joi.array().items(Joi.string().required()).required(),
      individuals: Joi.array().items(Joi.string().optional()).optional(),
      groups: Joi.array().items(Joi.string().optional()).optional(),
      threshold: Joi.number().integer().min(1).max(100).required(),
      deviation: Joi.number().integer().min(1).max(100).optional(),
      node: Joi.array().items(Joi.string()).optional(),
      resultCode: Joi.string().optional(),
      errorDescription: Joi.string().optional(),
      volumeType: Joi.string()
        .optional()
        .valid(...Object.values(alertConfig.volumeTypes)),
      minSubmissionCount: Joi.number()
        .integer()
        .max(1_00_00_000)
        .optional()
        .allow(null),
      category: Joi.string().required().valid("Major", "Minor", "Critical"),
    })
    .custom((obj, helpers) => {
      const hasAtLeastOneField = [
        "customers",
        "suppliers",
        "destinations",
      ].some((key) => Array.isArray(obj[key]) && obj[key].length > 0);

      if (!hasAtLeastOneField) {
        return helpers.message(
          'At least one of "customers", "suppliers", or "destinations" must have at least one value.'
        );
      }

      const {
        alertType,
        suppliers,
        customers,
        destinations,
        node,
        resultCode,
        errorDescription,
        volumeType,
      } = obj;
      const parametersConfig = alertConfig.filterOptions[alertType] || [];

      const isSupPresent = parametersConfig.find((name) =>
        name.toLowerCase().includes("supplier")
      );
      const isCusPresent = parametersConfig.find((name) =>
        name.toLowerCase().includes("customer")
      );
      const isDestPresent = parametersConfig.find((name) =>
        name.toLowerCase().includes("destination")
      );

      let errMsg = "";
      if (!isSupPresent && suppliers && suppliers.length) {
        errMsg = `"suppliers" not allowed for "${alertType}" alert type.`;
      }
      if (!isCusPresent && customers && customers.length) {
        errMsg = `"customers" not allowed for "${alertType}" alert type.`;
      }
      if (!isDestPresent && destinations && destinations.length) {
        errMsg = `"destinations" not allowed for "${alertType}" alert type.`;
      }

      if (errMsg) {
        logger.info(
          `Available parameters config ${JSON.stringify(parametersConfig)}`
        );
        return helpers.message(errMsg);
      }

      // Additional validation: node, resultCode, and errorDescription for "ERROR" alertType
      if (alertType === alertConfig.types.ERROR) {
        if (
          !Array.isArray(node) ||
          node.length === 0 ||
          typeof resultCode !== "string" ||
          !resultCode.trim() ||
          typeof errorDescription !== "string" ||
          !errorDescription.trim()
        ) {
          return helpers.message(
            `"node", "resultCode", and "errorDescription" are mandatory for "${alertConfig.types.ERROR}" alert type.`
          );
        }
      }

      if (alertType === alertConfig.types.VOLUME_DROP_OR_SPIKE) {
        if (!volumeType)
          return helpers.message(`Mandatory field missing[volumeType]`);
      }

      return obj; // Validation succeeded
    }, "Custom validation for alertType"),
};

const updateAlertValidation = {
  ...createAlertValidation,
  params: {
    id: Joi.number().required(),
  },
};

const updateAlertHistoryValidation = {
  body: Joi.object().keys({
    alertHistoryIds: Joi.array().items(Joi.number().required()).required(),
    status: Joi.string()
      .required()
      .valid(...Object.values(alertConfig.alertsHistoryStatus)),
  }),
};

module.exports = {
  getGroups,
  createGroup,
  updateGroup,
  createAlertValidation,
  updateAlertValidation,
  updateAlertHistoryValidation,
};
