const Joi = require('joi'),

  createDashboard = {
    body: Joi.object().keys({
      name: Joi.string().required().
        min(2).
        max(256).pattern(new RegExp(/[a-z]+/i)).
        messages({
          'string.pattern.base': 'Name must contain atleast one alphabet',
        }),
      panels: Joi.array(),
      cards: Joi.array()
    }).
      unknown()
  },

  getDashboards = {
    query: Joi.object().keys({
      search: Joi.string(),
      limit: Joi.number().integer(),
      page: Joi.number().integer(),
      createdBy: Joi.string()
    }),
  },

  getDashboard = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
  },

  updateDashboard = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
    body: Joi.object().
      keys({
        name: Joi.string().min(2).
          max(256).pattern(new RegExp(/[a-z]+/i)).
          messages({
            'string.pattern.base': 'Name must contain atleast one alphabet',
          })
      }).
      unknown().
      min(1),
  },

  deleteDashboard = {
    params: Joi.object().keys({
      id: Joi.string().uuid(),
    }),
  };

module.exports = {
  createDashboard,
  getDashboard,
  getDashboards,
  updateDashboard,
  deleteDashboard
};