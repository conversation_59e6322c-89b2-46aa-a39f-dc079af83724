const Joi = require('joi');

 const createRole = {
  body: Joi.object().keys({
    name: Joi.string().required().
min(2).
max(256).pattern(new RegExp(/[a-z]+/i)).
messages({
  'string.pattern.base': 'Name must contain atleast one alphabet',
}),
    description: Joi.string(),
    resources: Joi.array(),
    reports: Joi.array(),
    customerList: Joi.array(),
    supplierList: Joi.array()
  }).
unknown()
}

const getRoles = {
  query: Joi.object().keys({
    search: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
}

 const getRole = {
  params: Joi.object().keys({
    roleId: Joi.string(),
  }),
},

 updateRole = {
  params: Joi.object().keys({
    roleId: Joi.required(),
  }),
  body: Joi.object().
    keys({
      name: Joi.string().min(2).
max(256).pattern(new RegExp(/[a-z]+/i)).
messages({
  'string.pattern.base': 'Name must contain atleast one alphabet',
}),
      description: Joi.string(),
      resources: Joi.array()
    }).
unknown().
    min(1),
},

 deleteRole = {
  params: Joi.object().keys({
    roleId: Joi.string(),
  }),
};

module.exports = {
  createRole,
  getRoles,
  getRole,
  updateRole,
  deleteRole
};