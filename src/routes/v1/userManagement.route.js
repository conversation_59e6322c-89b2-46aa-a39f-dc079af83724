const express = require('express');
const usermanagementAuth = require('../../middlewares/usermanagementAuth');
const { dashboardController, reportsController } = require('../../controllers');


const router = express.Router();

router.route('/dashboard')
  .get(usermanagementAuth(), dashboardController.getDashboards)

router.route('/customer')
  .get(usermanagementAuth(), reportsController.getCustomerList)

router.route('/supplier')
  .get(usermanagementAuth(), reportsController.getSupplierList)



 module.exports = router;