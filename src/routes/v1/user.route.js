const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const userValidation = require('../../validations/user.validation');
const userController = require('../../controllers/user.controller');
const { accessRights,resources,rolePermissions } = require('../../config/roles'),


 router = express.Router();

router.
  route('/').
  post(auth(resources.USER_MANAGEMENT,rolePermissions.create),validate(),userController.createUser).
  get(auth(resources.USER_MANAGEMENT,rolePermissions.view),validate(userValidation.getUsers),userController.getUsers);

  router.
  route('/logs').
  get(auth(resources.LOG_MANAGEMENT,rolePermissions.view),userController.getAllAuditLogs);


  router.
  route('/logs/events').
  get(userController.getAuditLogEvents);

  router.
  route('/config/').
  get(userController.getGlobalConfig);


router.
  route('/:userId').
  get(auth(resources.USER_MANAGEMENT,rolePermissions.view),validate(userValidation.getUser),userController.getUser).
  put(auth(resources.USER_MANAGEMENT,rolePermissions.update),validate(),userController.updateUser).
  delete(auth(resources.USER_MANAGEMENT,rolePermissions.delete),validate(userValidation.deleteUser),userController.deleteUser);


module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management and retrieval
 */

/**
 * @swagger
 * path:
 * 
 *  /users:
 *    post:
 *      summary: Create a user
 *      description: Only admins can create other users.
 *      tags: [Users]
 *      security:
 *        - bearerAuth: []
 *      consumes:
 *      - "multipart/form-data"
 *      produces:
 *      - "application/json"
 *      requestBody:
 *        content:
 *          multipart/form-data:
 *            schema:
 *              type: object
 *              properties:
 *                image:
 *                  type: string
 *                  format: binary
 *                name:
 *                  type: string
 *                email:
 *                  type: string
 *                  format: email
 *                  description: must be unique
 *                countryCode:
 *                  type: string
 *                phoneNumber:
 *                  type: string
 *                defaultDashboard:
 *                  type: string
 *                password:
 *                  type: string
 *                  format: password
 *                  minLength: 8
 *                  description: At least one number and one letter
 *                role:
 *                   type: string
 *                type:
 *                  type: string 
 *                  enum: [Customer, Supplier, Both]
 * 
 *                customerList:
 *                  type: array
 *                  items:  
 *                    type: string
 *                supplierList:
 *                  type: array
 *                  items:  
 *                    type: string
 *                   
 *              example:
 *                name: fake name
 *                email: <EMAIL>
 *                password: password1
 *                role: user
 *                phonenNumber: 3543543543
 *                defaultDashboard: 23534534
 *                customerList: ["cust1","cust2"]
 *                supplierList: ["supp1" ,"supp2"]
 *      responses:
 *        "201":
 *          description: Created
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all users
 *      description: Only admins can retrieve all users.
 *      tags: [Users]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: name
 *          schema:
 *            type: string
 *          description: User name
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: search by user name or role
 *        - in: query
 *          name: role
 *          schema:
 *            type: string
 *          description: User role
 *        - in: query
 *          name: sortBy
 *          schema:
 *            type: string
 *          description: sort by query in the form of field:desc/asc (ex. name:asc)
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of users
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/User'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */


/**
 * @swagger
 * path:
 *  /users/{id}:
 *    get:
 *      summary: Get a user
 *      description: Logged in users can fetch only their own user information. Only admins can fetch other users.
 *      tags: [Users]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: User id
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    put:
 *      summary: Update a user
 *      description: Logged in users can only update their own information. Only admins can update other users.
 *      tags: [Users]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: User id

 *      consumes:
 *      - "multipart/form-data"
 *      produces:
 *      - "application/json"
 *      requestBody:
 *        content:
 *          multipart/form-data:
 *            schema:
 *              type: object
 *              properties:
 *                image:
 *                  type: string
 *                  format: binary
 *                name:
 *                  type: string
 *                email:
 *                  type: string
 *                  format: email
 *                  description: must be unique
 *                role:
 *                  type: string
 *                  description: role ID
 *                isUserActive: 
 *                  type: boolean
 *                  description: User status
 *                password:
 *                  type: string
 *                  format: password
 *                  minLength: 8
 *                  description: At least one number and one letter
 *                type:
 *                  type: string 
 *                  enum: [Customer, Supplier, Both]
 *              example:
 *                name: fake name
 *                email: <EMAIL>
 *                password: password1
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    delete:
 *      summary: Delete a user
 *      description: Logged in users can delete only themselves. Only admins can delete other users.
 *      tags: [Users]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: User id
 *      responses:
 *        "200":
 *          description: No content
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */


/**
 * @swagger
 * path:
 *  /users/logs:
 *    get:
 *      summary: Get all audit logs
 *      description: Audit logs
 *      tags: [Audit logs]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: search keyword
 *        - in: query
 *          name: role
 *          schema:
 *            type: string
 *          description: filter based on role name 
 *        - in: query
 *          name: event
 *          schema:
 *            type: string
 *          description: filter based on event
 *        - in: query
 *          name: startDate
 *          schema:
 *            type: string
 *          description: Start date filtering
 *        - in: query
 *          name: endDate
 *          schema:
 *            type: string
 *          description: End date filtering
 *        - in: query
 *          name: download
 *          schema:
 *            type: boolean
 *          description: Data to be downloaded or not
 *        - in: query
 *          name: type
 *          schema:
 *            type: string
 *          description: If file to be downloaded then file type CSV,EXCEL or PDF
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of logs
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:

 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */


/**
 * @swagger
 * path:
 *  /users/logs/events:
 *    get:
 *      summary: Get all audit log events
 *      description: Audit log events
 *      tags: [Audit logs]
 *      security:
 *        - bearerAuth: []
 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:

 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */


/**
 * @swagger
 * path:
 *  /users/config:
 *    get:
 *      summary: Get all global config 
 *      description: Get all global config 
 *      tags: [Global Config]
 *      security:
 *        - bearerAuth: []
 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:

 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */