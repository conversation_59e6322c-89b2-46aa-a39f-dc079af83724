const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');

const cardsController = require('../../controllers/cards.controller');
const { accessRights,resources,rolePermissions } = require('../../config/roles'),


 router = express.Router();

router.
  route('/').
  post(auth(resources.CARD_MANAGEMENT,rolePermissions.create),validate(),cardsController.createCard).
  get(auth(resources.CARD_MANAGEMENT,rolePermissions.view),validate(),cardsController.getCards);

  router.post('/details', auth(resources.CARD_MANAGEMENT,rolePermissions.view), validate(), cardsController.getCardDetails);

  router.
  route('/:id').
  get(auth(resources.CARD_MANAGEMENT,rolePermissions.view),validate(),cardsController.getCard).
  put(auth(resources.CARD_MANAGEMENT,rolePermissions.update),validate(),cardsController.updateCard).
  delete(auth(resources.CARD_MANAGEMENT,rolePermissions.delete),validate(),cardsController.deleteCard);

  
module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Cards
 *   description: Card management
 */

/**
 * @swagger
 * path:
 * 
 *  /card:
 *    post:
 *      summary: Create a card
 *      description: Creating card.
 *      tags: [Cards]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - members
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                reportField: 
 *                  type: string
 *                   
 *              example:
 *                name: card1
 *                description: test card
 *                reportField: Total submission
 
 *      responses:
 *        "201":
 *          description: Created
 
 *        "400":
 *          description: Failed input validation

 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all cards
 *      description: List all cards
 *      tags: [Cards]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: name
 *          schema:
 *            type: string
 *          description: Card name
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: search by card name 
 *        - in: query
 *          name: sortBy
 *          schema:
 *            type: string
 *          description: sort by query in the form of field:desc/asc (ex. name:asc)
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of groups
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *        - in: query
 *          name: values
 *          schema:
 *            type: boolean
 *          default: false
 *          description: Parameter to say if card values are required or not
 *        - in: query
 *          name: createdBy
 *          schema:
 *            type: string
 *          description: User ID
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      type: string
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */


/**
 * @swagger
 * path:
 *  /card/{id}:
 *    get:
 *      summary: Get a card
 *      description: Get card details
 *      tags: [Cards]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: card id
*        - in: query
 *          name: values
 *          schema:
 *            type: boolean
 *          default: false
 *          description: Parameter to say if card values are required or not
 *      responses:
 *        "200":
 *          description: OK
 
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    put:
 *      summary: Update a card
 *      description: Update card
 *      tags: [Cards]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Card id
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - members
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                members: 
 *                  type: array
 *                  items: 
 *                      type: string
 *                   
 *              example:
 *                name: Card1
 *                description: test card
 *                reportField: Total success

 *      responses:
 *        "200":
 *          description: OK 
 *        "400":
 *          description: Failed input validation
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    delete:
 *      summary: Delete  card
 *      description: Delete card
 *      tags: [Cards]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Card id
 *      responses:
 *        "200":
 *          description: No content
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */