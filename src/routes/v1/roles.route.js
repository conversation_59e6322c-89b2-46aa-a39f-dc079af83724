const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const roleValidation = require('../../validations/role.validation');
const roleController = require('../../controllers/role.controller');
const { accessRights,resources,rolePermissions } = require('../../config/roles'),

 router = express.Router();


router.
  route('/').
  post(auth(resources.ROLE_MANAGEMENT,rolePermissions.create),validate(roleValidation.createRole),roleController.createRole).
  get(auth(resources.ROLE_MANAGEMENT,rolePermissions.view),validate(roleValidation.getRoles),roleController.getAllRoles);

  router.
  route('/list').
  get(auth(),validate(roleValidation.getRole),roleController.getAllRoleNames)
router.
  route('/:roleId').
  get(auth(resources.ROLE_MANAGEMENT,rolePermissions.view),validate(roleValidation.getRole),roleController.getRole).
  put(auth(resources.ROLE_MANAGEMENT,rolePermissions.update),validate(roleValidation.updateRole),roleController.updateRole).
  delete(auth(resources.ROLE_MANAGEMENT,rolePermissions.delete),validate(roleValidation.deleteRole),roleController.deleteRole);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Roles
 *   description: Role management and retrieval
 */

/**
 * @swagger
 * path:
 *  /roles:
 *    post:
 *      summary: Create a role
 *      description: Create a role.
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - description
 *                - resources
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                panelCount: 
 *                  type: integer
 *                dashboardCount: 
 *                  type: integer
 *                resources:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          create : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 *                          update : 
 *                              type: boolean
 *                          delete : 
 *                              type: boolean
 *                staticReports:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          download : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 *                cards:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          download : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 *                          update : 
 *                              type: boolean
 *                          delete : 
 *                              type: boolean
 *
 *                dynamicDashboard:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          download : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 *                          update : 
 *                              type: boolean
 *                          delete : 
 *                              type: boolean
 
 *              example:
 *                "name": new_role
 *                "description": This is a new role
 *                "resources" : [
 *            {
 *           "name" : "Dashboard",
 *           "permissions" : {
 *               "create" : 1,
 *               "view" : 1,
 *               "update" : 0,
 *               "delete" : 0
 *           }
 *           }
 *         ]
 *                "staticReports" : [
 *            {
 *           "name" : "Customerwise destination traffic report",
 *           "permissions" : {
 *               "view" : 1,
 *               "download" : 1
 *           }
 *           }
 *          ]
 *                "dynamicReports" : [
 *            {
 *           "name" : "Customerwise destination traffic report",
 *           "permissions" : {
 *               "view" : 1,
 *               "download" : 1
 *           }
 *           }
 *          ]
 *                "dynamicDashboard" : [
 *            {
 *           "name" : "Customerwise destination traffic report",
 *           "permissions" : {
 *               "view" : 1,
 *               "download" : 1
 *           }
 *           }
 *          ]
 *          
 *      responses:
 *        "201":
 *          description: Created
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all roles
 *      description: Retrieve all roles.
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: role search
 *        - in: query
 *          name: sortBy
 *          schema:
 *            type: string
 *          description: sort by query in the form of field:desc/asc (ex. name:asc)
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of users
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/User'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */


 /**
 * @swagger
 * path:
 *  /roles/{id}:
 *    get:
 *      summary: Get a role
 *      description: Get role details.
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Role id
 
 *      responses:
 *        "200":
 *          description: Fetched data 
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    put:
 *      summary: Update a role
 *      description: Update role details.
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Role id
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - description
 *                - resources
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                 
 *                resources:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          create : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 *                          update : 
 *                              type: boolean
 *                          delete : 
 *                              type: boolean
 *                staticReports:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      permissions:
 *                        type: object
 *                        properties:
 *                          download : 
 *                              type: boolean
 *                          view : 
 *                              type: boolean
 
 *      
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/User'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 * 
 *    delete:
 *      summary: Delete a role
 *      description: Delete a role.
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Role id
 *      responses:
 *        "200":
 *          description: No content
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */


 /**
 * @swagger
 * path:
 *  /roles/list:
 *    get:
 *      summary: Get all role names
 *      description: Retrieve all role names 
 *      tags: [Roles]
 *      security:
 *        - bearerAuth: []
 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: array
 *                items:
 *                  type: string
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */