const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { panelValidation } = require('../../validations/');
const { panelController } = require('../../controllers/');
const { accessRights,resources,rolePermissions } = require('../../config/roles'),

 router = express.Router();


router.
    route('/').
    post(auth(resources.PANEL_MANAGEMENT,rolePermissions.create),validate(panelValidation.createPanel),panelController.createPanel).
    get(auth(resources.PANEL_MANAGEMENT,rolePermissions.view),validate(panelValidation.getPanels),panelController.getPanels);

router.
    route('/visualization').
    get(auth(resources.PANEL_MANAGEMENT,rolePermissions.view),validate(panelValidation.getPanel),panelController.getPanelVisualizationTypes)

router.
    route('/properties').
    get(auth(resources.PANEL_MANAGEMENT,rolePermissions.view),validate(panelValidation.getPanel),panelController.getPanelProperties)


router.
    route('/preview').
    post(auth(),validate(panelValidation.createPanel),panelController.getPanelPreview)
router.
    route('/:id').
    get(auth(resources.PANEL_MANAGEMENT,rolePermissions.view),validate(panelValidation.getPanel),panelController.getPanel).
    put(auth(resources.PANEL_MANAGEMENT,rolePermissions.update),validate(panelValidation.updatePanel),panelController.updatePanel).
    delete(auth(resources.PANEL_MANAGEMENT,rolePermissions.delete),validate(panelValidation.deletePanel),panelController.deletePanel);
    

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Panels
 *   description: Panel management 
 */

/**
 * @swagger
 * path:
 *  /panel:
 *    post:
 *      summary: Create a panel
 *      description: Create a panel.
 *      tags: [Panels]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object 
 *              properties:
 *                name:
 *                  type: string
 *                visualizationType:
 *                  type: string
 *                filters: 
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      field : 
 *                        type: string
 *                      condition : 
 *                        type: string
 *                      value : 
 *                        type: string
 *                      operator : 
 *                        type: string
 *                dataColumns:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      type : 
 *                        type: string               
 *                timePeriod: 
 *                  type: string
 *                interval:
 *                  type: string
 
 *              example:
 *                "name": new_panel
 *                "visualizationType": Bar
 *                "filters" : [
 *            {
 *           "field" : "customer name",
 *           "condition" : "equalTo",
 *           "value" : "xyz",
 *             "operator" : "and"
 *           }
 *         ]
 *                "dataColumns" : [
 *            {
 *           "name" : "Total Submission",
 *           "type" : "bar"
 *           }
 *          ]
 *                "timePeriod": "Last 7 days"
 *                "interval": "string"
 
 *      responses:
 *        "201":
 *          description: Created
 
 *        "400":
 *          description: Failed input validation
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all panels
 *      description: Retrieve all panles.
 *      tags: [Panels]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: panel search
 *        - in: query
 *          name: type
 *          schema:
 *            type: string
 *          description: panel type filter
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of panels
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *        - in: query
 *          name: createdBy
 *          schema:
 *            type: string
 *          description: User ID
 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/Panel'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */


/**
* @swagger
* path:
*  /panel/{id}:
*    get:
*      summary: Get a panel
*      description: Get panel details.
*      tags: [Panels]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: panel id
 
*      responses:
*        "200":
*          description: Fetched data 
*          content:
*            application/json:
*              schema:
*                 $ref: '#/components/schemas/Panel'
*        "400":
 *          description: Failed input validation
 *          content:
 *            application/json:
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*
*    put:
*      summary: Update a panel
*      description: Update panel details.
*      tags: [Panels]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: Panel id
*      requestBody:
*        required: true
*        content:
*          application/json:
*            schema:
*              type: object               
*              properties:
*                name:
*                  type: string
*                visualizationType:
*                  type: string
*                filters: 
*                  type: array
*                  items:  
*                    type: object
*                    properties:
*                      field : 
*                        type: string
*                      condition : 
*                        type: string
*                      value : 
*                        type: string
*                derivedFields:
*                  type: array
*                  items:  
*                    type: object
*                    properties:
*                      name : 
*                        type: string
*                      type : 
*                        type: string               
*                timePeriod: 
*                  type: string
*                interval:
*                  type: string
*      
*      responses:
*        "200":
*          description: OK
*          content:
*            application/json:
*              schema:
*                type: object
*                properties:
*                  results:
*                    type: array
*                    items:
*                      $ref: '#/components/schemas/Panel'
*                  page:
*                    type: integer
*                    example: 1
*                  limit:
*                    type: integer
*                    example: 10
*                  totalPages:
*                    type: integer
*                    example: 1
*                  totalResults:
*                    type: integer
*                    example: 1
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
* 
*    delete:
*      summary: Delete a panel
*      description: Delete a panel.
*      tags: [Panels]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: Panel id
*      responses:
*        "200":
*          description: No content
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*        "404":
*          $ref: '#/components/responses/NotFound'
*/


/**
 * @swagger
 * path:
 *  /panel/preview:
 *    post:
 *      summary: Get panel preview
 *      description: Get panel preview.
 *      tags: [Panels]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object 
 *              properties:
 *                name:
 *                  type: string
 *                visualizationType:
 *                  type: string
 *                filters: 
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      field : 
 *                        type: string
 *                      condition : 
 *                        type: string
 *                      value : 
 *                        type: string
 *                      operator : 
 *                        type: string
 *                dataColumns:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      name : 
 *                        type: string
 *                      type : 
 *                        type: string               
 *                startDate: 
 *                  type: string
 *                endDate: 
 *                  type: string
 *                interval:
 *                  type: string
  *                download:
 *                  type: boolean
 *                type:
 *                  type: string  
 *                sendEmail:
 *                  type: boolean
 *                mailList:
 *                  type: array
 *                  items:  
 *                    type: string
 
 
 *      responses:
 *        "200":
 *          description: Ok
 
 *        "400":
 *          description: Failed input validation
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */

/**
* @swagger
* path:
*  /panel/visualization:
*    get:
*      summary: Get panel visualization types
*      description: Get panel visualization details.
*      tags: [Panels]
*      security:
*        - bearerAuth: []
*      responses:
*        "200":
*          description: Visualization types
*          content:
*            application/json:
*              schema:
*                 $ref: '#/components/schemas/Panel'
*        "400":
*          description: Failed input validation
*          content:
*            application/json:
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*/


/**
* @swagger
* path:
*  /panel/properties:
*    get:
*      summary: Get panel properties 
*      description: Get panel properties.
*      tags: [Panels]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: query
*          name: type
*          schema:
*            type: string
*          description: visualization type
*      responses:
*        "200":
*          description: Visualization types
*          content:
*            application/json:
*              schema:
*                 $ref: '#/components/schemas/Panel'
*        "400":
*          description: Failed input validation
*          content:
*            application/json:
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*/