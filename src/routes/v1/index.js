const express = require('express');
const authRoute = require('./auth.route');
const userRoute = require('./user.route');
const roleRoute = require('./roles.route');
const reportsRoute = require('./reports.route');
const cardsRoute = require('./cards.route');
const panelsRoute = require('./panel.route');
const alertsRoute = require('./alerts.route');
const docsRoute = require('./docs.route');
const config = require('../../config/config');
const dashboardRoute = require('./dashboard.route');
const cdrDownloadRoute = require('./cdrDownload.route');
const grafanaWebhookRoute = require('./grafanaWebhook.route')
const userManagementRoute = require('./userManagement.route');
const offlineDownloadRoute = require('./offlineDownload.route');
const publicKey = require('./publicKey.route');
const router = express.Router();

const defaultRoutes = [
  {
    path: '/auth',
    route: authRoute,
  },
  {
    path: '/users',
    route: userRoute,
  },
  {
    path: '/roles',
    route: roleRoute,
  },
  {
    path: '/reports',
    route: reportsRoute,
  },
  {
    path: '/alert',
    route: alertsRoute,
  },
  {
    path: '/card',
    route: cardsRoute,
  },
  {
    path: '/panel',
    route: panelsRoute,
  },
  {
    path: '/dashboard',
    route: dashboardRoute,
  },
  {
    path: '/cdr-download',
    route: cdrDownloadRoute,
  },
  {
    path: '/grafana-webhook',
    route: grafanaWebhookRoute,
  },
  {
    path: '/usermanagement',
    route: userManagementRoute,
  },
  
  {
    path: '/offline-download',
    route: offlineDownloadRoute,
  },
  {
    path: '/public-key',
    route: publicKey,
  }
];

const devRoutes = [
  // routes available only in development mode
  {
    path: '/docs',
    route: docsRoute,
  },
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */
if (config.env === 'development') {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

module.exports = router;