const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const cdrDownloadController = require('../../controllers/cdrDownload.controller');
const { resources,rolePermissions } = require('../../config/roles');


 router = express.Router();

router.
  route('/').
  post(auth(resources.CDR_SEARCH,rolePermissions.view),validate(),cdrDownloadController.createCdrDownload)

router.
  route('/').
  get(auth(resources.CDR_SEARCH,rolePermissions.view),validate(),cdrDownloadController.getCdrDownloadData)

router.
  route('/:id').
  get(auth(resources.CDR_SEARCH,rolePermissions.view),validate(),cdrDownloadController.getDownloadById)

router.
  route('/download/:id').
  get(auth(resources.CDR_SEARCH,rolePermissions.view),validate(),cdrDownloadController.downloadCrdFile)

router.
  route('/:id').
  delete(auth(resources.CDR_SEARCH,rolePermissions.view),validate(),cdrDownloadController.deleteCdrDownload)


module.exports = router;


/**
 * @swagger
 * tags:
 *   name: CDR Downloads
 *   description: Manage CDR download files and data
 */

/**
 * @swagger
 * /cdr-download:
 *   post:
 *     summary: Create a new CDR download entry
 *     description: Create a new CDR download entry in the system.
 *     tags: [CDR Downloads]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileName:
 *                 type: string
 *                 description: Name of the file
 *               userId:
 *                 type: string
 *                 description: ID of the user creating the download
 *     responses:
 *       "200":
 *         description: Successfully created a CDR download entry
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /cdr-download:
 *   get:
 *     summary: Get CDR download data
 *     description: Fetch a list of CDR downloads, optionally filtered by search.
 *     tags: [CDR Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of records to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *     responses:
 *       "200":
 *         description: List of CDR download data
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /cdr-download/{id}:
 *   get:
 *     summary: Get CDR download by ID
 *     description: Retrieve a specific CDR download entry by its ID.
 *     tags: [CDR Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the CDR download entry
 *     responses:
 *       "200":
 *         description: Details of the specified CDR download
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /cdr-download/{id}:
 *   delete:
 *     summary: Delete CDR download by ID
 *     description: Delete a specific CDR download entry by its ID.
 *     tags: [CDR Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the CDR download entry
 *     responses:
 *       "200":
 *         description: Successfully deleted the CDR download
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /cdr-download/download/{id}:
 *   get:
 *     summary: Download CDR file by ID
 *     description: Retrieve and download a CDR file by its ID.
 *     tags: [CDR Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the CDR download entry
 *     responses:
 *       "200":
 *         description: File successfully downloaded
 *       "404":
 *         description: File not found or download failed
 *       "500":
 *         description: Server error while downloading the file
 */
