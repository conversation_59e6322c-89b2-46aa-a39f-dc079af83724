const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { dashboardValidation } = require('../../validations');
const { dashboardController } = require('../../controllers');
const { accessRights,resources,rolePermissions } = require('../../config/roles'),

 router = express.Router();


router.
    route('/').
    post(auth(resources.DASHBOARD_MANAGEMENT,rolePermissions.create),validate(dashboardValidation.createDashboard),dashboardController.createDashboard).
    get(auth(resources.DASHBOARD_MANAGEMENT,rolePermissions.view ,),validate(dashboardValidation.getDashboards),dashboardController.getDashboards);

router.
    route('/view/:id').
    get(auth(),validate(dashboardValidation.getDashboard),dashboardController.getDashboardView)

router.
    route('/:id').
    get(auth(resources.DASHBOARD_MANAGEMENT,rolePermissions.view),validate(dashboardValidation.getDashboard),dashboardController.getDashboard).
    put(auth(resources.DASHBOARD_MANAGEMENT,rolePermissions.update),validate(dashboardValidation.getDashboard),dashboardController.updateDashboard).
    delete(auth(resources.DASHBOARD_MANAGEMENT,rolePermissions.delete),validate(dashboardValidation.deleteDashboard),dashboardController.deleteDashboard);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Dashboard
 *   description: Dashboard management 
 */

/**
 * @swagger
 * path:
 *  /dashboard:
 *    post:
 *      summary: Create a dashboard
 *      description: Create a dashboard.
 *      tags: [Dashboard]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object 
 *              properties:
 *                name:
 *                  type: string
 *                panels: 
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      id: 
 *                        type: string
 *                      order : 
 *                        type: integer
 *                cards:
 *                  type: array
 *                  items:  
 *                    type: object
 *                    properties:
 *                      id: 
 *                        type: string
 *                      order : 
 *                        type: integer 

 *              example:
 *                "name": new_dashbiard
 *                "panels" : [
 *            {
 *           "id" : "5345345435",
 *           "order" : 1
 *           }
 *         ]
 *      responses:
 *        "201":
 *          description: Created
 
 *        "400":
 *          description: Failed input validation
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all dashboards
 *      description: Retrieve all dashboards.
 *      tags: [Dashboard]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: dashboard search
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of dashboard
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *        - in: query
 *          name: createdBy
 *          schema:
 *            type: string
 *          description: ID of user 
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/Dashboard'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */


/**
* @swagger
* path:
*  /dashboard/{id}:
*    get:
*      summary: Get a dashboard
*      description: Get dashboard details.
*      tags: [Dashboard]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: dashboard id
 
*      responses:
*        "200":
*          description: Fetched data 
*          content:
*            application/json:
*              schema:
*                 $ref: '#/components/schemas/Dashboard'
*        "400":
 *          description: Failed input validation
 *          content:
 *            application/json:
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*
*    put:
*      summary: Update a dashboard
*      description: Update dashboard details.
*      tags: [Dashboard]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: dashboard id
*      requestBody:
*        required: true
*        content:
*          application/json:
*            schema:
*              type: object               
*              properties:
*                name:
*                  type: string
*                panels: 
*                  type: array
*                  items:  
*                    type: object
*                    properties:
*                      id: 
*                        type: string
*                      order : 
*                        type: integer
*                cards:
*                  type: array
*                  items:  
*                    type: object
*                    properties:
*                      id: 
*                        type: string
*                      order : 
*                        type: integer 
*      
*      responses:
*        "200":
*          description: OK
*          content:
*            application/json:
*              schema:
*                type: object
*                properties:
*                  results:
*                    type: array
*                    items:
*                      $ref: '#/components/schemas/Dashboard'
*                  page:
*                    type: integer
*                    example: 1
*                  limit:
*                    type: integer
*                    example: 10
*                  totalPages:
*                    type: integer
*                    example: 1
*                  totalResults:
*                    type: integer
*                    example: 1
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
* 
*    delete:
*      summary: Delete a dashboard
*      description: Delete a dashboard.
*      tags: [Dashboard]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: Dashboard id
*      responses:
*        "200":
*          description: No content
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*        "404":
*          $ref: '#/components/responses/NotFound'
*/


/**
* @swagger
* path:
*  /dashboard/view/{id}:
*    get:
*      summary: Get a dashboard view
*      description: Get dashboard details.
*      tags: [Dashboard]
*      security:
*        - bearerAuth: []
*      parameters:
*        - in: path
*          name: id
*          required: true
*          schema:
*            type: string
*          description: dashboard id
 
*      responses:
*        "200":
*          description: Fetched data 
*          content:
*            application/json:
*              schema:
*                 $ref: '#/components/schemas/Dashboard'
*        "400":
 *          description: Failed input validation
 *          content:
 *            application/json:
*        "401":
*          $ref: '#/components/responses/Unauthorized'
*        "403":
*          $ref: '#/components/responses/Forbidden'
*/