const express = require('express');
const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const offlineDownloadController = require('../../controllers/offlineDownload.controller');
const { resources,rolePermissions } = require('../../config/roles');


const router = express.Router();

router.
  route('/').
  post(auth(),validate(),offlineDownloadController.createOfflineDownload)

router.
  route('/').
  get(auth(),validate(),offlineDownloadController.getOfflineDownloadData)

router.
  route('/:id').
  get(auth(),validate(),offlineDownloadController.getDownloadById)

router.
  route('/download/:id').
  get(auth(),validate(),offlineDownloadController.downloadFile)

router.
  route('/:id').
  delete(auth(),validate(),offlineDownloadController.deleteOfflineDownload)


module.exports = router;


/**
 * @swagger
 * tags:
 *   name: Offline Downloads
 *   description: Manage Offline download files and data
 */

/**
 * @swagger
 * /offline-download:
 *   post:
 *     summary: Create a new Offline download entry
 *     description: Create a new Offline download entry in the system.
 *     tags: [Offline Downloads]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fileName:
 *                 type: string
 *                 description: Name of the file
 *               userId:
 *                 type: string
 *                 description: ID of the user creating the download
 *     responses:
 *       "200":
 *         description: Successfully created a Offline download entry
 *       "400":
 *         $ref: '#/components/responses/BadRequest'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /offline-download:
 *   get:
 *     summary: Get Offline download data
 *     description: Fetch a list of Offline downloads, optionally filtered by search.
 *     tags: [Offline Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of records to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *     responses:
 *       "200":
 *         description: List of Offline download data
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 *       "403":
 *         $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * /offline-download/{id}:
 *   get:
 *     summary: Get Offline download by ID
 *     description: Retrieve a specific Offline download entry by its ID.
 *     tags: [Offline Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the Offline download entry
 *     responses:
 *       "200":
 *         description: Details of the specified Offline download
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /offline-download/{id}:
 *   delete:
 *     summary: Delete Offline download by ID
 *     description: Delete a specific Offline download entry by its ID.
 *     tags: [Offline Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the Offline download entry
 *     responses:
 *       "200":
 *         description: Successfully deleted the Offline download
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /offline-download/download/{id}:
 *   get:
 *     summary: Download Offline file by ID
 *     description: Retrieve and download a Offline file by its ID.
 *     tags: [Offline Downloads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the Offline download entry
 *     responses:
 *       "200":
 *         description: File successfully downloaded
 *       "404":
 *         description: File not found or download failed
 *       "500":
 *         description: Server error while downloading the file
 */
