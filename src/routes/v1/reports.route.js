const express = require("express");
const auth = require("../../middlewares/auth");
const validate = require("../../middlewares/validate");
const { reportsValidation, userPreferenceValidation} = require("../../validations");
const reportsController = require("../../controllers/reports.controller");
const {
    accessRights,
    resources,
    rolePermissions,
  } = require("../../config/roles"),
  router = express.Router();

router
  .route("/search-fields")
  .get(
    auth(),
    validate(reportsValidation.getFilterFields),
    reportsController.getFilterFields
  );

router
  .route("/classify")
  .get(
    auth(resources.REPORT_MANAGEMENT, rolePermissions.view),
    validate(),
    reportsController.getReportTypes
  );

router
  .route("/static")
  .post(
    auth(resources.REPORT_MANAGEMENT, rolePermissions.view),
    validate(),
    reportsController.getStaticReports
  );

router.route("/auto").get(validate(), reportsController.sendAutoReportsEmail);

router
  .route("/customers")
  .get(auth(), validate(), reportsController.getCustomerList);

router
  .route("/suppliers")
  .get(auth(), validate(), reportsController.getSupplierList);

router
  .route("/destination")
  .get(auth(), validate(), reportsController.getDestinationDetails);

router.route("/lcr").get(auth(), validate(), reportsController.getLcrDetails);

router
  .route("/cdr-status")
  .get(auth(), validate(), reportsController.getcdrStatus);

router
  .route("/destination-prime")
  .get(auth(), validate(), reportsController.getDestinationPrimeDetails);

router
  .route("/source-prime")
  .get(auth(), validate(), reportsController.getSourcePrimeDetails);

router
  .route("/derived-fields")
  .get(auth(), validate(), reportsController.getDerivedFieldsList);

router
  .route("/raw-cdr")
  .post(auth(), validate(), reportsController.getrawcdrsData);

router
  .route("/network-reports")
  .post(validate(), reportsController.downloadNetworkReports);

router
  .route("/billing-reports")
  .post(validate(), reportsController.downloadBillingReport);

router
  .route("/fy-reports")
  .post(validate(), reportsController.downloadFYreports);

router
  .route("/selected-columns")
  .post(auth(), 
  // validate(userPreferenceValidation.createSelectedColumns),
   reportsController.createSelectedColumns)
  .get(auth(), validate(userPreferenceValidation.getSelectedColumns), reportsController.getSelectedColumns);


  // HERE
router
  .route("/non-aggregate-fields")
  .get(auth(), validate(), reportsController.getNonAggregateFields);



module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Reports
 *   description: Reports
 */
/**
 * @swagger
 * path:
 * 
 *  /reports/classify:
 *    get:
 *      summary: Get report types
 *      description: Fetch all report types.
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: type
 *          schema:
 *            type: string
 *            enum: [REPORT_TYPE, REPORT_SUB_TYPE]
 *          description: Report type
 *          

 *        - in: query
 *          name: subtype
 *          schema:
 *            type: string
 *          description: Report sub type
 
 *      responses:
 *        "200":
 *          description: Report data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 * 
 *  /reports/static:
 *    post:
 *      summary: Get static report
 *      description: Fetch static report.
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 *      consumes:
 *      - "multipart/form-data"
 *      produces:
 *      - "application/json"
  *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - reportName
 *               
 *                
 *              properties:
 *                reportName:
 *                  type: string
 *                download:
 *                  type: boolean
 *                type:
 *                  type: string   
 *                sendEmail:
 *                  type: boolean
 *                mailList:
 *                  type: array
 *                  items:  
 *                    type: string
 *                groupList:
 *                  type: array
 *                  items:  
 *                    type: string
 *                limit:
 *                  type: integer              
 *                page:
 *                  type: integer   
 *                startDate:
 *                  type: string              
 *                endDate:
 *                  type: string   
 *                search:
 *                  type: string              
 
 *      responses:
 *        "200":
 *          description: Report data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 * 
 *  /reports/customers:
 *    get:
 *      summary: Get customer list
 *      description: Fetch all customer list .
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []

 *      responses:
 *        "200":
 *          description: customer list data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 * 
 *  /reports/suppliers:
 *    get:
 *      summary: Get suppliers list
 *      description: Fetch all suppliers list .
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []

 *      responses:
 *        "200":
 *          description: supplier list data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 * 
 *  /reports/derived-fields:
 *    get:
 *      summary: Get all derived field list
 *      description: Fetch all aggregations/derived fields list .
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 
 *      responses:
 *        "200":
 *          description: derived field list data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":  
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 *
 *  /reports/destination:
 *    get:
 *      summary: Get destination details
 *      description: Fetch all destination details .
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: type
 *          schema:
 *            type: string
 *            enum: [country, operator]
 *      responses:
 *        "200":
 *          description: destination list data
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 * 
 *  /reports/auto:
 *    get:
 *      summary: Send auto emails
 *      description: Send reports to admin via email
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: type
 *          schema:
 *            type: string
 *            enum: [day, hour]
 *          description: Report type

 *      responses:
 *        "200":
 *          description: Email sent
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * path:
 *
 *  /reports/raw-cdr:
 *    post:
 *      summary: Get raw CDRs
 *      description: Fetch raw CDR data or initiate a download for offline access.
 *      tags: [Reports]
 *      security:
 *        - bearerAuth: []
 *      produces:
 *        - "application/json"
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                filters:
 *                  type: array
 *                  items:
 *                    type: object
 *                    properties:
 *                      field:
 *                        type: string
 *                      value:
 *                        type: string
 *                download:
 *                  type: boolean
 *                  description: Set to `true` to initiate an offline download.
 *                type:
 *                  type: string
 *                limit:
 *                  type: integer
 *                page:
 *                  type: integer
 *                startDate:
 *                  type: string
 *                  format: date-time
 *                endDate:
 *                  type: string
 *                  format: date-time
 *      responses:
 *        "200":
 *          description: Successfully processed the request.
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  message:
 *                    type: string
 *                    description: Message indicating the status or outcome of the request.
 *                    example: "Your offline download is initiated, Please view the status of your download in CDR search download menu"
 *        "400":
 *          description: Bad request, validation error.
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */
