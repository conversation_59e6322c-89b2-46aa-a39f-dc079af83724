const express = require("express");
const auth = require("../../middlewares/auth");
const validate = require("../../middlewares/validate");
const alertsValidation = require("../../validations/alerts.validation");
const alertsController = require("../../controllers/alerts.controller");
const alertsNotificationHistoryController = require("../../controllers/alertsNotificationHistory.controller");
const {
    accessRights,
    resources,
    rolePermissions,
  } = require("../../config/roles"),
  router = express.Router();
const { ALERT_MANAGEMENT } = resources;
const {
  createAlertValidation,
  updateAlertValidation,
  updateAlertHistoryValidation,
} = alertsValidation;

router
  .route("/group")
  .post(
    auth(resources.GROUP_MANAGEMENT, rolePermissions.view),
    validate(alertsValidation.createGroup),
    alertsController.createGroup
  )
  .get(
    auth(resources.GROUP_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getGroups
  );

router
  .route("/group/:id")
  .get(
    auth(resources.GROUP_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getGroup
  )
  .put(
    auth(resources.GROUP_MANAGEMENT, rolePermissions.view),
    validate(alertsValidation.updateGroup),
    alertsController.updateGroup
  )
  .delete(
    auth(resources.GROUP_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.deleteGroup
  );

router
  .route("/metadata/:keys")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertsMetadata
  );

router
  .route("/history")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    // alertsController.getAlertHistory
    alertsNotificationHistoryController.getAlertsNotificationHistory
  )
  .put(
    auth(ALERT_MANAGEMENT, rolePermissions.update),
    validate(updateAlertHistoryValidation),
    // alertsController.updateAlertHistories
    alertsNotificationHistoryController.updateAlertNotificationHistories
  );
router
  .route("/history/alert-name-list")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertNameList
  );

router
  .route("/history/alert-names")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertNames
  );
router
  .route("/history/:alertId")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertHistoryById
  );
router
  .route("/history/view/:alertHistoryId")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    // alertsController.viewAlertHistoryById
    alertsNotificationHistoryController.viewAlertsNotificationById
  );

router
  .route("/notifications")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertNotifications
  );

router
  .route("/err-des-mapping/err-description")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getErrorDescriptionList
  );
router
  .route("/err-des-mapping/result-code")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getResultCodeList
  );
router
  .route("/node-list")
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    alertsController.getNodeList
  );

router
  .route("/")
  .post(
    auth(ALERT_MANAGEMENT, rolePermissions.create),
    validate(createAlertValidation),
    alertsController.createAlert
  )
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlerts
  );

router
  .route("/:id")
  .delete(
    auth(ALERT_MANAGEMENT, rolePermissions.delete),
    validate(),
    alertsController.deleteAlert
  )
  .get(
    auth(ALERT_MANAGEMENT, rolePermissions.view),
    validate(),
    alertsController.getAlertById
  )
  .put(
    auth(ALERT_MANAGEMENT, rolePermissions.update),
    validate(updateAlertValidation),
    alertsController.updateAlert
  );

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Groups
 *   description: Email group Management and retrieval
 */

/**
 * @swagger
 * path:
 * 
 *  /alert/group:
 *    post:
 *      summary: Create a group
 *      description: Creating group.
 *      tags: [Groups]
 *      security:
 *        - bearerAuth: []
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - members
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                members: 
 *                  type: array
 *                  items: 
 *                      type: string
 *                isSubGroup: 
 *                  type: boolean
 *                  description: Sub group or not
 *                subGroups: 
 *                  type: array
 *                  items: 
 *                      type: string
 *                   
 *              example:
 *                name: Group1
 *                description: test group
 *                members: ["<EMAIL>","<EMAIL>"]
 *                isSubGroup : true
 *                subGroups: ["234243235","54534534"]
 
 *      responses:
 *        "201":
 *          description: Created
 *          content:
 *            application/json:
 
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *
 *    get:
 *      summary: Get all groups
 *      description: List all groups
 *      tags: [Groups]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: name
 *          schema:
 *            type: string
 *          description: Group name
 *        - in: query
 *          name: search
 *          schema:
 *            type: string
 *          description: search by user name or role
 *        - in: query
 *          name: sortBy
 *          schema:
 *            type: string
 *          description: sort by query in the form of field:desc/asc (ex. name:asc)
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of groups
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *        - in: query
 *          name: subGroup
 *          schema:
 *            type: boolean
 *          description: list only sub groups
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  results:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/User'
 *                  page:
 *                    type: integer
 *                    example: 1
 *                  limit:
 *                    type: integer
 *                    example: 10
 *                  totalPages:
 *                    type: integer
 *                    example: 1
 *                  totalResults:
 *                    type: integer
 *                    example: 1
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 */

/**
 * @swagger
 * path:
 *  /alert/group/{id}:
 *    get:
 *      summary: Get a group
 *      description: Get group details
 *      tags: [Groups]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: group id
 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    put:
 *      summary: Update a group
 *      description: Update group
 *      tags: [Groups]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Group id
 *      requestBody:
 *        required: true
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              required:
 *                - name
 *                - members
 *                
 *              properties:
 *                name:
 *                  type: string
 *                description:
 *                  type: string
 *                members: 
 *                  type: array
 *                  items: 
 *                      type: string
 *                subGroups: 
 *                  type: array
 *                  items: 
 *                      type: string
 *                   
 *              example:
 *                name: Group1
 *                description: test group
 *                members: ["<EMAIL>","<EMAIL>"]

 *      responses:
 *        "200":
 *          description: OK
 *          content:
 *            application/json:
 *              schema:
 *                 $ref: '#/components/schemas/User'
 *        "400":
 *          $ref: '#/components/responses/DuplicateEmail'
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 *
 *    delete:
 *      summary: Delete  group
 *      description: Delete group
 *      tags: [Groups]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: Group id
 *      responses:
 *        "200":
 *          description: No content
 *        "401":
 *          $ref: '#/components/responses/Unauthorized'
 *        "403":
 *          $ref: '#/components/responses/Forbidden'
 *        "404":
 *          $ref: '#/components/responses/NotFound'
 */
