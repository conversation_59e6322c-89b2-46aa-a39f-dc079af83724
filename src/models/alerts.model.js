const { alertConfig } = require("../config/alertConfig");

module.exports = (sequelize, DataTypes) => {
  const Alerts = sequelize.define(
    "alerts",
    {
      id: {
        type: DataTypes.BIGINT(20),
        allowNull: false,
        primaryKey: true,
      },
      // uid: {
      //   type: DataTypes.STRING(255),
      //   allowNull: false,
      //   primaryKey: true,
      // },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      alert_timezone: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      evaluation_timeframe: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
      },
      run_every: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
      },
      filters: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      contact_point: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      compared_timediff: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
      },
      compared_timerange: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
      },
      triggering_expression: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
      created_by: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
        onUpdate: DataTypes.NOW,
      },
      updated_by: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false,
        values: [
          alertConfig.categoryTypes.CRITICAL,
          alertConfig.categoryTypes.MAJOR,
          alertConfig.categoryTypes.MINOR,
        ],
      },
      status: {
        type: DataTypes.STRING(10),
        allowNull: false,
        values: [
          alertConfig.alertStatus.ACTIVE,
          alertConfig.alertStatus.INACTIVE,
        ],
      },
    },
    {
      timestamps: false,
      //   tableName: 'alerts',
      //   timestamps: false,  // since you have custom `created_at` and `updated_at`
      //   underscored: true,  // Use snake_case for column names in the database
    }
  );

  return Alerts;
};
