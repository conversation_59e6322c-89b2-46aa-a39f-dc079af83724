const bcrypt = require('bcryptjs');
const { roles } = require('../config/roles');

const Card = (sequelize, DataTypes) => {
  const card = sequelize.define(
    'card',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(256),
        allowNull: false,
        unique: true,
        validate: {
          notEmpty: true,
          len: {
            args: [1, 256],
            msg: "Name should be max length 256"
          }
        },
      },
      reportField: {
        type: DataTypes.STRING,
        allowNull: false
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false
      },
      filterFlag: {
        type: DataTypes.STRING(64),
        allowNull: true
      }
    },
    {
      indexes: [
        {
          fields: ['name', 'createdBy'],
          unique: true,
        }
      ]
    },

    {
      timestamps: true,
    }
  );


  return card;
};

module.exports = Card;