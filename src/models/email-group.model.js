const bcrypt = require('bcryptjs');
const { roles } = require('../config/roles'),

  EmailGroup = (sequelize, DataTypes) => {
    const emailGroup = sequelize.define(
      'email_group',
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(256),
          allowNull: false,
          validate: {
            notEmpty: true,
            len: {
              args: [
                2,
                256
              ],
              msg: "Name should be max length 256"
            }
          },
        },
        description: {
          type: DataTypes.STRING(256),
          allowNull: true
        },
        members: {
          type: DataTypes.JSON
        },
        subGroups: {
          type: DataTypes.JSON,
          defaultValue : []
        },
        isSubGroup : {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false
        }
      },
      {
        indexes: [
          {
            fields: [
              'name',
              'createdBy'
            ],
            unique: true,
          }
        ]
      },
    
    {
        timestamps: true,
      }
    );


    return emailGroup;
  };

module.exports = EmailGroup;