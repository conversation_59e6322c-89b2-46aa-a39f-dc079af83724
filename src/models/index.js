const Sequelize = require('sequelize');
const pg = require('pg');
const config = require('../config/config');
const user = require('./user.model');
const role = require('./role.model');
const token = require('./token.model');
const audit_log = require('./auditlog.model');
const alerts = require('./alerts.model');
const alertsHistory = require('./alertsHistory.model');
const email_group = require('./email-group.model');
const card = require('./cards.model');
const panel = require('./panel.model');
const dashboard = require('./dashboard.model');
const cdr_download = require('./cdrDownload.model');
const offline_download = require('./offlineDownload.model');
const { paginate } = require("./plugins");
const UserReportPreferences = require('./userReportPreferences.model');
const alertNotificationLog = require('./alertNotificationLog.model');

var sequelize;
if (config.dbToBeUsed == "mysql") {
  //connecting to  mysql
  sequelize = new Sequelize(config.mysql_config.DB, config.mysql_config.USER, config.mysql_config.PASSWORD, {
    host: config.mysql_config.HOST,
    port: config.mysql_config.PORT,
    dialect: config.mysql_config.dialect,
    logging: false, // Disable query logging
    operatorsAliases: 0,

    pool: {
      max: config.mysql_config.pool.max,
      min: config.mysql_config.pool.min,
      acquire: config.mysql_config.pool.acquire,
      idle: config.mysql_config.pool.idle
    }
  });

  // sequelize
  //   .authenticate()
  //   .then(() => console.log('Connection successful'))
  //   .catch((err) => {
  //     console.error('Unable to connect:', err);
  //     console.log(config.mysql_config);
  //   });
}

else if (config.dbToBeUsed == "postgres") {
  sequelize = new Sequelize(config.postgres.url, {
    dialect: 'postgres',
    ssl: false,
  });
}

const models = {
  User: user(sequelize, Sequelize),
  cdr_download: cdr_download(sequelize, Sequelize),
  offline_download: offline_download(sequelize, Sequelize),
  Token: token(sequelize, Sequelize),
  Role: role(sequelize, Sequelize),
  Audit_log: audit_log(sequelize, Sequelize),
  Email_group: email_group(sequelize, Sequelize),
  Card: card(sequelize, Sequelize),
  Panel: panel(sequelize, Sequelize),
  Dashboard: dashboard(sequelize, Sequelize),
  AlertsModel: alerts(sequelize, Sequelize),
  AlertsHistoryModel: alertsHistory(sequelize, Sequelize),
  UserReportPreferences: UserReportPreferences(sequelize, Sequelize),
  AlertNotificationLog: alertNotificationLog(sequelize, Sequelize),
};

models.Token.belongsTo(models.User);

models.User.belongsTo(models.Role, { foreignKey: 'roleId' })
models.Role.hasMany(models.User, { foreignKey: 'roleId' });

models.User.hasMany(models.cdr_download, { foreignKey: 'userId' }); // User has many cdr_downloads
models.cdr_download.belongsTo(models.User, { foreignKey: 'userId' }); // cdr_download belongs to User

paginate(models.cdr_download)

models.User.hasMany(models.offline_download, { foreignKey: 'userId' }); // User has many offline_downloads
models.offline_download.belongsTo(models.User, { foreignKey: 'userId' }); // offline_download belongs to User

paginate(models.offline_download)

// User association
models.User.hasMany(models.UserReportPreferences, {
  foreignKey: 'userId',
  onDelete: 'CASCADE',
});
models.UserReportPreferences.belongsTo(models.User, {
  foreignKey: 'userId',
  onDelete: 'CASCADE',
});

// Panel association
models.Panel.hasMany(models.UserReportPreferences, {
  foreignKey: 'reportId',
  onDelete: 'CASCADE',
});
models.UserReportPreferences.belongsTo(models.Panel, {
  foreignKey: 'reportId',
  onDelete: 'CASCADE',
});


Object.keys(models).forEach((key) => {
  if ('associate' in models[key]) {
    models[key].associate(models);
  }
});

module.exports = { sequelize, models };
