const Panel = (sequelize, DataTypes) => {
  const panel = sequelize.define(
    'panel',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(256),

        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      visualizationType: {
        type: DataTypes.STRING(64),
        allowNull: false
      },
      dynamicReport: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false
      },
      filters: {
        type: DataTypes.JSON
      },
      dataColumns: {
        type: DataTypes.JSON
      },
    
      timePeriod: {
        type: DataTypes.STRING
      },
      interval : {
        type: DataTypes.STRING	
       
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false
      },
      filterFlag: {
        type: DataTypes.STRING(64),
        allowNull: true
      }
    },
    {
      indexes: [
        {
          fields: ['name', 'createdBy'],
          unique: true,
        }
      ]
    },
    {
      timestamps: true,
    }
  );


  return panel;
};

module.exports = Panel;