const bcrypt = require('bcryptjs');
const rolesConfig = require('../config/roles'),

  User = (sequelize, DataTypes) => {
    const user = sequelize.define(
      'user',
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },

        name: {
          type: DataTypes.STRING(256),
          unique: false,
          allowNull: false,
          validate: {
            notEmpty: true,
            len: {
              args: [
                2,
                256
              ],
              msg: "Name should be max length 256"
            }
          },
        },
        email: {
          type: DataTypes.STRING(256),
          unique: true,
          allowNull: false,
          validate: {
            notEmpty: true,
            isEmail: true,
          },
        },
        isSuperAdmin: {
          type: DataTypes.BOOLEAN,
          defaultValue: false
        },
        countryCode: {
          type: DataTypes.STRING(10),
          validate: {
            len: {
              args: [
                2,
                8
              ],
              msg: "Country code should be of min length 2 and max 8"
            }
          }
        },
        phoneNumber: {
          type: DataTypes.STRING(20),
          validate: {
            len: {
              args: [
                7,
                15
              ],
              msg: "Phone number should be of min length 7 and max 15"
            }
          }
        },
        defaultDashboard: {
          type: DataTypes.STRING
        },
        isEmailVerified: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        password: {
          type: DataTypes.STRING,
          allowNull: true
        },
        oldPasswords: {
          type: DataTypes.JSON,
          allowNull: true
        },
        profileImage: {
          type: DataTypes.STRING,
          allowNull: true
        },
        type: {
          type: DataTypes.ENUM,
          values: [
            rolesConfig.userTypes.CUSTOMER,
            rolesConfig.userTypes.SUPPLIER,
            rolesConfig.userTypes.BOTH
          ],
          defaultValue: rolesConfig.userTypes.BOTH
        },
        customerList: {
          type: DataTypes.JSON
        },
        supplierList: {
          type: DataTypes.JSON
        },
        createdBy: {
          type: DataTypes.UUID
        },
        isUserActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
        isLdapUser: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        allCustomerList: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        allSupplierList: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        lastLoggedin: {
          type: DataTypes.DATE(6)
        },
        reportMasking: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
      },
      {
        timestamps: true,
      }
    );

    /**
     * check if email is taken
     * @param {string} email - The user's email
     * @returns {Promise<boolean>}
     */
    user.isEmailTaken = async function (email) {
      const userFound = await this.findOne({
        where: {
          email,
        },
      });
      return Boolean(userFound);
    };

    user.beforeCreate(async (user) => {
      if (user.password) {
        user.password = await user.generatePasswordHash();
      }
    });
    /*
      user.beforeUpdate(async (user) => {
        user.password = await user.generatePasswordHash();
      });*/

    user.prototype.generatePasswordHash = async function (password = null) {
      const saltRounds = 10;
      if (password)
        return await bcrypt.hash(password, saltRounds);
      else
        return await bcrypt.hash(this.password, saltRounds);
    };

    /**
     * check if password matches the user's password
     * @param {string} password
     * @returns {Promise<boolean>}
     */
    user.prototype.isPasswordMatch = async function (password) {
      if (this.password) {
        return await bcrypt.compare(password, this.password);
      }

      return false;
    };

    return user;
  };

module.exports = User;