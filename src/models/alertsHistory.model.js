const { alertConfig } = require("../config/alertConfig");

module.exports = (sequelize, DataTypes) => {
  const AlertsHistory = sequelize.define(
    "alerts_histories",
    {
      id: {
        type: DataTypes.BIGINT(20),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true, // Enable auto-increment
      },
      alert_id: {
        type: DataTypes.BIGINT(20),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      timestamp: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING(20),
        allowNull: true,
        values: [
          alertConfig.alertsHistoryStatus.ANALYZING,
          alertConfig.alertsHistoryStatus.OPEN,
          alertConfig.alertsHistoryStatus.CLOSED,
          alertConfig.alertsHistoryStatus.FIXED,
          alertConfig.alertsHistoryStatus.DELETED,
        ],
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      alert_type: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      // created_at: {
      //   type: DataTypes.DATE,
      //   defaultValue: DataTypes.NOW,
      //   allowNull: false,
      // },
      // created_by: {
      //   type: DataTypes.STRING(255),
      //   allowNull: false,
      // },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
        onUpdate: DataTypes.NOW,
      },
      updated_by: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: false,
      //   tableName: 'alerts',
      //   timestamps: false,  // since you have custom `created_at` and `updated_at`
      //   underscored: true,  // Use snake_case for column names in the database
    }
  );

  return AlertsHistory;
};
