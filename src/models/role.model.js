const bcrypt = require("bcryptjs");
const { roles } = require("../config/roles"),
  Role = (sequelize, DataTypes) => {
    const role = sequelize.define(
      "role",
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(256),
          allowNull: false,
          validate: {
            notEmpty: true,
            len: {
              args: [2, 256],
              msg: "Name should be max length 256",
            },
          },
        },
        description: {
          type: DataTypes.STRING(256),
          allowNull: true,
        },
        resources: {
          type: DataTypes.JSON,
        },
        staticReports: {
          type: DataTypes.JSON,
        },
        dynamicReports: {
          type: DataTypes.JSON,
        },
        cards: {
          type: DataTypes.JSON,
        },
        dynamicDashboard: {
          type: DataTypes.JSON,
        },
        dashboardCount: {
          type: DataTypes.SMALLINT.UNSIGNED,
          defaultValue: 0,
        },
        panelCount: {
          type: DataTypes.SMALLINT.UNSIGNED,
          defaultValue: 0,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        isSuperAdminRole: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
        },
        status: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
        },
      },
      {
        indexes: [
          {
            fields: ["name", "createdBy"],
            unique: true,
          },
        ],
      },
      {
        timestamps: true,
      }
    );

    return role;
  };

module.exports = Role;
