const { DATE } = require("sequelize"),

 Dashboard = (sequelize,DataTypes) => {
  const dashboard = sequelize.define(
    'dashboard',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(256),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      panels: {
        type: DataTypes.JSON
      },
      cards: {
        type: DataTypes.JSON
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false
      }
    },
    {
      indexes: [
        {
          fields: [
'name',
'createdBy'
],
          unique: true,
        }
      ]
    },
    {
      timestamps: true,
    }
  );


  return dashboard;
};

module.exports = Dashboard;