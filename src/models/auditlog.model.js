const bcrypt = require("bcryptjs");
const { roles } = require("../config/roles"),
  Auditlog = (sequelize, DataTypes) => {
    const auditlog = sequelize.define(
      "auditlog",
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        username: {
          type: DataTypes.STRING(256),
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        roleName: {
          type: DataTypes.STRING(256),
        },
        event: {
          type: DataTypes.STRING,
        },
        action: {
          type: DataTypes.STRING(300),
        },
        details: {
          type: DataTypes.JSON(),
        },
      },

      {
        timestamps: true,
      }
    );

    return auditlog;
  };

module.exports = Auditlog;
