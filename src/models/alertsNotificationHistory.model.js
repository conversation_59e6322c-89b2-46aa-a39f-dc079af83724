const { alertConfig } = require("../config/alertConfig");

module.exports = (sequelize, DataTypes) => {
  const AlertsHistory = sequelize.define(
    "alerts_notification_histories",
    {
      id: {
        type: DataTypes.BIGINT(20),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true, // Enable auto-increment
      },
      alert_id: {
        type: DataTypes.BIGINT(20),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      startsAt: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      every_run_minute: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
      },
      interval_start: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      interval_end: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING(20),
        allowNull: true,
        values: [
          alertConfig.alertsHistoryStatus.ANALYZING,
          alertConfig.alertsHistoryStatus.OPEN,
          alertConfig.alertsHistoryStatus.CLOSED,
          alertConfig.alertsHistoryStatus.FIXED,
          alertConfig.alertsHistoryStatus.DELETED,
        ],
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      alert_type: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      notification_payload: {
        type: DataTypes.JSON,
        allowNull: true,
      }
    },
    {
      timestamps: true,
    }
  );

  return AlertsHistory;
};
