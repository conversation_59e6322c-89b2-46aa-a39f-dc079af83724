
const CdrDownload = (sequelize, DataTypes) => {
  const cdrDownload = sequelize.define(
    'cdr_download',
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      initiatedBy: {
        type: DataTypes.STRING(256),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      fileName: {
        type: DataTypes.STRING(256),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      filePath: {
        type: DataTypes.STRING(256),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      status: {
        type: DataTypes.STRING(256),
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
    },
    {
      timestamps: true
    }
  )

  return cdrDownload
};


module.exports = CdrDownload