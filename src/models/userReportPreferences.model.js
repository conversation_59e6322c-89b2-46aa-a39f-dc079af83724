const UserReportPreferences = (sequelize, DataTypes) => {
  const UserReportPreferences = sequelize.define("UserReportPreferences", {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    reportType: {
      type: DataTypes.ENUM('static', 'dynamic'),
      allowNull: false,
    },
    reportName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    selectAll : {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    reportId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Panels',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    selectedColumns: {
      type: DataTypes.JSON,
      allowNull: false,
    },
  }, {
    indexes: [
      {
        unique: true,
        fields: ['userId', 'reportType', 'reportName'],
      }
    ],
    timestamps: true,
    tableName: 'UserReportPreferences',
  });

  return UserReportPreferences;
};


module.exports = UserReportPreferences;