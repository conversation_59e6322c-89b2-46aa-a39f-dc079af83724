const dayjs = require("dayjs");
const config = require("./config");

const monthMap = {
  january: 0,
  february: 1,
  march: 2,
  april: 3,
  may: 4,
  june: 5,
  july: 6,
  august: 7,
  september: 8,
  october: 9,
  november: 10,
  december: 11,
};

const weekMap = {
  sunday: 0,
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
};

// Month setup
const fyrStartMonth = monthMap[config.START_OF_YEAR.toLowerCase()];
const fyrEndMonth = monthMap[config.END_OF_YEAR.toLowerCase()];

const baseYear = dayjs().year();
const shouldAddYear = fyrEndMonth < fyrStartMonth;

const FYR_START_DATE = dayjs()
  .year(baseYear)
  .set("month", fyrStartMonth)
  .set("date", 1)
  .startOf("day")
  .format("YYYY-MM-DD 00:00:00");

const endYear = shouldAddYear ? baseYear + 1 : baseYear;
const FYR_END_DATE = dayjs()
  .year(endYear)
  .set("month", fyrEndMonth)
  .set("date", dayjs().year(endYear).set("month", fyrEndMonth).daysInMonth())
  .endOf("day")
  .format("YYYY-MM-DD 00:00:00");

// Week setup
// const startOfWeekDay = weekMap[config.START_OF_WEAK.toLowerCase()];
// const endOfWeekDay = weekMap[config.END_OF_WEAK.toLowerCase()];

// const today = dayjs();
// const currentDay = today.day();

// const daysToStart = (currentDay - startOfWeekDay + 7) % 7;
// const daysToEnd = (endOfWeekDay - currentDay + 7) % 7;

// const WEEK_START_DATE = today
//   .subtract(daysToStart, "day")
//   .startOf("day")
//   .format("YYYY-MM-DD 00:00:00");

// const WEEK_END_DATE = today
//   .add(daysToEnd, "day")
//   .endOf("day")
//   .format("YYYY-MM-DD 00:00:00");

module.exports = {
  FYR_START_DATE,
  FYR_END_DATE,
  // WEEK_START_DATE,
  // WEEK_END_DATE,
};
