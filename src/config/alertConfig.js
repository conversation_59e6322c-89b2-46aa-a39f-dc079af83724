const createAlertConfig = {
    name: "alertCreate",
    responseFields: {
      compared_to: "comparedTo",
      contact_point: "contactPoint",
      evaluation_timeframe: "evaluationTimeframe",
      fields: "fields",
      name: "name",
      run_every: "runEvery",
    },
  },
  getAlertsConfig = {
    name: "alertsGet",
  },
  deleteAlertConfig = {
    name: "alertDelete",
  };
const createContactPointConfig = {
    name: "createContactPoint",
  },
  getContactPointsConfig = {
    name: "getContactPoints",
  };
const getErrDesMappingConfig = {
  name: "getErrorDescriptionMappingDetails",
  reqFields: {
    // actual filed : query filter field
    failureError: "failureError",
    errorCode: "errorCode",
    errorDescription: "errorDescription",
    failureResult: "failureResult",
  },
};

const alertTypes = {
    DELIVERY_DROP: "Delivery Drop",
    ERROR: "Error",
    VOLUME_DROP_OR_SPIKE: "Volume Drop or Spike",
    DELIVERY_REPORT_DROP: "Delivery Report Drop",
  },
  alertStatus = {
    ACTIVE: "active",
    INACTIVE: "inactive",
  },
  alertsHistoryStatus = {
    OPEN: "open",
    ANALYZING: "analyzing",
    FIXED: "fixed",
    CLOSED: "closed",
    DELETED: "deleted",
  };

const alertParameters = ["customer", "supplier", "destination"];

const alertFilterOptions = {
  [alertTypes.DELIVERY_DROP]: [
    "Destination",
    "Supplier",
    "Customer",
    "Supplier Destination",
    "Customer Destination",
    "Customer-Supplier Destination",
  ],
  [alertTypes.ERROR]: [
    "Destination",
    "Supplier",
    "Customer",
    "Supplier Destination",
    "Customer Destination",
    "Customer-Supplier Destination",
  ],
  [alertTypes.VOLUME_DROP_OR_SPIKE]: [
    "Destination",
    "Customer",
    "Customer Destination",
  ],
  [alertTypes.DELIVERY_REPORT_DROP]: [
    "Supplier",
    "Customer",
    "Supplier Destination",
    "Customer Destination",
  ],
};

const volumeTypes = {
    SPIKE: "spike",
    DROP: "drop",
    BOTH: "both",
  },
  volumeTypesOptions = {
    // key : value => key is for FE UI display, value is for BE API
    Spike: volumeTypes.SPIKE,
    Drop: volumeTypes.DROP,
    Both: volumeTypes.BOTH,
  },
  categoryTypes = {
    CRITICAL: "critical",
    MAJOR: "major",
    MINOR: "minor",
  };

const alertNotifyMailConfig = {
  [categoryTypes.CRITICAL]: {
    duration: 6,
    unit: "hour",
  },
  [categoryTypes.MAJOR]: {
    duration: 8,
    unit: "hour",
  },
  [categoryTypes.MINOR]: {
    duration: 3,
    unit: "day",
  },
};


const timePeriodOptions = [
    { label: "Yesterday", value: 1 },
    { label: "Last three days", value: 3 },
    { label: "Last seven days", value: 7 },
    { label: "Last weekdays", value: 5 },
  ],
  timeIntervalOptions = [
    { label: "15 min", value: 15 },
    { label: "30 min", value: 30 },
    { label: "45 min", value: 45 },
    { label: "1 hour", value: 60 },
  ];

const alertConfig = {
  types: alertTypes,
  alertStatus,
  alertsHistoryStatus,
  filterOptions: alertFilterOptions,
  parameters: alertParameters,
  volumeTypes,
  volumeTypesOptions,
  timePeriodOptions,
  timeIntervalOptions,
  categoryTypes,
  alertNotifyMailConfig,
  notificationSec: 300, // 5 minutes
};

const alertApiFields = {
  delivery_failed: "delivery_failure_count_new",
  total_submitted: "total_submissions", // total_submitted | total_submissions
  source_ip: "source_ip",
  result_code: "result_code",
  error_description: "error_description",
  destination_ip: "destination_ip",
  host_id: "host_id", // sms_cdr direct column

  submission_success: "submission_success",
  submission_error: "submission_error",
  delivery_success: "count_successful_delivery_final", // "delivery_success",
  delivery_error: "delivery_error",
  delivery_report_success: "delivery_report_success",
  delivery_report_failed: "delivery_report_failure",
};

const resultCode = [
    { label: "1 SMSR internal error", value: 1 },
    { label: "0 SMSR internal error", value: 0 },
    { label: "2 User error", value: 2 },
    { label: "3 Provider error", value: 3 },
    { label: "4 Internal error", value: 4 },
    { label: "8 As error", value: 8 },
    { label: "9 Redirection error", value: 9 },
    { label: "10 SMPP protocol error", value: 10 },
  ],
  errorDescription = [
    { label: "101. Absent subscriber for SM", value: 101 },
    { label: "102 Called barred", value: 102 },
    { label: "103 Subscriber busy for MT", value: 103 },
    { label: "104 Facility not supported", value: 104 },
    { label: "105 SS Incompatible", value: 105 },
    { label: "106 SM delivery fail", value: 106 },
    { label: "107 Message list full", value: 107 },
  ];

const alertGrafanaStates = {
  NORMAL: "Normal",
  ALERTING: "Alerting",
  PENDING: "Pending",
  NODATA: "NoData",
  NORMAL_MISSING_SERIES: "Normal (MissingSeries)",
};

// const nodesConfig = Array.from({ length: 13 }, (_, i) => ({
//   label: `node ${i + 1}`,
//   value: i + 1,
// }));
const nodesConfig = [
  {
    label: "e2e-92-254",
    value: "e2e-92-254",
  }
]

const alertExportFields = {
  name: "Alert Name",
  alert_type: "Aler Type",
  category: "Alert Criteria",
  timestamp: "Timestamp",
  status: "Status"
}

module.exports = {
  createAlertConfig,
  alertConfig,
  alertApiFields,
  createContactPointConfig,
  getContactPointsConfig,
  getAlertsConfig,
  deleteAlertConfig,
  alertGrafanaStates,
  getErrDesMappingConfig,
  nodesConfig,
  alertExportFields
};
