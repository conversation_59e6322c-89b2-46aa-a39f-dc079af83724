const { Strategy: JwtStrategy, ExtractJwt } = require('passport-jwt');
const config = require('./config');
const { tokenTypes } = require('./tokens');
const { models } = require('../models');
const logger = require('./logger'),

  { User } = models,
  { Role } = models,
  jwtOptions = {
    secretOrKey: config.jwt.secret,
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  },

  jwtVerify = async (payload, done) => {
    try {
     
      if (payload.type !== tokenTypes.ACCESS) {
        throw new Error('Invalid token type');
      }
      const user = await User.findByPk(payload.sub, {
        include: [
          {
            model: Role
          }
        ]
      });
      if (!user) {
        return done(null, false);
      }
      user.timezone = payload.timezone;
      done(null, user);
    }
    catch (error) {
      logger.error(error);
      done(error, false);
    }
  },

  jwtStrategy = new JwtStrategy(jwtOptions, jwtVerify);

module.exports = {
  jwtStrategy,
};