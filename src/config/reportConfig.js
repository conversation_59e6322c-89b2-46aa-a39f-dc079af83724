const graphqlServerUri = `${process.env.DATA_SERVICE_URL}/graphql`;
const restapiUri = process.env.DATA_SERVICE_URL;

const maxPageLimit = 100000;
const timestampParam = "timestamp";
const reportClassification = {
  "Hub Traffic": [
    /*{
                "name": "Customer wise destination wise Traffic Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Customer wise destination wise Error Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day"
                    //"week"
                ]
            },
            {
                "name": "Supplier wise destination wise Latency Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Customer wise destination wise Latency Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Supplier wise destination wise Error Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week"
                ]
            },
            {
                "name": "Hourly Traffic Analysis",
                "viewBy": ["hour"]
            }, */
    {
      name: "Hourly Traffic Failure Analysis",
      viewBy: ["hour", "day", "week", "month"],
    },
    /* {
                "name": "Aggregate Traffic Analysis",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week"
                ]
            }, */
    {
      name: "Customer Supplier Destination Wise Error Report",
      viewBy: ["minute", "hour", "day", "week", "month"],
    },
    {
      name: "Hourly Customer Destination wise Traffic Report",
      viewBy: ["hour", "day", "week", "month"],
    },
    /* {
                "name": "Hourly Supplier Destination wise Traffic Report",
                "viewBy": ["hour"]
            },
            {
                "name": "Hourly Traffic Analysis (Supplier Wise)",
                "viewBy": ["hour"]
            },*/
    {
      name: "Incoming and Outgoing SRI",
      viewBy: ["hour", "day", "month", "week"],
    },
    {
      name: "SMS Retry Report",
      viewBy: ["hour", "day", "month", "week"],
    },
    {
      name: "E2E Hub Summary Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "E2E Hub Summary Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "E2E Hub Summary Report WOS",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "E2E Hub Summary Report WOS Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "E2E Hourly Traffic Report",
      viewBy: ["hour"],
    },
    {
      name: "E2E Hourly Traffic Report Multiple",
      viewBy: ["hour"],
    },
    {
      name: "E2E Hourly Traffic Report WOS",
      viewBy: ["hour"],
    },
    {
      name: "E2E Hourly Traffic Report WOS Multiple",
      viewBy: ["hour"],
    },
    {
      name: "LCR Switchover Report",
      viewBy: ["hour", "day", "month", "week"],
    },
    {
      name: "HLR Dip Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "HLR Dip Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "MNP Report",
      viewBy: ["hour", "day", "month", "week"],
    },
  ],
  Performance: [
    /* {
                "name": "Customer Performance Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },*/
    {
      name: "Customer Wise Destination Performance",
      viewBy: ["hour", "day", "week", "month"],
    },
    /*{
                "name": "Latency Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week"
                ]
            }, */
    /* {
                "name": "Destination wise Latency Reports",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week"
                ]
            },
            {
                "name": "Country Wise Performance",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Customer Wise Traffic Report",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Supplier Wise Destination Performance",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Customer Performance Report (Next Hop)",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Customer Wise Destination Performance (Next Hop)",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            },
            {
                "name": "Supplier Wise Destination Performance (Next Hop)",
                "viewBy": [
                    "minute",
                    "hour",
                    "day",
                    "week",
                    "month"
                ]
            }*/
  ],
  Billing: [
    {
      name: "Customer Message Fee Invoice Detail",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Message Fee Invoice Detail Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Message Fee Invoice Detail - Summary",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Message Fee Invoice Detail - Summary Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Termination Credit Details",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Termination Credit Details Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Processing Fee Invoice Detail",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Processing Fee Invoice Detail Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Processing Fee Invoice Summary",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Processing Fee Invoice Summary Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "REVENUE Summary",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "REVENUE Summary Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "COST Summary",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "COST Summary Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Prepaid Settlement Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Prepaid Settlement Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Supplier Termination Credit Details",
      viewBy: ["day", "week", "month"],
    },
    /*{
                "name": "Summary of Customer Termination Charges",
                "viewBy": ["day"]
            },*/
    /* {
                "name": "Customer Msg Fee Invoice Details Zero Rated",
                "viewBy": ["day"]
            },
            {
                "name": "Customer Termination Credit Details Zero Rated",
                "viewBy": ["day"]
            }*/

    /*  "Customer Termination Credit Summary",
              "Prime Wise Termination Cost",*/
  ],
  RA: [
    {
      name: "Cost Dump",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Revenue Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Reconciliation Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Reconciliation Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Supplier Reconciliation Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Supplier Reconciliation Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Billing",
      viewBy: ["hour", "day", "week", "month"],
    },
    {
      name: "Billing Multiple",
      viewBy: ["hour", "day", "week", "month"],
    },
    /* "Network Rejection Report",
             "Billing"*/
  ],
  MM: [
    {
      name: "Supplier Wise Cost Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Supplier Wise Cost Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Wise Revenue Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Customer Wise Revenue Report Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "WhatsApp Traffic Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Facebook Traffic Report",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Sms Firewall Report",
      viewBy: ["month"],
    },
  ],
  "Tier / Slab based billing": [
    {
      name: "Slab Based Billing Report (Customer)",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Slab Based Billing Report (Customer) Multiple",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Slab Based Billing Report (Supplier)",
      viewBy: ["day", "week", "month"],
    },
    {
      name: "Slab Based Billing Report (Supplier) Multiple",
      viewBy: ["day", "week", "month"],
    },
  ],
  MS: [
    {
      name: "A Number Report",
      viewBy: ["day", "week", "month"],
    },
  ],
  "Dynamic Reports": [],

  /* "Cust Outgoing": [
            //  "Outgoing Report"
        ],
        "Incoming Reports": [
            // "Incoming Report"
        ],
        "Performance Report": [
            //  "Outgoing/Incoming Performance Report"
        ]*/
};

const reportsMapping = {
  "Aggregate Traffic Analysis": {
    name: "getAggregateTrafficAnalysis",
    normalizedName: "AggregateTrafficAnalysis",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      source_operator_code: "Source Operator Code",
      source_operator_name: "Source Operator Name",
      destination_operator_code: "Destination Operator Code",
      destination_operator_name: "Destination Operator Name",
      status: "Status",
      number_of_records_new: "Count",
    },
    searchFields: [
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "destination_operator_name",
      "status",
    ],
    filters: {
      // customer_name: "Customer Name",
      // customer_bind: "Customer Bind",
      // supplier: "Supplier Name",
      // supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      // destination_country_name: "Destination Country",
      // supplier_interface_type: "Supplier Interface",
      // customer_interface_type: "Customer Interface",
      // customer_traffic_type: "Customer Protocol",
      // supplier_traffic_type: "Supplier Protocol",
      // destination_mcc_final: "Destination MCC",
      // destination_mnc_final: "Destination MNC",
      // lcr_name: "LCR Name",
      // spec_lcr: "Spec LCR",
      status: "Status",
    },
  },
  "Customer wise destination wise Error Report": {
    name: "getCustomerWiseDestinationWiseErrorReport",
    normalizedName: "CustomerWiseDestinationWiseErrorReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination: "Destination",
      traffic_type: "Traffic Type",
      status_code: "Status",
      //"error_code": "Error Code",
      //"result_code": "Result Code",
      error_description: "Error Description",
      number_of_records_new: "Count",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination",
      "traffic_type",
      "error_description",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      // supplier: "Supplier Name",
      // supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      // destination_country_name: "Destination Country",
      // supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      customer_traffic_type: "Customer Protocol",
      // supplier_traffic_type: "Supplier Protocol",
      // destination_mcc_final: "Destination MCC",
      // destination_mnc_final: "Destination MNC",
      // lcr_name: "LCR Name",
      // spec_lcr: "Spec LCR",
      status: "Status",
    },
  },
  "Customer wise destination wise Latency Report": {
    name: "getCustomerWiseDestinationWiseLatencyReport",
    normalizedName: "CustomerWiseDestinationWiseLatencyReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination: "Destination",
      total_submissions: "SMS Submitted",
      total_success: "Success",
      delivery_failure_count_new: "Error",
      average_latency_td_tr: "Avg Latency",
    },
    searchFields: ["customer_name", "customer_bind", "destination"],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier_interface_type: "Supplier Interface",
      destination: "Destination (Destination Operator)",
    },
  },
  "Customer wise destination wise Traffic Report": {
    name: "getCustomerWiseDestinationWiseTrafficReport",
    normalizedName: "CustomerWiseDestinationWiseTrafficReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination: "Destination",
      protocol: "Protocol",
      total_submitted: "Total Submission",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      delivery_success_count_new: "Delivery Success",
      delivery_failure_count_new: "Delivery Failure",
    },
    searchFields: ["customer_name", "customer_bind", "destination", "protocol"],
  },
  "E2E Hub Summary Report": {
    name: "getE2eHubSummaryReport",
    normalizedName: "E2eHubSummaryReport",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      status: "Status",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
      customer_kam: "Customer KAM",
      supplier_kam: "Supplier KAM",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "status",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "customer_kam",
      "supplier_kam",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hub_summary_report",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        count_successful_delivery_final: "Delivery Success",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hub Summary Report Multiple": {
    name: "getE2eHubSummaryReportMultiple",
    normalizedName: "E2eHubSummaryReportMultiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      status: "Status",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
      customer_kam: "Customer KAM",
      supplier_kam: "Supplier KAM",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "status",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "customer_kam",
      "supplier_kam",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hub_summary_report_multiple",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        count_successful_delivery_final: "Delivery Success",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hub Summary Report WOS": {
    name: "getE2eHubSummaryReportWos",
    normalizedName: "E2eHubSummaryReportWos",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
      customer_kam: "Customer KAM",
      supplier_kam: "Supplier KAM",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "customer_kam",
      "supplier_kam",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hub_summary_report_wos",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        count_successful_delivery_final: "Delivery Success",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hub Summary Report WOS Multiple": {
    name: "getE2eHubSummaryReportWosMultiple",
    normalizedName: "E2eHubSummaryReportWosMultiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
      customer_kam: "Customer KAM",
      supplier_kam: "Supplier KAM",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "customer_kam",
      "supplier_kam",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hub_summary_report_multiple",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        count_successful_delivery_final: "Delivery Success",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hourly Traffic Report": {
    name: "getE2eHourlyTrafficReport",
    normalizedName: "E2eHourlyTrafficReport",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      status: "Status",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "status",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hourly_traffic_report",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        count_successful_delivery_final: "Delivery Success",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hourly Traffic Report Multiple": {
    name: "getE2eHourlyTrafficReportMultiple",
    normalizedName: "E2eHourlyTrafficReportMultiple",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      status: "Status",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "status",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hourly_traffic_report_multiple",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        count_successful_delivery_final: "Delivery Success",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hourly Traffic Report WOS": {
    name: "getE2eHourlyTrafficReport",
    normalizedName: "E2eHourlyTrafficReport",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hourly_traffic_report",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        count_successful_delivery_final: "Delivery Success",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },
  "E2E Hourly Traffic Report WOS Multiple": {
    name: "getE2eHourlyTrafficReportWosMultiple",
    normalizedName: "E2eHourlyTrafficReportWosMultiple",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      traffic_type_customer: "Source  Traffic Type",
      source_protocol: "Source Protocol",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      supplier_interconnect: "Supllier Interconnect",
      customer_interconnect: "Customer Interconnect",
      source_mnp_supplier: "Source MNP Supplier",
      destination_mnp_supplier: "Destination MNP Supplier",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      dest_hub: "DEST HUB",
      destination_protocol: "Destination Protocol",
      destination_operator_code: "Destination operator code",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      traffic_type_supplier: "Supplier Traffic Type",
      destination_mcc_final: "Destinaton MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      total_submissions: "Total Submission",
      submission_success: "Sub / Next Hop Sub",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency",
      next_hop_success_new: "Next Hop Success",
      next_hop_success_percent_final: "Next Hop Success Percent (%)",
      total_deliveries: "Total Delivery",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
      final_delivery_efficiency: "Delivery Efficiency",
      percentage_successful: "Success Percentage(%)",
      percentage_failure: "Failure Percentage(%)",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "source_country_name",
      "src_hub",
      "dest_hub",
      "traffic_type_supplier",
      "source_protocol",
      "destination_country_name",
      "destination_operator_name",
      "source_protocol",
      "visiting_operator",
      "destination_protocol",
      "customer_interconnect",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_billing_logic_multiple: "Customer Billing Logic",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "e2e_hourly_traffic_report_multiple",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Sub / Next Hop Sub",
        submission_error: "Submission Error",
        next_hop_success_new: "Next Hop Success",
        total_deliveries: "Total Delivery",
        delivery_failure_count_final: "Delivery Failure",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        count_successful_delivery_final: "Delivery Success",
        final_delivery_efficiency: "Delivery Efficiency",
        percentage_successful: "Success Percentage(%)",
        percentage_failure: "Failure Percentage(%)",
      },
    },
  },

  "LCR Switchover Report": {
    name: "getLcrSwitcoverReport",
    normalizedName: "LcrSwitcoverReport",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      supplier: "Switchover Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      destination_country_code: "Destination Country Code",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_switch_over_value: "LCR Switch Over Value",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      total_submissions: "Total Submission",
      submission_success: "Submission Success",
      count_successful_delivery_final: "Success Count ",
      delivery_failure_count_final: "Failure Count",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_system_id",
      "customer_interconnect",
      "supplier",
      "supplier_bind",
      "supplier_system_id",
      "supplier_interconnect",
      "destination_operator_code",
      "destination_operator_name",
      "destination_country_name",
      "lcr_name",
      "spec_lcr",
      "customer_system_id",
      "customer_interconnect",
      "final_operator_name",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "lcr_switcover_report",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Submission Success",
        delivery_failure_count_final: "Failure Count",
      },
      derivedFieldName: {
        count_successful_delivery_final: "Success Count",
      },
    },
  },
  "HLR Dip Report": {
    name: "getHlrDipReport",
    normalizedName: "HlrDipReport",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      week_of_the_year: "Week",
      billing_month: "Billing Month",
      src_nop_id: "Source NOP ID",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      source_operator_code: "Source Operator Code",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      src_prime: "Source Prime",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      traffic_type_customer: "Traffic Type Src",
      customer_interface_type: "Interface Type Src",
      destination_operator_code: "Destination Operator Code",
      visiting_operator: "Destination Operator (Roaming)",
      dest_nop_id: "Destination NOP ID",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Interface Type Supplier",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      dest_prime: "Destination Prime",
      destination_country_code: "Destination Country Code",
      visited_opr_country: "Dest Country Name Roaming",
      destination_mnc_final: "Destination MCC",
      destination_mcc_final: "Destination MNC",
      final_operator_name: "Final Operator Name",
      customer_billing_logic: "Source Billing Logic",
      total_sri_count: "Total SRI Count",
      sri_success: "SRI Success",
      sri_failure: "SRI Failure",
      mt_counts: "MT Counts",
      only_hlr_dip: "Only HLR Dip",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_system_id",
      "customer_interconnect",
      "source_operator_code",
      "source_operator_name",
      "src_hub",
      "src_prime",
      "source_country_name",
      "traffic_type_customer",
      "customer_interface_type",
      "destination_operator_code",
      "visiting_operator",
      "supplier",
      "supplier_bind",
      "supplier_system_id",
      "supplier_interconnect",
      "traffic_type_supplier",
      "supplier_interface_type",
      "lcr_name",
      "spec_lcr",
      "dest_hub",
      "dest_prime",
      "visited_opr_country",
      "final_operator_name",
      "customer_billing_logic",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      customer_billing_logic: "Customer Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      bilateral: ["destination_operator_code"],
    },
    aggregationFieldMapping: {
      name: "hlr_dip_report",
      fieldName: {
        total_sri_count: "Total SRI Count",
        sri_success: "SRI Success",
        sri_failure: "SRI Failure",
        mt_counts: "MT Counts",
        only_hlr_dip: "Only HLR Dip",
      },
    },
  },
  "HLR Dip Report Multiple": {
    name: "getHlrDipReportMultiple",
    normalizedName: "HlrDipReportMultiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      week_of_the_year: "Week",
      billing_month: "Billing Month",
      src_nop_id: "Source NOP ID",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      source_operator_code: "Source Operator Code",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      src_prime: "Source Prime",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      traffic_type_customer: "Traffic Type Src",
      customer_interface_type: "Interface Type Src",
      destination_operator_code: "Destination Operator Code",
      visiting_operator: "Destination Operator (Roaming)",
      dest_nop_id: "Destination NOP ID",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Interface Type Supplier",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      dest_prime: "Destination Prime",
      destination_country_code: "Destination Country Code",
      visited_opr_country: "Dest Country Name Roaming",
      destination_mnc_final: "Destination MCC",
      destination_mcc_final: "Destination MNC",
      final_operator_name: "Final Operator Name",
      customer_billing_logic_multiple: "Source Billing Logic",
      total_sri_count: "Total SRI Count",
      sri_success: "SRI Success",
      sri_failure: "SRI Failure",
      mt_counts: "MT Counts",
      only_hlr_dip: "Only HLR Dip",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_system_id",
      "customer_interconnect",
      "source_operator_code",
      "source_operator_name",
      "src_hub",
      "src_prime",
      "source_country_name",
      "traffic_type_customer",
      "customer_interface_type",
      "destination_operator_code",
      "visiting_operator",
      "supplier",
      "supplier_bind",
      "supplier_system_id",
      "supplier_interconnect",
      "traffic_type_supplier",
      "supplier_interface_type",
      "lcr_name",
      "spec_lcr",
      "dest_hub",
      "dest_prime",
      "visited_opr_country",
      "final_operator_name",
      "customer_billing_logic_multiple",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      customer_billing_logic_multiple: "Customer Billing Logic",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      bilateral: ["destination_operator_code"],
    },
    aggregationFieldMapping: {
      name: "hlr_dip_report",
      fieldName: {
        total_sri_count: "Total SRI Count",
        sri_success: "SRI Success",
        sri_failure: "SRI Failure",
        mt_counts: "MT Counts",
        only_hlr_dip: "Only HLR Dip",
      },
    },
  },
  "MNP Report": {
    name: "getMnpReport",
    normalizedName: "MnpReport",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      hour_of_day: "Hour",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      source_operator_code: "SOP ID",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_mnp_type: "Source MNP Type",
      source_mnp_supplier: "Source MNP GW",
      source_mnp_success_count: "Source MNP Success Count",
      source_mnp_failure_count: "Source MNP Failure Count",
      source_mnp_average_cost_euro: "Src MNP Average Cost (Euro)",
      soure_mnp_cost_euro: "Source MNP Dip Cost (Euro)",
      source_mnp_currency: "Source MNP Currency",
      destination_operator_code: "DOP ID",
      destination_country_code: "Destination Country Code",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      destination_mnp_type: "Destination MNP Type",
      destination_mnp_supplier: "Destination MNP GW",
      destination_mnp_success_count: "Dest MNP Success Count",
      destination_mnp_failure_count: "Dest MNP Failure Count",
      destination_mnp_average_cost_euro: "Dest MNP Average Cost (Euro)",
      destination_mnp_cost_euro: " Dest MNP Dip Cost (Euro)",
      destination_mnp_currency: "Dest MNP Currency",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "supplier",
      "supplier_bind",
      "customer_interconnect",
      "supplier_interconnect",
      "supplier_system_id",
      "customer_system_id",
      "source_mnp_supplier",
      "destination_mnp_supplier",
      "source_mnp_currency",
      "destination_mnp_currency",
      "source_mnp_type",
      "destination_mnp_type",
      "hour_of_day",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
    },
    otherFilters: {
      bilateral: ["destination_country_code", "destination_country_code"],
    },
    aggregationFieldMapping: {
      name: "mnp_report",
      fieldName: {
        destination_mnp_success_count: "Dest MNP Success Count",
        destination_mnp_failure_count: "Dest MNP Failure Count",
        destination_mnp_average_cost_euro: "Dest MNP Average Cost (Euro)",
        destination_mnp_cost_euro: "Dest MNP Dip Cost (Euro)",
        source_mnp_success_count: "Source MNP Success Count",
        source_mnp_failure_count: "Source MNP Failure Count",
        source_mnp_average_cost_euro: "Src MNP Average Cost (Euro)",
        soure_mnp_cost_euro: "Source MNP Dip Cost (Euro)",
      },
    },
  },
  "Customer report": {
    name: "getRecordsByCustomer",
    normalizedName: "RecordsByCustomer",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      number_of_records: "No. of Records",
    },
    searchFields: ["customer_name", "customer_bind"],
  },
  "Customer success rate report": {
    name: "getSuccessRateByCustomer",
    normalizedName: "SuccessRateByCustomer",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      success_rate: "Success Rate",
    },
    searchFields: ["customer_name"],
  },
  "Supplier wise destination wise Latency Report": {
    name: "getSupplierWiseDestinationWiseLatencyReport",
    normalizedName: "SupplierWiseDestinationWiseLatencyReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination: "Destination",
      total_submissions: "SMS Submitted",
      total_success: "Success",
      delivery_failure_count_new: "Error",
      average_latency_td_tr: "Avg Latency",
    },
    searchFields: ["supplier", "supplier_bind", "destination"],
  },
  "Hourly Traffic Analysis": {
    name: "getTrafficAnalysis",
    normalizedName: "TrafficAnalysis",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_bind: "Customer Bind",
      status: "Status",
      number_of_records_new: "Count",
    },
    searchFields: ["customer_bind", "status"],
  },
  /* "Hourly Traffic Data Report" : {
             name: "getTrafficDataReport",
             defaultFilters: {
                 viewBy: "hour"
             },
             responseFields: {
                 "timestamp" : "Date",
                 "customer_name" : "Customer",
                 "customer_bind" : "Customer Bind",
                 "total_success" : "Submission Success",
                 "total_submitted" : "Total Submission",
                 "success_rate" : "Success (%)",
                 "protocol" : "Source Protocol",
                 "traffic_type" : "Traffic Type",
                 "supplier" : "Supplier",
                 "supplier_bind" : "Supplier Bind",
                 "source_operator_code" : "Source Operator ID",
                 "source_operator_name" : "Source Operator",
                 "source_mcc" : "Source MCC",
                 "source_mnc" : "Source MNC",
                 "destination_mcc" : "Destination MCC",
                 "destination_mnc" : "Destination MNC"
             }
         },*/

  "Hourly Traffic Failure Analysis": {
    name: "getTrafficFailureAnalysis",
    normalizedName: "TrafficFailureAnalysis",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      traffic_type_customer: "Customer Traffic Type",
      customer_interface_type: "Customer Interface",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      lcr_name: "LCR Name",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Destination Operator",
      visiting_operator: "Visited Operator",
      spec_lcr: "Spec LCR",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      status: "Status",
      failure_reason_final: "Failure Reason",
      number_of_records_new: "Count",
      final_operator_name: "Final Operator Name", //,
      //"error_code":"Error Code",
      //"result_code":"Result Code",
      //"error_description":"Error Description"
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_supplier",
      "traffic_type_customer",
      "status",
      "supplier",
      "supplier_bind",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
      "lcr_name",
      "visiting_operator",
      "supplier_interface_type",
      "customer_interface_type",
      "customer_interconnect",
      "supplier_interconnect",
      "failure_reason_final",
      "error_description",
      "final_operator_name",
      "spec_lcr",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "traffic_failure_analysis",
      fieldName: {
        number_of_records_new: "Count",
      },
    },
  },
  "Incoming and Outgoing SRI": {
    name: "getIncomingAndOutgoingSri",
    normalizedName: "IncomingAndOutgoingSri",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination: "Destination",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      visiting_operator: "Visited Operator",
      final_operator_name: "Final Operator",
      destination_operator_code: "Operator ID",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      total_submissions: "Total Count",
      total_success: "Success Count",
      delivery_failure_new_final: "Failure Count",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination",
      "supplier",
      "supplier_bind",
      "destination_country_name",
      "destination_operator_code",
      "visiting_operator",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier_name: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination: "Destination (Destination Operator)",
      destination_country_name: "Destination Country",
      destination_mnc_final: "Destination MNC",
      destination_mcc_final: "Destination MCC",
      only_roaming: "Only Roaming",
      only_direct: "Only Direct",
      both: "Both",
    },
    otherFilters: {
      only_roaming: "destination != visiting_operator",
      only_direct: "destination = visiting_operator",
      both: true,
      bilateral: ["destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "incoming_and_outgoing_sri",
      fieldName: {
        total_submissions: "Total Count",
        total_success: "Success Count",
        delivery_failure_new_final: "Failure Count",
      },
    },
  },
  "Customer Performance Report": {
    name: "getCustomerPerformanceReport",
    normalizedName: "CustomerPerformanceReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      delivery_success_count_new: "Successful Deliveries",
      delivery_failure_count_new: "Failed Deliveries",
      delivery_efficiency_new: "Delivery Efficiency(%)",
      pending_delivery_new: "Pending Delivery",
    },
    searchFields: ["customer_name", "customer_bind"],
  },

  "SMS Retry Report": {
    name: "getSmsRetryReport",
    normalizedName: "SmsRetryReport",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interface_type: "Customer Interface Type",
      customer_interconnect: "Customer Interconnect",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_interface_type: "Supplier Interface",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      destination_operator_code: "Destination Operator Code",
      destination_operator_name: "Destination Operator Name",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country Name",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      delivery_retry: "Delivery Retry",
      total_submissions: "Total Submission",
      submission_success: "Submission Success",
      count_successful_delivery_final: "Message Delivered Count",
      delivery_failure_count_final: "Failure Count",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_system_id",
      "customer_interface_type",
      "customer_interconnect",
      "supplier",
      "supplier_bind",
      "supplier_interface_type",
      "supplier_system_id",
      "supplier_interconnect",
      "lcr_name",
      "spec_lcr",
      "destination_operator_code",
      "destination_operator_name",
      "destination_country_name",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "sms_retry_report",
      fieldName: {
        delivery_retry: "Delivery Retry",
        total_submissions: "Total Submission",
        submission_success: "Submission Success",
        delivery_failure_count_final: "Failure Count",
      },
      derivedFieldName: {
        count_successful_delivery_final: "Message Delivered Count",
      },
    },
  },
  "Customer Wise Destination Performance": {
    name: "getCustomerWiseDestinationPerformance",
    normalizedName: "CustomerWiseDestinationPerformance",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator Name",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      destination_country_name: "Destination Country Name",
      destination_country_code: "Destination Country Code",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      count_successful_delivery_final: "Successful Deliveries",
      delivery_failure_count_final: "Failed Deliveries",
      final_delivery_efficiency: "Delivery Efficiency(%)",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination_operator_code",
      "destination_operator_name",
      "destination_country_name",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
    },
    otherFilters: {
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_wise_destination_performance",
      fieldName: {
        total_submissions: "Total Submissions",
        submission_success: "Submission Success",
        submission_error: "Submission Error",
        total_deliveries: "Total Deliveries",
        delivery_failure_count_final: "Failed Deliveries",
      },
      derivedFieldName: {
        submission_efficiency: "Submission Efficiency(%)",
        count_successful_delivery_final: "Successful Deliveries",
        final_delivery_efficiency: "Delivery Efficiency(%)",
      },
    },
  },
  "Latency Report": {
    name: "getPerformanceLatencyReport",
    normalizedName: "PerformanceLatencyReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      total_submissions: "Total Submission",
      avg_submission_latency: "Avg Submission Latency(sec)",
      average_latency_td_tr: "Avg Hub Latency(sec)",
      total_deliveries: "Total Delivery",
      average_latency: "Avg Delivery Latency(sec)",
      delivery_retries: "No Of Delivery Retries",
    },
    searchFields: ["customer_name", "customer_bind"],
  },
  "Destination wise Latency Reports": {
    name: "getDestinationWiseLatencyReports",
    normalizedName: "DestinationWiseLatencyReports",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator Name",
      destination_mcc: "Destination MCC    ",
      destination_mnc: "Destination MNC",
      destination_country_name: "Destination Country",
      destination_country_code: "Destination Country Code",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      total_submissions: "Total Submission",
      avg_submission_latency: "Avg Submission Latency(sec)",
      average_latency_td_tr: "Avg Hub Latency(sec)",
      average_latency: "Avg Delivery Latency(sec)",
      delivery_retries: "No Of Delivery Retries",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination_operator_code",
      "destination_operator_name",
      "destination_country_name",
      "supplier",
      "supplier_bind",
    ],
  },
  "Country Wise Performance": {
    name: "getCountryWisePerformance",
    normalizedName: "CountryWisePerformance",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination_country_name: "Destination Country Name",
      success_percentage_new: "Success Rate",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination_country_name",
    ],
  },
  "Customer Wise Traffic Report": {
    name: "getPerformanceCustomerWiseTrafficReport",
    normalizedName: "PerformanceCustomerWiseTrafficReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      customer_interface_type: "Interface Type",
      traffic_type: "Traffic Type",
      submission_success: "Submit Success",
      delivery_success_count_new: "Delivery Success",
      delivery_failure_count_new: "Failure",
      success_percentage_new: "Succes Percentage",
      failure_percentage_new: "Failure Percentage",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type",
      "customer_interface_type",
    ],
  },

  "Customer Performance Report (Next Hop)": {
    name: "getCustomerPerformanceReportNextHop",
    normalizedName: "CustomerPerformanceReportNextHop",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      next_hop_success: "Next Hop Success",
      next_hop_failure: "Next Hop Failure",
      next_hop_success_percent: "Next Hop Success Percent",
      pending_next_hop: "Pending Next Hop",
    },
    searchFields: ["customer_name", "customer_bind"],
  },

  "Customer Supplier Destination Wise Error Report": {
    name: "getCustomerSupplierDestinationWiseErrorReport",
    normalizedName: "CustomerSupplierDestinationWiseErrorReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_interconnect: "Customer Interconnect",
      traffic_type_customer: "Customer Traffic Type",
      customer_interface_type: "Customer Interface",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      supplier_interconnect: "Supplier Interconnect",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface",
      lcr_name: "LCR Name",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Destination Operator",
      visiting_operator: "Visited Operator",
      spec_lcr: "Spec LCR ",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country Name",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      status: "Status",
      failure_reason_final: "Failure Reason",
      number_of_records_new: "Count",
      final_operator_name: "Final Operator Name", //,
      //"error_code": "Error Code",
      //"result_code": "Result Code",
      //"error_description": "Error Description"
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_interface_type",
      "supplier",
      "supplier_bind",
      "supplier_interface_type",
      "destination_operator_name",
      "destination_country_name",
      "destination_operator_code",
      "lcr_name",
      "visiting_operator",
      "status",
      "failure_reason_final",
      "final_operator_name",
      "traffic_type_customer",
      "traffic_type_supplier",
      //"error_description"
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    otherFilters: {
      only_roaming: "destination_operator_name!=visiting_operator",
      only_direct: "destination_operator_name=visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_supplier_destination_wise_error_report",
      fieldName: {
        number_of_records_new: "Count",
      },
    },
  },
  "Customer Wise Destination Performance (Next Hop)": {
    name: "getCustomerWiseDestinationPerformanceNextHop",
    normalizedName: "CustomerWiseDestinationPerformanceNextHop",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator Name",
      destination_mcc: "Destination MCC",
      destination_mnc: "Destination MNC",
      destination_country_name: "Destination Country Name",
      destination_country_code: "Destination Country Code",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      next_hop_success: "Next Hop Success",
      next_hop_failure: "Next Hop Failure",
      next_hop_success_percent: "Next Hop Success Percent",
      pending_next_hop: "Pending Next Hop",
    },
    searchFields: [
      "customer_bind",
      "customer_name",
      "destination_country_name",
    ],
  },
  "Supplier wise destination wise Error Report": {
    name: "getSupplierWiseDestinationWiseErrorReport",
    normalizedName: "SupplierWiseDestinationWiseErrorReport",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination: "Destination",
      traffic_type: "Traffic Type",
      status: "Status",
      number_of_records_new: "Count",
    },
    searchFields: [
      "supplier",
      "supplier_bind",
      "traffic_type",
      "destination",
      "status",
    ],
  },
  "Hourly Customer Destination wise Traffic Report": {
    name: "getCustomerDestinationWiseTrafficReport",
    normalizedName: "CustomerDestinationWiseTrafficReport",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer",
      customer_bind: "Customer Bind",
      destination: "Destination",
      supplier_interface_type: "Interface Type Supplier",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      count_successful_delivery_final: "Delivery Success",
      delivery_failure_count_final: "Delivery Failure",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination",
      "supplier_interface_type",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      supplier_interface_type: "Supplier Interface",
      destination: "Destination (Destination Operator)",
    },
    otherFilters: {
      bilateral: ["destination"],
    },
    aggregationFieldMapping: {
      name: "customer_destination_wise_traffic_report",
      fieldName: {
        submission_success: "Submission Success",
        submission_error: "Submission Error",
        delivery_failure_count_final: "Delivery Failure",

        count_successful_delivery_final: "Delivery Success",
      },
      // derivedFieldName: {
      //   count_successful_delivery_final: "Delivery Success",
      // },
    },
  },
  "Hourly Supplier Destination wise Traffic Report": {
    name: "getSupplierDestinationWiseTrafficReport",
    normalizedName: "SupplierDestinationWiseTrafficReport",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination: "Destination",
      traffic_type: "Supplier Traffic Type",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      delivery_success_count_new: "Delivery Success",
      delivery_failure_count_new: "Delivery Failure",
    },
    searchFields: ["supplier", "supplier_bind", "destination", "traffic_type"],
  },
  "Hourly Traffic Analysis (Supplier Wise)": {
    name: "getTrafficAnalysisSupplierWise",
    normalizedName: "TrafficAnalysisSupplierWise",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      supplier_bind: "Supplier Bind",
      status: "Status",
      number_of_records_new: "Count",
    },
    searchFields: ["supplier_bind", "status"],
  },
  "Supplier Wise Destination Performance": {
    name: "getSupplierWiseDestinationPerformance",
    normalizedName: "SupplierWiseDestinationPerformance",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator Name",
      destination_mcc: "Destination MCC",
      destination_mnc: "Destination MNC",
      destination_country_name: "Destination Country Name",
      destination_country_code: "Destination Country Code",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      delivery_success_count_new: "Successful Deliveries",
      delivery_failure_count_new: "Failed Deliveries",
      delivery_efficiency_new: "Delivery Efficiency(%)",
    },
    searchFields: [
      "supplier_bind",
      "supplier",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
    ],
  },
  "Supplier Wise Destination Performance (Next Hop)": {
    name: "getSupplierWiseDestinationPerformanceNextHop",
    normalizedName: "SupplierWiseDestinationPerformanceNextHop",
    defaultFilters: {
      viewBy: "minute",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator Name",
      destination_mcc: "Destination MCC",
      destination_mnc: "Destination MNC",
      destination_country_name: "Destination Country Name",
      destination_country_code: "Destination Country Code",
      total_submissions: "Total Submissions",
      submission_success: "Submission Success",
      submission_error: "Submission Error",
      submission_efficiency: "Submission Efficiency(%)",
      total_deliveries: "Total Deliveries",
      next_hop_success: "Next Hop Success(%)",
      next_hop_failure: "Next Hop Failure(%)",
      next_hop_success_percent: "Next Hop Success Percent",
      pending_next_hop: "Pending Next Hop",
    },
    searchFields: ["supplier_bind", "supplier"],
  },
  "Customer Message Fee Invoice Detail": {
    name: "getCustomerMessageFeeInvoiceDetail",
    normalizedName: "CustomerMessageFeeInvoiceDetail",
    weeklyMonthlyReportName: "customer_message_fee_invoice_detail",
    category: "billing",
    viewBy: ["day", "week", "month"],
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      customer_name: "Source Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_timezone: "Customer Time Zone",
      src_routing_id: "Source Routing ID",
      source_operator_code: "Source Operator ID",
      source_country_name: "Source Country",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Destination Op Visited",
      customer_interconnect: "Customer Interconnect",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      cdr_type_description: "CDR Type Description",
      bparty_billing_logic: "B-Party Billing",
      customer_billing_logic_count_final: "Message Processed",
      unit_new_rate_round: "Unit Rate",
      cust_message_fee: "Message Fee",
      unit_new_currency: "Currency",
      cust_message_fee_euro: "Messaging Fee (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
      oracle_account_no: "Oracle ID",
      euro_new_rate_round: "Exchange Rate",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_interconnect",
      "source_operator_code",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
      "source_country_name",
      "visiting_operator",
      "unit_new_currency",
      "cdr_type_description",
      "src_prime",
      "final_operator_name",
      "customer_system_id",
      "bparty_billing_logic",
      "customer_timezone",
      "traffic_type_customer",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      customer_interconnect: "Customer Interconnect",
      // visiting_operator: "Visiting Operator",
    },
    otherFilters: {
      only_roaming: "destination_operator_name != visiting_operator",
      only_direct: "destination_operator_name = visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_message_fee_invoice_detail",
      aggregationViewBy: "day",
      fieldName: {
        customer_billing_logic_count_final: "Message Processed",
        unit_new_rate_round: "Unit Rate",
        cust_message_fee: "Message Fee",
        euro_new_rate_round: "Exchange Rate",
        cust_message_fee_euro: "Messaging Fee (EUR)",
      },
    },
  },
  "Customer Message Fee Invoice Detail Multiple": {
    name: "getCustomerMessageFeeInvoiceDetailMultiple",
    normalizedName: "CustomerMessageFeeInvoiceDetailMultiple",
    weeklyMonthlyReportName: "customer_message_fee_invoice_detail_multiple",
    category: "billing",
    viewBy: ["day", "week", "month"],
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      customer_name: "Source Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_timezone: "Customer Time Zone",
      src_routing_id: "Source Routing ID",
      source_operator_code: "Source Operator ID",
      source_country_name: "Source Country",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Destination Op Visited",
      customer_interconnect: "Customer Interconnect",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      cdr_type_description_multiple: "CDR Type Description",
      bparty_billing_logic: "B-Party Billing",
      customer_billing_logic_count_multiple: "Message Processed",
      unit_new_rate_round: "Unit Rate",
      cust_message_fee_multiple: "Message Fee",
      unit_new_currency: "Currency",
      cust_message_fee_euro_multiple: "Messaging Fee (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
      oracle_account_no: "Oracle ID",
      euro_new_rate_round: "Exchange Rate",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "customer_interconnect",
      "source_operator_code",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
      "source_country_name",
      "visiting_operator",
      "unit_new_currency",
      "cdr_type_description_multiple",
      "src_prime",
      "final_operator_name",
      "customer_system_id",
      "bparty_billing_logic",
      "customer_timezone",
      "traffic_type_customer",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      customer_interconnect: "Customer Interconnect",
    },
    otherFilters: {
      only_roaming: "destination_operator_name != visiting_operator",
      only_direct: "destination_operator_name = visiting_operator",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_message_fee_invoice_detail_multiple",
      aggregationViewBy: "day",
      fieldName: {
        customer_billing_logic_count_multiple: "Message Processed",
        unit_new_rate_round: "Unit Rate",
        cust_message_fee_multiple: "Message Fee",
        euro_new_rate_round: "Exchange Rate",
        cust_message_fee_euro_multiple: "Messaging Fee (EUR)",
      },
    },
  },
  "Customer Message Fee Invoice Detail - Summary": {
    name: "getCustomerMessageFeeInvoiceDetailSummary",
    normalizedName: "CustomerMessageFeeInvoiceDetailSummary",
    weeklyMonthlyReportName: "customer_message_fee_invoice_detail_summary",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Source Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Destination Op Visited",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      customer_billing_logic_count_final: "Message Processed",
      unit_new_rate_round: "Unit Rate",
      cust_message_fee: "Message Fee Customer",
      unit_new_currency: "Currency",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
      "visiting_operator",
      "src_prime",
      "customer_system_id",
      "traffic_type_customer",
      "unit_new_currency",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_message_fee_invoice_detail_summary",
      fieldName: {
        customer_billing_logic_count_final: "Messages Processed",
        unit_new_rate_round: "Unit Rate",
        cust_message_fee: "Messaging Fee",
      },
    },
  },
  "Customer Message Fee Invoice Detail - Summary Multiple": {
    name: "getCustomerMessageFeeInvoiceDetailSummaryMultiple",
    normalizedName: "CustomerMessageFeeInvoiceDetailSummaryMultiple",
    weeklyMonthlyReportName:
      "customer_message_fee_invoice_detail_summary_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Source Customer",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Destination Op Visited",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      customer_billing_logic_count_multiple: "Message Processed",
      unit_new_rate_round: "Unit Rate",
      cust_message_fee_multiple: "Message Fee Customer",
      unit_new_currency: "Currency",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "destination_country_name",
      "destination_operator_name",
      "destination_operator_code",
      "visiting_operator",
      "src_prime",
      "customer_system_id",
      "traffic_type_customer",
      "unit_new_currency",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator: "Visiting Operator",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_message_fee_invoice_detail_summary_multiple",
      fieldName: {
        customer_billing_logic_count_multiple: "Messages Processed",
        unit_new_rate_round: "Unit Rate",
        cust_message_fee_multiple: "Messaging Fee",
      },
    },
  },
  "Customer Termination Credit Details": {
    name: "getCustomerTerminationCreditDetails",
    normalizedName: "CustomerTerminationCreditDetails",
    weeklyMonthlyReportName: "customer_termination_credit_details",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      customer_name: "Source Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      src_hub: "Source Hub",
      src_routing_id: "Source Routing ID",
      source_operator_name: "Source Operator Name",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      customer_interface_type: "Customer Interface Type",
      dest_nop_id: "Destination NOP ID",
      supplier_interconnect: "Supplier Interconnect",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      dest_prime: "Destination Prime",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      supplier_timezone: "Supplier Timezone",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Visiting Operator",
      cdr_type_supplier: "CDR Type",
      supplier_billing_count_final: "Messages Terminated",
      billing_option: "Billing Option",
      final_delivery_unit_cost_round: "Termination Rate",
      supplier_currency_new: "Currency",
      supplier_exchange_rate: "Exchange Rate",
      final_delivery_unit_cost_euro: "Termination Rate (EUR)",
      final_termination_charges_euro: "Termination Credit (EUR)",
      cost_start_date: "From Date",
      cost_end_date: "To Date",
      final_termination_charges: "Termination Fee",
      oracle_account_no: "Oracle ID",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "traffic_type_supplier",
      "supplier_timezone",
      "supplier_interconnect",
      "source_operator_name",
      "source_country_name",
      "supplier",
      "supplier_bind",
      "lcr_name",
      "spec_lcr",
      "source_operator_name",
      "destination_operator_name",
      "destination_country_name",
      "destination_operator_code",
      "visiting_operator",
      "supplier_interface_type",
      "supplier_currency_new",
      "customer_interface_type",
      "src_hub",
      "dest_hub",
      "src_prime",
      "dest_prime",
      "final_operator_name",
      "billing_option",
      "cdr_type_supplier",
      "supplier_system_id",
      "customer_system_id",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_interface_type: "Customer Interface",
      supplier_interface_type: "Supplier Interface",
      traffic_type_supplier: "Supplier Protocol",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_termination_credit_details",
      fieldName: {
        supplier_billing_count_final: "Messages Terminated",
        final_delivery_unit_cost_round: "Termination Rate",
        // supplier_exchange_rate: "Exchange Rate",
        final_delivery_unit_cost_euro: "Termination Rate (EUR)",
        final_termination_charges_euro: "Termination Credit (EUR)",
      },
    },
  },
  "Customer Termination Credit Details Multiple": {
    name: "getCustomerTerminationCreditDetailsMultiple",
    normalizedName: "CustomerTerminationCreditDetailsMultiple",
    weeklyMonthlyReportName: "customer_termination_credit_details_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      customer_name: "Source Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      src_hub: "Source Hub",
      src_routing_id: "Source Routing ID",
      source_operator_name: "Source Operator Name",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      src_prime: "Source Prime",
      traffic_type_customer: "Traffic Type",
      customer_interface_type: "Customer Interface Type",
      dest_nop_id: "Destination NOP ID",
      supplier_interconnect: "Supplier Interconnect",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      dest_prime: "Destination Prime",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      supplier_timezone: "Supplier Timezone",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Visiting Operator",
      cdr_type_supplier_multiple: "CDR Type",
      supplier_billing_count_multiple: "Messages Terminated",
      billing_option: "Billing Option",
      final_delivery_unit_cost_round: "Termination Rate",
      supplier_currency_new: "Currency",
      supplier_exchange_rate: "Exchange Rate",
      final_delivery_unit_cost_euro: "Termination Rate (EUR)",
      termination_charges_euro_multiple: "Termination Credit (EUR)",
      cost_start_date: "From Date",
      cost_end_date: "To Date",
      termination_charges_multiple: "Termination Fee",
      oracle_account_no: "Oracle ID",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "traffic_type_supplier",
      "supplier_timezone",
      "supplier_interconnect",
      "source_operator_name",
      "source_country_name",
      "supplier",
      "supplier_bind",
      "lcr_name",
      "spec_lcr",
      "source_operator_name",
      "destination_operator_name",
      "destination_country_name",
      "destination_operator_code",
      "visiting_operator",
      "supplier_interface_type",
      "supplier_currency_new",
      "customer_interface_type",
      "src_hub",
      "dest_hub",
      "src_prime",
      "dest_prime",
      "final_operator_name",
      "billing_option",
      "cdr_type_supplier_multiple",
      "supplier_system_id",
      "customer_system_id",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      customer_interface_type: "Customer Interface",
      supplier_interface_type: "Supplier Interface",
      customer_billing_logic_multiple: "Customer Protocol",
      supplier_billing_logic_multiple: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      visiting_operator: "Visiting Operator",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "customer_termination_credit_details_multiple",
      fieldName: {
        supplier_billing_count_multiple: "Messages Terminated",
        final_delivery_unit_cost_round: "Termination Rate",
        // supplier_exchange_rate: "Exchange Rate",
        final_delivery_unit_cost_euro: "Termination Rate (EUR)",
        termination_charges_euro_multiple: "Termination Credit (EUR)",
      },
    },
  },
  "Supplier Termination Credit Details": {
    name: "getSupplierTerminationCreditDetails",
    normalizedName: "SupplierTerminationCreditDetails",
    weeklyMonthlyReportName: "supplier_termination_credit_details",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      dest_nop_id: "Destination NOP ID",
      supplier_interconnect: "Supplier Interconnect",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      dest_prime: "Destination Prime",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      supplier_timezone: "Supplier Timezone",
      dest_hub: "Destination Hub",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator_id: "Visiting Routing ID",
      visiting_operator: "Visiting Operator",
      cdr_type_supplier: "Supplier CDR Type",
      supplier_billing_count_final: "Messages Terminated",
      billing_option: "Billing Option",
      final_delivery_unit_cost_round: "Termination Rate",
      supplier_currency_new: "Currency",
      supplier_exchange_rate: "Exchange Rate",
      final_delivery_unit_cost_euro: "Termination Rate (EUR)",
      final_termination_charges_euro: "Termination Credit (EUR)",
      cost_start_date: "From Date",
      cost_end_date: "To Date",
      final_termination_charges: "Termination Fee",
      oracle_account_no: "Oracle ID",
      final_operator_name: "Final Operator Name",
    },
    searchFields: [
      "traffic_type_supplier",
      "supplier_timezone",
      "supplier_interconnect",
      "supplier",
      "supplier_bind",
      "destination_operator_name",
      "destination_country_name",
      "destination_operator_code",
      "supplier_currency_new",
      "dest_hub",
      "dest_prime",
      "final_operator_name",
      "billing_option",
      "cdr_type_supplier",
      "supplier_system_id",
    ],
    filters: {
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      dest_prime: "Destination Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
    },
    aggregationFieldMapping: {
      name: "supplier_termination_credit_details",
      fieldName: {
        supplier_billing_count_final: "Messages Terminated",
        final_delivery_unit_cost_round: "Termination Rate",
        // supplier_exchange_rate: "Exchange Rate",
        final_delivery_unit_cost_euro: "Termination Rate (EUR)",
        final_termination_charges_euro: "Termination Credit (EUR)",
      },
    },
  },
  "Processing Fee Invoice Summary": {
    name: "getProcessingFeeInvoiceSummary",
    normalizedName: "ProcessingFeeInvoiceSummary",
    weeklyMonthlyReportName: "processing_fee_invoice_summary",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_system_id: "Customer System ID",
      oracle_account_no: "Oracle ID",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Operator Name",
      destination_country_code: "Operator Country Code",
      destination_mcc_final: "Operator MCC",
      destination_mnc_final: "Operator MNC",
      traffic_type_customer: "Traffic Type",
      src_prime: " Source Prime",
      customer_billing_logic_count_final: "Submission messages",
      cust_message_fee_euro: "Revenue (EUR)",
      supplier_billing_count_final: "Termination Messages",
      final_termination_charges_euro: "Cost (EUR)",
      final_margin_euro: "Margin (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
    },
    searchFields: [
      "customer_name",
      "customer_system_id",
      "destination_operator_code",
      "destination_operator_name",
      "src_prime",
      "traffic_type_customer",
    ],
    filters: {
      customer_name: "Customer Name",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
    },
    otherFilters: {
      bilateral: ["destination_operator_name"],
      negative_report: "final_margin_euro <0",
    },
    aggregationFieldMapping: {
      name: "processing_fee_invoice_summary",
      fieldName: {
        customer_billing_logic_count_final: "Submission messages",
        cust_message_fee_euro: "Revenue (EUR)",
        supplier_billing_count_final: "Termination Messages",
        final_termination_charges_euro: "Cost (EUR)",
      },
      derivedFieldName: {
        final_margin_euro: "Margin (EUR)",
      },
    },
  },
  "Processing Fee Invoice Summary Multiple": {
    name: "getProcessingFeeInvoiceSummaryMultiple",
    normalizedName: "ProcessingFeeInvoiceSummaryMultiple",
    weeklyMonthlyReportName: "processing_fee_invoice_summary_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_system_id: "Customer System ID",
      oracle_account_no: "Oracle ID",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Operator Name",
      destination_country_code: "Operator Country Code",
      destination_mcc_final: "Operator MCC",
      destination_mnc_final: "Operator MNC",
      traffic_type_customer: "Traffic Type",
      src_prime: " Source Prime",
      customer_billing_logic_count_multiple: "Submission messages",
      cust_message_fee_euro_multiple: "Revenue (EUR)",
      supplier_billing_count_multiple: "Termination Messages",
      termination_charges_euro_multiple: "Cost (EUR)",
      margin_euro_multiple: "Margin (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
    },
    searchFields: [
      "customer_name",
      "customer_system_id",
      "destination_operator_code",
      "destination_operator_name",
      "src_prime",
      "traffic_type_customer",
    ],
    filters: {
      customer_name: "Customer Name",
      src_prime: "Source Prime",
      destination_operator_name: "Destination Operator",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
    },
    otherFilters: {
      bilateral: ["destination_operator_name"],
      negative_report: "margin_euro_multiple <0",
    },
    aggregationFieldMapping: {
      name: "processing_fee_invoice_summary_multiple",
      fieldName: {
        customer_billing_logic_count_multiple: "Submission messages",
        cust_message_fee_euro_multiple: "Revenue (EUR)",
        supplier_billing_count_multiple: "Termination Messages",
        termination_charges_euro_multiple: "Cost (EUR)",
      },
      derivedFieldName: {
        margin_euro_multiple: "Margin (EUR)",
      },
    },
  },
  "Summary of Customer Termination Charges": {
    name: "getSummaryOfCustomerTerminationCharges",
    normalizedName: "SummaryOfCustomerTerminationCharges",
    defaultFilters: {
      viewBy: "day",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_name: "Source Country Name",
      //"Source Country Code",
      traffic_type: "Traffic Type",
      /* "Prime",
      "CDR Type", */
      delivery_success: "Messages",
      message_fee: "Termination Charges",
      //"Currency",
      message_fee_in_euro: "Termination Charges(EUR)",
    },
    searchFields: [
      "source_operator_name",
      "source_country_name",
      "source_operator_name",
      "traffic_type",
    ],
  },
  "Customer Processing Fee Invoice Detail": {
    name: "getCustomerProcessingFeeInvoiceDetail",
    normalizedName: "CustomerProcessingFeeInvoiceDetail",
    weeklyMonthlyReportName: "customer_processing_fee_invoice_detail",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      category: "Category",
      carrier: "Carrier",
      vertical: "Vertical",
      customer_kam: "Customer KAM",
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_timezone: "Customer Timezone",
      src_routing_id: "Source Routing ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      src_prime: " Source Prime",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      traffic_type_customer: "Traffic Type",
      customer_interface_type: "Customer Interface Type",
      customer_interconnect: "Customer InterConnect",
      supplier_interconnect: "Supplier Interconnect",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      dest_nop_id: "Destination NOP ID",
      supplier: "Supplier Name",
      supplier_vertical: "Supplier Vertical",
      supplier_kam: "Supplier KAM",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      supplier_timezone: "Supplier Timezone",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      dest_prime: "Destination Prime",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator: "Visiting Operator Name",
      cdr_type_description: "CDR Type Description",
      bparty_billing_logic: "B-Party Billing",
      cdr_type_supplier: "CDR Type Supplier",
      billing_option: "Billing Option",
      source_mnp_supplier: "Source MNP",
      source_mnp_count: "Source MNP Count",
      destination_mnp_supplier: "Destination MNP",
      destination_mnp_count: "Destination MNP Count",
      customer_billing_logic_count_final: "Submission messages",
      cust_message_fee_euro: "Revenue (EUR)",
      unit_new_rate_euro: "Average Revenue (EUR)",
      supplier_billing_count_final: "Termination Messages",
      final_termination_charges_euro: "Cost (EUR)",
      final_delivery_unit_cost_euro: "Average Cost (EUR)",
      final_margin_euro: "Margin (EUR)",
      final_average_margin_euro: "Average Margin (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
      supplier_currency_new: "Supplier Currency",
      customer_currency_new: "Customer Currency",
      final_operator_name: "Final Operator Name",
      soure_mnp_cost_euro: "Source MNP Cost (EUR)",
      destination_mnp_cost_euro: "Destination MNP Cost (EUR)",
      hlr_count: "HRL Count",
      destination_hlr_cost_euro: "Dest HLR Cost (EUR)",
      hlr_supplier_name: "HRL supplier Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "supplier",
      "supplier_bind",
      "destination_country_name",
      "destination_operator_code",
      "destination_operator_name",
      "lcr_name",
      "spec_lcr",
      "source_operator_name",
      "source_country_name",
      "visiting_operator",
      "supplier_interface_type",
      "dest_prime",
      "src_prime",
      "src_hub",
      "dest_hub",
      "supplier_interface_type",
      "customer_interface_type",
      "customer_interconnect",
      "supplier_interconnect",
      "cdr_type_description",
      "supplier_currency_new",
      "customer_currency_new",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
      "supplier_vertical",
      "customer_kam",
      "supplier_kam",
      "billing_option",
      "bparty_billing_logic",
      "vertical",
      "supplier_system_id",
      "customer_system_id",
      "customer_timezone",
      "supplier_timezone",
      "traffic_type_customer",
      "traffic_type_supplier",
      "carrier",
      "category",
      "hlr_supplier_name",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
      negative_report: "final_margin_euro <0",
    },
    aggregationFieldMapping: {
      name: "customer_processing_fee_invoice_detail",
      fieldName: {
        source_mnp_count: "Source MNP Count",
        destination_mnp_count: "Destination MNP Count",
        customer_billing_logic_count_final: "Submission messages",
        cust_message_fee_euro: "Revenue (EUR)",
        unit_new_rate_euro: "Average Revenue (EUR)",
        supplier_billing_count_final: "Termination Messages",
        final_termination_charges_euro: "Cost (EUR)",
        final_delivery_unit_cost_euro: "Average Cost (EUR)",
        soure_mnp_cost_euro: "Source MNP Cost (EUR)",
        destination_mnp_cost_euro: "Destination MNP Cost (EUR)",
        hlr_count: "HRL Count",
        destination_hlr_cost_euro: "Dest HLR Cost (EUR)",
      },
      derivedFieldName: {
        final_margin_euro: "Margin (EUR)",
        final_average_margin_euro: "Average Margin (EUR)",
      },
    },
  },
  "Customer Processing Fee Invoice Detail Multiple": {
    name: "getCustomerProcessingFeeInvoiceDetailMultiple",
    normalizedName: "CustomerProcessingFeeInvoiceDetailMultiple",
    weeklyMonthlyReportName: "customer_processing_fee_invoice_detail_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      src_nop_id: "Source NOP ID",
      category: "Category",
      carrier: "Carrier",
      vertical: "Vertical",
      customer_kam: "Customer KAM",
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      customer_timezone: "Customer Timezone",
      src_routing_id: "Source Routing ID",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      src_prime: " Source Prime",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source Country Code",
      source_country_name: "Source Country Name",
      traffic_type_customer: "Traffic Type",
      customer_interface_type: "Customer Interface Type",
      customer_interconnect: "Customer InterConnect",
      supplier_interconnect: "Supplier Interconnect",
      destination_operator_code: "Destination Routing ID",
      destination_operator_name: "Destination Operator",
      visiting_operator_id: "Visiting Routing ID",
      dest_nop_id: "Destination NOP ID",
      supplier: "Supplier Name",
      supplier_vertical: "Supplier Vertical",
      supplier_kam: "Supplier KAM",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Supplier Interface Type",
      supplier_timezone: "Supplier Timezone",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      dest_prime: "Destination Prime",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      visiting_operator: "Visiting Operator Name",
      cdr_type_description_multiple: "CDR Type Description",
      bparty_billing_logic: "B-Party Billing",
      cdr_type_supplier_multiple: "CDR Type Supplier",
      billing_option: "Billing Option",
      source_mnp_supplier: "Source MNP",
      source_mnp_count: "Source MNP Count",
      destination_mnp_supplier: "Destination MNP",
      destination_mnp_count: "Destination MNP Count",
      customer_billing_logic_count_multiple: "Submission messages",
      cust_message_fee_euro_multiple: "Revenue (EUR)",
      unit_new_rate_euro: "Average Revenue (EUR)",
      supplier_billing_count_multiple: "Termination Messages",
      termination_charges_euro_multiple: "Cost (EUR)",
      final_delivery_unit_cost_euro: "Average Cost (EUR)",
      margin_euro_multiple: "Margin (EUR)",
      final_average_margin_euro: "Average Margin (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
      supplier_currency_new: "Supplier Currency",
      customer_currency_new: "Customer Currency",
      final_operator_name: "Final Operator Name",
      soure_mnp_cost_euro: "Source MNP Cost (EUR)",
      destination_mnp_cost_euro: "Destination MNP Cost (EUR)",
      hlr_count: "HRL Count",
      destination_hlr_cost_euro: "Dest HLR Cost (EUR)",
      hlr_supplier_name: "HRL supplier Name",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "supplier",
      "supplier_bind",
      "destination_country_name",
      "destination_operator_code",
      "destination_operator_name",
      "lcr_name",
      "spec_lcr",
      "source_operator_name",
      "source_country_name",
      "visiting_operator",
      "supplier_interface_type",
      "dest_prime",
      "src_prime",
      "src_hub",
      "dest_hub",
      "supplier_interface_type",
      "customer_interface_type",
      "customer_interconnect",
      "supplier_interconnect",
      "cdr_type_description_multiple",
      "cdr_type_supplier_multiple",
      "supplier_currency_new",
      "customer_currency_new",
      "final_operator_name",
      "source_mnp_supplier",
      "destination_mnp_supplier",
      "supplier_vertical",
      "customer_kam",
      "supplier_kam",
      "billing_option",
      "bparty_billing_logic",
      "vertical",
      "supplier_system_id",
      "customer_system_id",
      "customer_timezone",
      "supplier_timezone",
      "traffic_type_customer",
      "traffic_type_supplier",
      "carrier",
      "category",
      "hlr_supplier_name",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      only_roaming:
        "CAST(destination_operator_code AS Int64) != visiting_operator_id",
      only_direct:
        "CAST(destination_operator_code AS Int64) = visiting_operator_id",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
      negative_report: "margin_euro_multiple <0",
    },
    aggregationFieldMapping: {
      name: "customer_processing_fee_invoice_detail_multiple",
      fieldName: {
        source_mnp_count: "Source MNP Count",
        destination_mnp_count: "Destination MNP Count",
        customer_billing_logic_count_multiple: "Submission messages",
        cust_message_fee_euro_multiple: "Revenue (EUR)",
        unit_new_rate_euro: "Average Revenue (EUR)",
        supplier_billing_count_multiple: "Termination Messages",
        termination_charges_euro_multiple: "Cost (EUR)",
        final_delivery_unit_cost_euro: "Average Cost (EUR)",
        soure_mnp_cost_euro: "Source MNP Cost (EUR)",
        destination_mnp_cost_euro: "Destination MNP Cost (EUR)",
        hlr_count: "HRL Count",
        destination_hlr_cost_euro: "Dest HLR Cost (EUR)",
      },
      derivedFieldName: {
        margin_euro_multiple: "Margin (EUR)",
        final_average_margin_euro: "Average Margin (EUR)",
      },
    },
  },

  "REVENUE Summary": {
    name: "getRevenueSummary",
    normalizedName: "RevenueSummary",
    weeklyMonthlyReportName: "revenue_summary",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      oracle_account_no: "Oracle Id",
      src_prime: "Source Prime",
      customer_currency_new: "Currency",
      customer_billing_logic_count_final: "Messages Processed",
      cust_message_fee: "Messaging Fee",
      customer_interconnect: "Customer Tagging",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "src_prime",
      "customer_interconnect",
      "customer_currency_new",
    ],
    filters: {
      src_prime: "Source Prime",
      customer_name: "Customer Name",
      customer_interconnect: "Customer Interconnect",
    },
    aggregationFieldMapping: {
      name: "revenue_summary",
      fieldName: {
        customer_billing_logic_count_final: "Messages Processed",
        cust_message_fee: "Messaging Fee",
      },
    },
  },

  "REVENUE Summary Multiple": {
    name: "getRevenueSummaryMultiple",
    normalizedName: "RevenueSummaryMultiple",
    weeklyMonthlyReportName: "revenue_summary_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      oracle_account_no: "Oracle Id",
      src_prime: "Source Prime",
      customer_currency_new: "Currency",
      customer_billing_logic_count_multiple: "Messages Processed",
      cust_message_fee_multiple: "Messaging Fee",
      customer_interconnect: "Customer Tagging",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "src_prime",
      "customer_interconnect",
      "customer_currency_new",
    ],
    filters: {
      src_prime: "Source Prime",
      customer_name: "Customer Name",
      customer_interconnect: "Customer Interconnect",
    },
    aggregationFieldMapping: {
      name: "revenue_summary_multiple",
      fieldName: {
        customer_billing_logic_count_multiple: "Messages Processed",
        cust_message_fee_multiple: "Messaging Fee",
      },
    },
  },

  "COST Summary": {
    name: "getCostSummary",
    normalizedName: "CostSummary",
    category: "billing",
    weeklyMonthlyReportName: "cost_summary",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier Name",
      oracle_account_no: "Oracle Id",
      dest_prime: "Destination Prime",
      supplier_currency_new: "Currency",
      supplier_billing_count_final: "Messages Terminated",
      final_termination_charges: "Termination Fee",
      supplier_interconnect: "Customer Tagging",
    },
    searchFields: [
      "supplier",
      "dest_prime",
      "supplier_currency_new",
      "supplier_interconnect",
    ],
    filters: {
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_interconnect: "Supplier Interconnect",
    },
    aggregationFieldMapping: {
      name: "cost_summary",
      fieldName: {
        supplier_billing_count_final: "Messages Terminated",
        final_termination_charges: "Termination Fee",
      },
    },
  },

  "COST Summary Multiple": {
    name: "getCostSummaryMultiple",
    normalizedName: "CostSummaryMultiple",
    category: "billing",
    weeklyMonthlyReportName: "cost_summary_multiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier Name",
      oracle_account_no: "Oracle Id",
      dest_prime: "Destination Prime",
      supplier_currency_new: "Currency",
      supplier_billing_count_multiple: "Messages Terminated",
      termination_charges_multiple: "Termination Fee",
      supplier_interconnect: "Customer Tagging",
    },
    searchFields: [
      "supplier",
      "dest_prime",
      "supplier_currency_new",
      "supplier_interconnect",
    ],
    filters: {
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_interconnect: "Supplier Interconnect",
    },
    aggregationFieldMapping: {
      name: "cost_summary_multiple",
      fieldName: {
        supplier_billing_count_multiple: "Messages Terminated",
        termination_charges_multiple: "Termination Fee",
      },
    },
  },
  "Prepaid Settlement Report": {
    name: "getPrepaidSettlementReport",
    normalizedName: "PrepaidSettlementReport",
    category: "billing",
    weeklyMonthlyReportName: "prepaid_settlement_report",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      timestamp: "Date",
      customer_name: "Customer Name",
      oracle_name: "Oracle Customer Name",
      oracle_account_no: "Oracle Account No",
      customer_currency_new: "Message Fee Currency",
      segment: "Segment",
      customer_billing_logic_count_final: "Submission Messages",
      supplier_billing_count_final: "Termination Messages",
      unit_new_rate_round: "Message Fee",
      unit_new_rate_euro: "Message Fee (EUR)",
      final_delivery_unit_cost_round: "Termination Credit",
      final_delivery_unit_cost_euro: "Termination Credit (EUR)",
      final_net_message_fee: "Net Message Fee",
      final_average_margin_euro: "Net Message Fee (EUR)",
    },
    searchFields: [
      "customer_name",
      "oracle_name",
      "customer_currency_new",
      "segment",
    ],
    filters: {
      customer_name: "Customer Name",
    },
    aggregationFieldMapping: {
      name: "prepaid_settlement_report",
      fieldName: {
        customer_billing_logic_count_final: "Submission Messages",
        supplier_billing_count_final: "Termination Messages",
        unit_new_rate_round: "Message Fee",
        unit_new_rate_euro: "Message Fee (EUR)",
        final_delivery_unit_cost_round: "Termination Credit",
        final_delivery_unit_cost_euro: "Termination Credit (EUR)",
      },
      derivedFieldName: {
        final_net_message_fee: "Net Message Fee",
        final_average_margin_euro: "Net Message Fee (EUR)",
      },
    },
  },
  "Prepaid Settlement Report Multiple": {
    name: "getPrepaidSettlementReportMultiple",
    normalizedName: "PrepaidSettlementReportMultiple",
    category: "billing",
    weeklyMonthlyReportName: "prepaid_settlement_report_multiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      timestamp: "Date",
      customer_name: "Customer Name",
      oracle_name: "Oracle Customer Name",
      oracle_account_no: "Oracle Account No",
      customer_currency_new: "Message Fee Currency",
      segment: "Segment",
      customer_billing_logic_count_multiple: "Submission Messages",
      supplier_billing_count_multiple: "Termination Messages",
      unit_new_rate_round: "Message Fee",
      unit_new_rate_euro: "Message Fee (EUR)",
      final_delivery_unit_cost_round: "Termination Credit",
      final_delivery_unit_cost_euro: "Termination Credit (EUR)",
      final_net_message_fee: "Net Message Fee",
      final_average_margin_euro: "Net Message Fee (EUR)",
    },
    searchFields: [
      "customer_name",
      "oracle_name",
      "customer_currency_new",
      "segment",
    ],
    filters: {
      customer_name: "Customer Name",
    },
    aggregationFieldMapping: {
      name: "prepaid_settlement_report_multiple",
      fieldName: {
        customer_billing_logic_count_multiple: "Submission Messages",
        supplier_billing_count_multiple: "Termination Messages",
        unit_new_rate_round: "Message Fee",
        unit_new_rate_euro: "Message Fee (EUR)",
        final_delivery_unit_cost_round: "Termination Credit",
        final_delivery_unit_cost_euro: "Termination Credit (EUR)",
      },
      derivedFieldName: {
        final_net_message_fee: "Net Message Fee",
        final_average_margin_euro: "Net Message Fee (EUR)",
      },
    },
  },

  "Cost Dump": {
    name: "getCostDump",
    normalizedName: "CostDump",
    category: "billing",
    weeklyMonthlyReportName: "cost_dump",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      traffic_type_supplier: "Traffic Type",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Operator",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      cost_start_date: "Cost From",
      cost_end_date: "Cost To",
      final_delivery_unit_cost_round: "Cost",
      supplier_currency_new: "Currency",
      cost_remark: "Remark",
      supplier_system_id: "System ID",
    },
    searchFields: [
      "supplier",
      "supplier_bind",
      "destination_operator_name",
      "supplier_currency_new",
      "destination_operator_code",
      "traffic_type_supplier",
      "supplier_system_id",
      "cost_remark",
    ],
    filters: {
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      bilateral: "Bilateral",
    },
    otherFilters: {
      bilateral: ["destination_operator_name"],
    },
    aggregationFieldMapping: {
      name: "cost_dump",
      fieldName: {
        final_delivery_unit_cost_round: "Cost",
      },
    },
  },
  "Revenue Report": {
    name: "getRevenueReport",
    normalizedName: "RevenueReport",
    weeklyMonthlyReportName: "revenue_report",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      traffic_type_customer: "Traffic Type",
      destination_operator_code: "Operator ID",
      destination_operator_name: "Operator",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      rate_from_date: "Revenue From",
      rate_to_date: "Revenue To",
      unit_new_rate_round: "Revenue",
      customer_currency_new: "Currency",
      rate_remark: "Remark",
      customer_system_id: "System ID",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "traffic_type_customer",
      "destination_operator_name",
      "destination_operator_code",
      "customer_currency_new",
      "rate_remark",
      "customer_system_id",
    ],
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      destination_operator_name: "Destination Operator",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
    },
    otherFilters: {
      bilateral: ["destination_operator_name"],
    },
    aggregationFieldMapping: {
      name: "revenue_report",
      fieldName: {
        unit_new_rate_round: "Revenue",
      },
    },
  },

  "Network Reports": {
    name: "getNetworkReport",
    normalizedName: "NetworkReport",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      // "sno_hourly": "SNO",
      stp: "STP Name",
      cdr_filename: "CDR File Name",
      timestamp: "Date",
      success_sms: "Success SMS",
      failure_sms: "Fail SMS",
      total_sms: "Total SMS",
    },
    calculatedFields: {
      s_no: "No.",
      first_seq_no: "First Sequence Number",
      last_seq_no: "Last Sequence Number",
    },
    searchFields: ["stp", "cdr_filename"],
  },

  "Network Reports Multiple": {
    name: "getNetworkReportMultiple",
    normalizedName: "NetworkReportMultiple",
    defaultFilters: {
      viewBy: "hour",
      useDefaultViewBy: true,
    },
    responseFields: {
      // "sno_hourly": "SNO",
      stp: "STP Name",
      cdr_filename: "CDR File Name",
      timestamp: "Date",
      success_sms_multiple: "Success SMS",
      failure_sms_multiple: "Fail SMS",
      total_sms_multiple: "Total SMS",
    },
    calculatedFields: {
      s_no: "No.",
      first_seq_no: "First Sequence Number",
      last_seq_no: "Last Sequence Number",
    },
    searchFields: ["stp", "cdr_filename"],
  },

  "Customer Reconciliation Report": {
    name: "getCustomerReconciliationReport",
    normalizedName: "CustomerReconciliationReport",
    weeklyMonthlyReportName: "customer_reconciliation_report",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_type: "Customer Type",
      traffic_type_customer: "Traffic Type",
      customer_billing_logic: "Billing Logic",
      customer_billing_logic_count_final: "Processed CDR",
    },
    searchFields: [
      "customer_name",
      "customer_type",
      "traffic_type_customer",
      "customer_billing_logic",
    ],
    filters: {
      customer_name: "Customer Name",
      traffic_type_customer: "Customer Protocol",
      customer_billing_logic: "Customer Billing Logic",
    },
    aggregationFieldMapping: {
      name: "customer_reconciliation_report",
      fieldName: {
        customer_billing_logic_count_final: "Processed CDR",
      },
    },
  },
  "Customer Reconciliation Report Multiple": {
    name: "getCustomerReconciliationReportMultiple",
    normalizedName: "CustomerReconciliationReportMultiple",
    weeklyMonthlyReportName: "customer_reconciliation_report_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Customer Name",
      customer_type: "Customer Type",
      traffic_type_customer: "Traffic Type",
      customer_billing_logic_multiple: "Billing Logic",
      customer_billing_logic_count_multiple: "Processed CDR",
    },
    searchFields: [
      "customer_name",
      "customer_type",
      "traffic_type_customer",
      "customer_billing_logic_multiple",
    ],
    filters: {
      customer_name: "Customer Name",
      traffic_type_customer: "Customer Protocol",
      customer_billing_logic_multiple: "Customer Billing Logic",
    },
    aggregationFieldMapping: {
      name: "customer_reconciliation_report_multiple",
      fieldName: {
        customer_billing_logic_count_multiple: "Processed CDR",
      },
    },
  },
  "Supplier Reconciliation Report": {
    name: "getSupplierReconciliationReport",
    normalizedName: "SupplierReconciliationReport",
    category: "billing",
    weeklyMonthlyReportName: "supplier_reconciliation_report",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier Name",
      supplier_type: "Supplier Type",
      traffic_type_supplier: "Traffic Type",
      supplier_billing_logic: "Supplier Billing Logic",
      supplier_billing_count_final: "Processed CDR",
    },
    searchFields: [
      "supplier",
      "supplier_type",
      "traffic_type_supplier",
      "supplier_billing_logic",
    ],
    filters: {
      supplier: "Supplier Name",
      traffic_type_supplier: "Supplier Protocol",
      supplier_billing_logic: "Supplier Billing Logic",
    },
    aggregationFieldMapping: {
      name: "supplier_reconciliation_report",
      fieldName: {
        supplier_billing_count_final: "Processed CDR",
      },
    },
  },
  "Supplier Reconciliation Report Multiple": {
    name: "getSupplierReconciliationReportMultiple",
    normalizedName: "SupplierReconciliationReportMultiple",
    category: "billing",
    weeklyMonthlyReportName: "supplier_reconciliation_report_multiple",
    defaultFilters: {
      viewBy: "day",
    },
    responseFields: {
      timestamp: "Date",
      supplier: "Supplier Name",
      supplier_type: "Supplier Type",
      traffic_type_supplier: "Traffic Type",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
      supplier_billing_count_multiple: "Processed CDR",
    },
    searchFields: [
      "supplier",
      "supplier_type",
      "traffic_type_supplier",
      "supplier_billing_logic_multiple",
    ],
    filters: {
      supplier: "Supplier Name",
      traffic_type_supplier: "Supplier Protocol",
      supplier_billing_logic_multiple: "Supplier Billing Logic",
    },
    aggregationFieldMapping: {
      name: "supplier_reconciliation_report_multiple",
      fieldName: {
        supplier_billing_count_multiple: "Processed CDR",
      },
    },
  },
  Billing: {
    name: "getBilling",
    normalizedName: "Billing",
    weeklyMonthlyReportName: "billing",
    category: "billing",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      stp: "STP Name",
      cdr_filename: "CDR File Name",
      count_successful_delivery_final: "Success Delivered SMS",
      success_sms: "Actual Billed SMS Count",
      billing_error: "Billing Error",
      failure_reason_final: "Reason For Billing Failure",
    },
    searchFields: ["stp", "cdr_filename", "failure_reason_final"],
    aggregationFieldMapping: {
      name: "billing",
      fieldName: {
        count_successful_delivery_final: "Success Delivered SMS",
        success_sms: "Actual Billed SMS Count",
        billing_error: "Billing Error",
      },
    },
  },
  "Billing Multiple": {
    name: "getBillingMultiple",
    normalizedName: "BillingMultiple",
    weeklyMonthlyReportName: "billing_multiple",
    category: "billing",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      stp: "STP Name",
      cdr_filename: "CDR File Name",
      count_successful_delivery_final: "Success Delivered SMS",
      success_sms_multiple: "Actual Billed SMS Count",
      billing_error_multiple: "Billing Error",
      failure_reason_final: "Reason For Billing Failure",
    },
    searchFields: ["stp", "cdr_filename", "failure_reason_final"],
    aggregationFieldMapping: {
      name: "billing_multiple",
      fieldName: {
        count_successful_delivery_final: "Success Delivered SMS",
        success_sms_multiple: "Actual Billed SMS Count",
        billing_error_multiple: "Billing Error",
      },
    },
  },
  "Customer Msg Fee Invoice Details Zero Rated": {
    name: "getCustomerMsgFeeInvoiceDetailsZeroRated",
    normalizedName: "CustomerMsgFeeInvoiceDetailsZeroRated",
    defaultFilters: {
      viewBy: "day",
      useDefaultViewBy: true,
    },
    responseFields: {
      timestamp: "Date",
      customer_name: "Source Customer",
      customer_bind: "Customer Bind",
      source_operator_name: "Source Operator",
      source_operator_code: "Source Operator ID",
      source_country_name: "Source Country",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      traffic_type: "Traffic Type",
      destination_operator_name: "Destination Operator",
      destination_operator_code: "Destination Op ID",
      visiting_operator: "Destination Op Visited",
      visiting_operator_id: "Destination Op Visited ID",
      destination_country_name: "Destination Country",
      destination_mcc: "Destination MCC",
      destination_mnc: "Destination MNC",
      cdr_type_description: "CDR Type Description",
      delivery_success_cust_billing: "Messages Processed",
      unit_rate_new: "Unit Rate",
      message_fee_customer: "Messaging fee",
      unit_currency: "Currency",
      message_fee_customer_euro: "Messaging fee(EUR)",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "source_country_name",
      "destination_operator_name",
      "destination_country_name",
      "traffic_type",
    ],
  },
  "Customer Termination Credit Details Zero Rated": {
    name: "getCustomerTerminationCreditDetailsZeroRated",
    normalizedName: "CustomerTerminationCreditDetailsZeroRated",
    defaultFilters: {
      viewBy: "day",
      useDefaultViewBy: true,
    },

    responseFields: {
      timestamp: "Date",
      customer_name: "Source Customer Name",
      customer_bind: "Customer Bind",
      source_hub: "Source Hub",
      source_operator_code: "Source Operator ID",
      source_operator_name: "Source Operator Name",
      source_country_name: "Source Country Name",
      source_country_code: "Source CC",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      traffic_type: "Traffic Type",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      traffic_type: "Supplier Traffic Type",
      destination_hub: "Destination Hub",
      destination_operator_code: "Destination Operator ID",
      destination_operator_name: "Destination Operator",
      destination_country_code: "Destination Country Code",
      destination_country_name: "Destination Country",
      destination_mcc: "Destination MCC",
      destination_mnc: "Destination MNC",
      visiting_operator_id: "Visiting Operator ID",
      visiting_operator: "Visiting Operator",
      cdr_type_supplier_multiple: "CDR Type",
      delivery_success_supp_billing: "Messages Terminated",
      unit_cost_of_delivery: "Termination Rate",
      supplier_currency: "Currency",
      unit_cost_of_delivery_in_euro: "Termination Rate(EUR)",
      termination_charges_new_in_euro: "Termination Credit(EUR)",
      cost_from_date: "From Date",
      cost_to_date: "To Date",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "source_country_name",
      "destination_operator_name",
      "source_operator_name",
      "destination_country_name",
    ],
  },
  "Supplier Wise Cost Report": {
    name: "Get Supplier Wise Cost",
    apiEndpoint: "/api/v4/getdaysupplyerwisecost",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "day",
      roleSearch: "supplier",
    },
    responseFields: {
      supplier: "Supplier",
      supplier_kam: "Supplier Kam",
      final_termination_charges_euro: "Termination Fee (Euro)",
    },
    searchFields: ["supplier", "supplier_kam"],
    filters: {
      supplier: "Supplier",
      supplier_kam: "Airtel KAM",
    },
    aggregationFieldMapping: {
      name: "supplier_wise_cost_report",
      fieldName: {
        final_termination_charges_euro: "Termination Fee (Euro)",
      },
    },
  },
  "Supplier Wise Cost Report Multiple": {
    name: "Get Supplier Wise Cost Multiple",
    apiEndpoint: "/api/v4/getdaysupplyerwisecost",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "day",
      roleSearch: "supplier",
    },
    responseFields: {
      supplier: "Supplier",
      supplier_kam: "Supplier Kam",
      termination_charges_euro_multiple: "Termination Fee (Euro)",
    },
    searchFields: ["supplier", "supplier_kam"],
    filters: {
      supplier: "Supplier Name",
      supplier_kam: "Airtel KAM",
    },
    aggregationFieldMapping: {
      name: "supplier_wise_cost_report_multiple",
      fieldName: {
        termination_charges_euro_multiple: "Termination Fee (Euro)",
      },
    },
  },
  "Customer Wise Revenue Report": {
    name: "Get Customer Wise Revenue",
    apiEndpoint: "/api/v4/getdaycustomerwisecost",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "day",
      roleSearch: "customer",
    },
    responseFields: {
      customer: "Customer Name",
      customer_kam: "Customer Kam",
      cust_message_fee_euro: "Revenue (EUR)",
    },
    searchFields: ["customer_name", "customer_kam"],
    filters: {
      customer_name: "Customer Name",
      customer_kam: "Airtel KAM",
    },
    aggregationFieldMapping: {
      name: "customer_wise_revenue_report",
      fieldName: {
        cust_message_fee_euro: "Revenue (EUR)",
      },
    },
  },
  "Customer Wise Revenue Report Multiple": {
    name: "Get Customer Wise Revenue Multiple",
    apiEndpoint: "/api/v4/getdaycustomerwisecost",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "day",
      roleSearch: "customer",
    },
    responseFields: {
      customer: "Customer Name",
      customer_kam: "Customer Kam",
      cust_message_fee_euro_multiple: "Revenue (EUR)",
    },
    searchFields: ["customer_name", "customer_kam"],
    filters: {
      customer_name: "Customer Name",
      customer_kam: "Airtel KAM",
    },
    aggregationFieldMapping: {
      name: "customer_wise_revenue_report_multiple",
      fieldName: {
        cust_message_fee_euro_multiple: "Revenue (EUR)",
      },
    },
  },

  "WhatsApp Traffic Report": {
    name: "wa_report",
    apiEndpoint: "/api/v1/getmmreports",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
    },
    aggregationFieldMapping: {
      name: "wa_report",
      fieldName: {
        sms_sent: "Sum of Attempt",
        success_sms: "Sum of Success",
        convertion_rate_1hr: "Average of CR%",
        best_convertion_rate_1hr: "Average of Best Route CR%",
        convertion_rate_24hr: "Average of CR% 24h",
        best_convertion_rate_24hr: "Average of Best Route CR% 24h",
        difference_from_best: "Average of Price (Delivery gap%)",
      },
    },
  },

  "Facebook Traffic Report": {
    name: "fb_report",
    apiEndpoint: "/api/v1/getmmreports",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
    },
    aggregationFieldMapping: {
      name: "fb_report",
      fieldName: {
        sms_sent: "Sum of Attempt",
        success_sms: "Sum of Success",
        convertion_rate_1hr: "Average of CR%",
        best_convertion_rate_1hr: "Average of Best Route CR%",
        convertion_rate_24hr: "Average of CR% 24h",
        best_convertion_rate_24hr: "Average of Best Route CR% 24h",
        difference_from_best: "Average of Price (Delivery gap%)",
      },
    },
  },

  "Slab Based Billing Report (Customer)": {
    timeZoneMapping: {
      "Asia/Kolkata": "customer_tier_based_billing_view",
    },
    apiEndpoint: "/api/v1/getTierBasedBilling",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
      roleSearch: "customer",
    },
    responseFields: {
      "Date": "Date",
      "Source NOP ID": "Source NOP ID",
      "Category": "Category",
      "Carrier": "Carrier",
      "Vertical": "Vertical",
      "Customer KAM": "Customer KAM",
      "Customer Name": "Customer Name",
      "Customer Bind": "Customer Bind",
      "Customer System ID": "Customer System ID",
      "Customer Timezone": "Customer Timezone",
      "Source Routing ID": "Source Routing ID",
      "Source Operator Name": "Source Operator Name",
      "Source Hub": "Source Hub",
      "Source Prime": "Source Prime",
      "Source MCC": "Source MCC",
      "Source MNC": "Source MNC",
      "Source Country Code": "Source Country Code",
      "Source Country Name": "Source Country Name",
      "Customer Traffic Type": "Customer Traffic Type",
      "Customer Interface Type": "Customer Interface Type",
      "Customer Interconnect": "Customer Interconnect",
      "Supplier Interconnect": "Supplier Interconnect",
      "Destination Routing ID": "Destination Routing ID",
      "Destination Operator": "Destination Operator",
      "Visiting Operator ID": "Visiting Operator ID",
      "Destination NOP ID": "Destination NOP ID",
      "LCR Name": "LCR Name",
      "Spec LCR": "Spec LCR",
      "Destination Hub": "Destination Hub",
      "Destination Prime": "Destination Prime",
      "Destination Country Code": "Destination Country Code",
      "Destination Country Name": "Destination Country Name",
      "Destination MCC": "Destination MCC",
      "Destination MNC": "Destination MNC",
      "Visiting Operator Name": "Visiting Operator Name",
      "CDR Type Description": "CDR Type Description",
      "B-Party Billiing": "B-Party Billiing",
      "CDR Type Supplier": "CDR Type Supplier",
      "Supplier Billing Option": "Supplier Billing Option",
      "Submission Messages": "Submission Messages",
      "Source MNP": "Source MNP",
      "Source MNP Count": "Source MNP Count",
      "Destination MNP": "Destination MNP",
      "Destination MNP Count": "Destination MNP Count",
      "Slab-Limits": "Slab-Limits",
      "Billing Model": "Billing Model",
      "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
      "MRC": "MRC",
      "Fixed Fee/OTC": "Fixed Fee/OTC",
      "Total per SMS Charges": "Total per SMS Charges",
      "Total Messaging Fee": "Total Messaging Fee",
      "Currency": "Currency",
      "Message Fee(EUR)": "Message Fee(EUR)",
      "Rate From": "Rate From",
      "Rate To": "Rate To",
    },
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      customer_interface_type: "Customer Interface Type",
      customer_traffic_type: "Customer Traffic Type",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "CDR Type Description",
    },
    otherFilters: {
      only_roaming:
        "(toString(`Destination Routing ID`) != toString(`Visiting Operator ID`))",
      only_direct:
        "(toString(`Destination Routing ID`) = toString(`Visiting Operator ID`))",
      both: true,
      bilateral: ["Destination Country Name", "Destination Operator"],
    },
    aggregationFieldMapping: {
      name: "customer_tier_based_billing_view",
      fieldName: {
        "Submission Messages": "Submission Messages",
        "Source MNP Count": "Source MNP Count",
        "Destination MNP Count": "Destination MNP Count",
        "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
        MRC: "MRC",
        "Fixed Fee/OTC": "Fixed Fee/OTC",
        "Total per SMS Charges": "Total per SMS Charges",
        "Total Messaging Fee": "Total Messaging Fee",
        "Message Fee(EUR)": "Message Fee(EUR)",
      },
    },
  },

  "Slab Based Billing Report (Customer) Multiple": {
    timeZoneMapping: {
      "Asia/Kolkata": "customer_tier_based_billing_view_multiple",
    },
    apiEndpoint: "/api/v1/getTierBasedBilling",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
      roleSearch: "customer",
    },
    responseFields: {
      "Date": "Date",
      "Source NOP ID": "Source NOP ID",
      "Category": "Category",
      "Carrier": "Carrier",
      "Vertical": "Vertical",
      "Customer KAM": "Customer KAM",
      "Customer Name": "Customer Name",
      "Customer Bind": "Customer Bind",
      "Customer System ID": "Customer System ID",
      "Customer Timezone": "Customer Timezone",
      "Source Routing ID": "Source Routing ID",
      "Source Operator Name": "Source Operator Name",
      "Source Hub": "Source Hub",
      "Source Prime": "Source Prime",
      "Source MCC": "Source MCC",
      "Source MNC": "Source MNC",
      "Source Country Code": "Source Country Code",
      "Source Country Name": "Source Country Name",
      "Customer Traffic Type": "Customer Traffic Type",
      "Customer Interface Type": "Customer Interface Type",
      "Customer Interconnect": "Customer Interconnect",
      "Supplier Interconnect": "Supplier Interconnect",
      "Destination Routing ID": "Destination Routing ID",
      "Destination Operator": "Destination Operator",
      "Visiting Operator ID": "Visiting Operator ID",
      "Destination NOP ID": "Destination NOP ID",
      "LCR Name": "LCR Name",
      "Spec LCR": "Spec LCR",
      "Destination Hub": "Destination Hub",
      "Destination Prime": "Destination Prime",
      "Destination Country Code": "Destination Country Code",
      "Destination Country Name": "Destination Country Name",
      "Destination MCC": "Destination MCC",
      "Destination MNC": "Destination MNC",
      "Visiting Operator Name": "Visiting Operator Name",
      "CDR Type Description": "CDR Type Description",
      "B-Party Billiing": "B-Party Billiing",
      "CDR Type Supplier": "CDR Type Supplier",
      "Supplier Billing Option": "Supplier Billing Option",
      "Submission Messages": "Submission Messages",
      "Source MNP": "Source MNP",
      "Source MNP Count": "Source MNP Count",
      "Destination MNP": "Destination MNP",
      "Destination MNP Count": "Destination MNP Count",
      "Slab-Limits": "Slab-Limits",
      "Billing Model": "Billing Model",
      "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
      "MRC": "MRC",
      "Fixed Fee/OTC": "Fixed Fee/OTC",
      "Total per SMS Charges": "Total per SMS Charges",
      "Total Messaging Fee": "Total Messaging Fee",
      "Currency": "Currency",
      "Message Fee(EUR)": "Message Fee(EUR)",
      "Rate From": "Rate From",
      "Rate To": "Rate To"
    },
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country Name",
      customer_interface_type: "Customer Interface Type",
      customer_traffic_type: "Customer Traffic Type",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "CDR Type Description",
    },
    otherFilters: {
      only_roaming:
        "(toString(`Destination Routing ID`) != toString(`Visiting Operator ID`))",
      only_direct:
        "(toString(`Destination Routing ID`) = toString(`Visiting Operator ID`))",
      both: true,
      bilateral: ["Destination Country Name", "Destination Operator"],
    },
    aggregationFieldMapping: {
      name: "customer_tier_based_billing_view_multiple",
      fieldName: {
        "Submission Messages": "Submission Messages",
        "Source MNP Count": "Source MNP Count",
        "Destination MNP Count": "Destination MNP Count",
        "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
        MRC: "MRC",
        "Fixed Fee/OTC": "Fixed Fee/OTC",
        "Total per SMS Charges": "Total per SMS Charges",
        "Total Messaging Fee": "Total Messaging Fee",
        "Message Fee(EUR)": "Message Fee(EUR)",
      },
    },
  },

  "Slab Based Billing Report (Supplier)": {
    timeZoneMapping: {
      "Asia/Kolkata": "supplier_tier_based_billing_view",
    },
    apiEndpoint: "/api/v1/getTierBasedBilling",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
      roleSearch: "supplier",
    },
    responseFields: {
      "Date": "Date",
      "Supplier": "Supplier",
      "Supplier Bind": "Supplier Bind",
      "Supplier System Id": "Supplier System Id",
      "Supplier Traffic Type": "Supplier Traffic Type",
      "Supplier Interface Type": "Supplier Interface Type",
      "Supplier Timezone": "Supplier Timezone",
      "Supplier Interconnect": "Supplier Interconnect",
      "Destination Routing ID": "Destination Routing ID",
      "Destination Operator": "Destination Operator",
      "Visiting Routing ID": "Visiting Routing ID",
      "Destination NOP ID": "Destination NOP ID",
      "LCR Name": "LCR Name",
      "Spec LCR": "Spec LCR",
      "Destination Hub": "Destination Hub",
      "Destination Prime": "Destination Prime",
      "Destination Country Code": "Destination Country Code",
      "Destination Country": "Destination Country",
      "Destination MCC": "Destination MCC",
      "Destination MNC": "Destination MNC",
      "Visiting Operator Name": "Visiting Operator Name",
      "CDR Type Description": "CDR Type Description",
      "B-Party Billing": "B-Party Billing",
      "CDR Type Supplier": "CDR Type Supplier",
      "Supplier Billing Option": "Supplier Billing Option",
      "Submission Messages": "Submission Messages",
      "Destination MNP": "Destination MNP",
      "Destination MNP Count": "Destination MNP Count",
      "Slab-Limits": "Slab-Limits",
      "Billing Model": "Billing Model",
      "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
      "MRC": "MRC",
      "Fixed Fee/OTC": "Fixed Fee/OTC",
      "Total per SMS Charges": "Total per SMS Charges",
      "Total Messaging Fee": "Total Messaging Fee",
      "Currency": "Currency",
      "Message Fee(EUR)": "Message Fee(EUR)",
      "Rate From": "Rate From",
      "Rate To": "Rate To"
    },
    filters: {
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_traffic_type: "Supplier Protocol",
      supplier_interface_type: "Supplier Interface",
      destination_operator_name: "Destination Operator",
      destination_prime: "Destination Prime",
      destination_country: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "CDR Type Description",
    },
    otherFilters: {
      only_roaming:
        "(toString(`Destination Routing ID`) != toString(`Visiting Routing ID`))",
      only_direct:
        "(toString(`Destination Routing ID`) = toString(`Visiting Routing ID`))",
      both: true,
      bilateral: ["Destination Operator", "Destination Country"],
    },
    aggregationFieldMapping: {
      name: "supplier_tier_based_billing_view",
      fieldName: {
        "Submission Messages": "Submission Messages",
        "Destination MNP Count": "Destination MNP Count",
        "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
        MRC: "MRC",
        "Fixed Fee/OTC": "Fixed Fee/OTC",
        "Total per SMS Charges": "Total per SMS Charges",
        "Total Messaging Fee": "Total Messaging Fee",
        "Message Fee(EUR)": "Message Fee(EUR)",
      },
    },
  },

  "Slab Based Billing Report (Supplier) Multiple": {
    timeZoneMapping: {
      "Asia/Kolkata": "supplier_tier_based_billing_view_multiple",
    },
    apiEndpoint: "/api/v1/getTierBasedBilling",
    isCustomReport: true,
    defaultFilters: {
      viewBy: "month",
      roleSearch: "supplier",
    },
    responseFields: {
      "Date": "Date",
      "Supplier": "Supplier",
      "Supplier Bind": "Supplier Bind",
      "Supplier System Id": "Supplier System Id",
      "Supplier Traffic Type": "Supplier Traffic Type",
      "Supplier Interface Type": "Supplier Interface Type",
      "Supplier Timezone": "Supplier Timezone",
      "Supplier Interconnect": "Supplier Interconnect",
      "Destination Routing ID": "Destination Routing ID",
      "Destination Operator": "Destination Operator",
      "Visiting Routing ID": "Visiting Routing ID",
      "Destination NOP ID": "Destination NOP ID",
      "LCR Name": "LCR Name",
      "Spec LCR": "Spec LCR",
      "Destination Hub": "Destination Hub",
      "Destination Prime": "Destination Prime",
      "Destination Country Code": "Destination Country Code",
      "Destination Country": "Destination Country",
      "Destination MCC": "Destination MCC",
      "Destination MNC": "Destination MNC",
      "Visiting Operator Name": "Visiting Operator Name",
      "CDR Type Description": "CDR Type Description",
      "B-Party Billing": "B-Party Billing",
      "CDR Type Supplier": "CDR Type Supplier",
      "Supplier Billing Option": "Supplier Billing Option",
      "Submission Messages": "Submission Messages",
      "Destination MNP": "Destination MNP",
      "Destination MNP Count": "Destination MNP Count",
      "Slab-Limits": "Slab-Limits",
      "Billing Model": "Billing Model",
      "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
      "MRC": "MRC",
      "Fixed Fee/OTC": "Fixed Fee/OTC",
      "Total per SMS Charges": "Total per SMS Charges",
      "Total Messaging Fee": "Total Messaging Fee",
      "Currency": "Currency",
      "Message Fee(EUR)": "Message Fee(EUR)",
      "Rate From": "Rate From",
      "Rate To": "Rate To"
    },
    filters: {
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_traffic_type: "Supplier Protocol",
      supplier_interface_type: "Supplier Interface",
      destination_operator_name: "Destination Operator",
      destination_prime: "Destination Prime",
      destination_country: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      supplier_interconnect: "Supplier Interconnect",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      customer_billing_logic: "CDR Type Description",
    },
    otherFilters: {
      only_roaming:
        "(toString(`Destination Routing ID`) != toString(`Visiting Routing ID`))",
      only_direct:
        "(toString(`Destination Routing ID`) = toString(`Visiting Routing ID`))",
      both: true,
      bilateral: ["Destination Operator", "Destination Country"],
    },
    aggregationFieldMapping: {
      name: "supplier_tier_based_billing_view_multiple",
      fieldName: {
        "Submission Messages": "Submission Messages",
        "Destination MNP Count": "Destination MNP Count",
        "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
        MRC: "MRC",
        "Fixed Fee/OTC": "Fixed Fee/OTC",
        "Total per SMS Charges": "Total per SMS Charges",
        "Total Messaging Fee": "Total Messaging Fee",
        "Message Fee(EUR)": "Message Fee(EUR)",
      },
    },
  },
  "Sms Firewall Report": {
    name: "Get SMS Firewall",
    apiEndpoint: "/api/v1/getsmsfirewallreport",
    isCustomReport: true,
    noStartEndDate: true,
    noViewBy: true,
    defaultFilters: {
      viewBy: "month",
      useDefaultViewBy: true,
      roleSearch: "supplier_bind",
    },
    responseFields: {
      supplier_carrier: "Supplier Carrier",
      supplier_bind: "Supplier Bind",
      total_deliveries: "Total Deliveries",
    },
    searchFields: ["supplier_carrier", "supplier_bind"],
    filters: {
      supplier_bind: "Supplier Bind",
    },
    aggregationFieldMapping: {
      name: "sms_firewall_report",
      fieldName: {
        total_deliveries: "Total Deliveries",
      },
    },
  },

  "A Number Report": {
    name: "getANumberReport",
    normalizedName: "ANumberReport",
    weeklyMonthlyReportName: "a_number_report",
    category: "billing",
    defaultFilters: {
      viewBy: "hour",
    },
    responseFields: {
      timestamp: "Date",
      billing_month: "Billing Month",
      customer_name: "Customer Name",
      a_number: "A Number",
      customer_bind: "Customer Bind",
      customer_system_id: "Customer System ID",
      source_operator_code: "Source Operator Code",
      source_operator_name: "Source Operator Name",
      src_hub: "Source Hub",
      src_prime: "Source Prime",
      source_mcc: "Source MCC",
      source_mnc: "Source MNC",
      source_country_code: "Source CC",
      source_country_name: "Source Country Name",
      traffic_type_customer: "Traffic Type Src",
      customer_interface_type: "Interface Type Source",
      destination_operator_code: "Destination Operator Code",
      visiting_operator: "Visiting Operator Name",
      dest_nop_id: "Destination NOP ID",
      supplier: "Supplier",
      supplier_bind: "Supplier Bind",
      supplier_system_id: "Supplier System ID",
      traffic_type_supplier: "Supplier Traffic Type",
      supplier_interface_type: "Interface Type Supplier",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      percentage_routing_final: "Percentage Routing",
      dest_hub: "Destination Hub",
      dest_prime: "Destination Prime",
      destination_country_code: "Destination CC",
      visited_opr_country: "Destination Country Name Roaming",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      customer_billing_logic_multiple: "Source Billing Logic",
      total_submissions: "Total Submission",
      submission_success: "Submission Success",
      customer_billing_logic_count_multiple: "Delivered SMS",
      next_hop_success_new: "Next Hop Success Submission",
      unit_new_rate_round: "Avg Revenue",
      cust_message_fee_euro_multiple: "Revenue (EUR)",
      unit_new_rate_euro: "Avg Revenue (EUR)",
      supplier_billing_count_multiple: "Messages Terminated",
      termination_charges_euro_multiple: "Cost (EUR)",
      final_delivery_unit_cost_euro: "Avg Cost (EUR)",
      margin_euro_multiple: "Margin (EUR)",
      final_average_margin_euro: "Avg Margin (EUR)",
      rate_from_date: "Rate From",
      rate_to_date: "Rate To",
    },
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_interface_type: "Supplier Interface",
      customer_interface_type: "Customer Interface",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
    },
    otherFilters: {
      bilateral: ["destination_operator_code", "destination_country_code"],
      negative_report: "margin_euro_multiple < 0",
    },
    searchFields: [
      "customer_name",
      "customer_bind",
      "supplier",
      "supplier_bind",
      "source_operator_code",
      "source_operator_name",
      "destination_operator_code",
      "lcr_name",
      "source_country_name",
      "visiting_operator",
      "a_number",
      "supplier_interface_type",
      "customer_interface_type",
      "src_hub",
      "src_prime",
      "dest_hub",
      "dest_prime",
      "visited_opr_country",
      "customer_system_id",
      "supplier_system_id",
      "spec_lcr",
      "billing_month",
      "traffic_type_customer",
      "traffic_type_supplier",
      "customer_billing_logic_multiple",
    ],
    aggregationFieldMapping: {
      name: "a_number_report",
      fieldName: {
        supplier_billing_count_multiple: "Messages Terminated",
        cust_message_fee_euro_multiple: "Revenue (EUR)",
        customer_billing_logic_count_multiple: "Delivered SMS",
        termination_charges_euro_multiple: "Cost (EUR)",
        unit_new_rate_euro: "Avg Revenue (EUR)",
        final_delivery_unit_cost_euro: "Avg Cost (EUR)",
        unit_new_rate_round: "Avg Revenue",
        percentage_routing_final: "Percentage Routing",
      },
    },
    derivedFieldName: {
      margin_euro_multiple: "Margin (EUR)",
      final_average_margin_euro: "Avg Margin (EUR)",
    },
  },
  "Dynamic Report": {
    name: "dynamicReport",
    // normalizedName: "ANumberReport",
    // weeklyMonthlyReportName: "a_number_report",
    responseFields: {},
    filters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      dest_prime: "Destination Prime",
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      supplier_interface_type: "Supplier Interface Type",
      customer_billing_logic: "Customer Billing Logic",
      supplier_billing_logic: "Supplier Billing Logic",
      supplier_interconnect: "Supplier Interconnect",
      customer_interconnect: "Customer Interconnect",
      customer_interface_type: "Customer Interface Type",
      traffic_type_customer: "Customer Protocol",
      traffic_type_supplier: "Supplier Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    customerFilters: {
      customer_name: "Customer Name",
      customer_bind: "Customer Bind",
      src_prime: "Source Prime",
      customer_interface_type: "Customer Interface Type",
      customer_billing_logic: "Customer Billing Logic",
      customer_interconnect: "Customer Interconnect",
      src_prime: "Source Prime",
      traffic_type_customer: "Customer Protocol",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    supplierFilters: {
      supplier: "Supplier Name",
      supplier_bind: "Supplier Bind",
      supplier_interface_type: "Supplier Interface Type",
      traffic_type_supplier: "Supplier Protocol",
      supplier_billing_logic: "Supplier Billing Logic",
      supplier_interconnect: "Supplier Interconnect",
      dest_prime: "Destination Prime",
      destination_operator_name: "Destination Operator",
      destination_country_name: "Destination Country",
      destination_mcc_final: "Destination MCC",
      destination_mnc_final: "Destination MNC",
      lcr_name: "LCR Name",
      spec_lcr: "Spec LCR",
      status: "Status",
    },
    searchFields: [
      "a_number",
      "b_number",
      "customer_bind",
      "customer_name",
      "customer_interface_type",
      "customer_billing_logic",
      "traffic_type_customer",
      "customer_interface_type",
      "customer_interconnect",
      "customer_currency_new",
      "customer_system_id",
      "customer_kam",
      "dest_imsi",
      "orig_imsi",
      "dest_prime",
      "src_prime",
      "source_ip",
      "destination_ip",
      "src_hub",
      "dest_hub",
      "visited_opr_country",
      "visiting_operator",
      "vertical",
      "destination_mcc_mnc_final",
      "destination_country_name",
      "destination_msg",
      "destination_operator_name",
      "destination_operator_code",
      "destination_mnp_supplier",
      "source_operator_code",
      "source_mcc_mnc",
      "source_operator_name",
      "source_country_name",
      "source_mnp_supplier",
      "lcr_name",
      "spec_lcr",
      "status",
      "supplier",
      "supplier_bind",
      "traffic_type_supplier",
      "supplier_billing_logic",
      "supplier_interface_type",
      "supplier_interconnect",
      "supplier_currency_new",
      "supplier_system_id",
      "supplier_kam",
      "content_category",
      "category",
      "carrier",
      "cdr_type_description",
      "cdr_type_supplier",
      "traffic_type",
    ],
    otherFilters: {
      only_roaming:
        "(CAST({destination_operator_code} AS Int64) != {visiting_operator_id})",
      only_direct:
        "(CAST({destination_operator_code} AS Int64) = {visiting_operator_id})",
      both: true,
      bilateral: ["destination_operator_name", "destination_country_name"],
      negative_report: "final_margin_euro <0",
    },
    aggregationFieldMapping: {
      name: "dashboard_chart_data",
      fieldName: {
        total_submissions: "Total Submission",
        submission_success: "Successful Submission",
        submission_error: "Submission Failure / Blocked Messages Count",
        total_deliveries: "Total Delivery",
        delivery_failure_count_final: "Error Count / Delivery Error",
        avg_submission_latency_final: "Average Submission Latency",
        average_latency_td_tr_final: "Average Hub Latency",
        average_latency_final: "Average Delivery latency",
        delivery_retries: "No. of Delievery retries",
        next_hop_success_new: "Next hop Success",
        number_of_records_new: "Count",
        unit_new_rate_round: "Customer Unit Rate",
        supplier_billing_count_final: "Message Terminated",
        customer_billing_logic_count_final: "Message Processed",
        final_delivery_unit_cost_round: "Supplier Unit Cost",
        delivery_retry: "DRC Retry Count",
        unit_new_rate_euro: "Average Revenue (EUR)",
        final_delivery_unit_cost_euro: "Average Cost (EUR)",
        final_margin_euro: "Margin (EUR)",
        true_delivery_efficiency: "True Delivery Efficiency"

      },
      derivedFieldName: {
        count_successful_delivery_final: "Successful Delivery",
        delivery_pending_final: "Pending Delivery",
        submission_efficiency: "Submission Efficiency(%)",
        final_delivery_efficiency: "Delivery Efficiency(%)",
        percentage_successful: "Success Percentage",
        percentage_failure: "Failure Percentage",
        next_hop_success_percent_final: "Next Hop Success Percent (%)",
        cust_message_fee_euro: "Messaging Fee (EUR)",
        final_termination_charges_euro: "Cost (EUR)",
        final_average_margin_euro: "Average Margin (EUR)",
      },
    },
  },
};

const customerList = {
  name: "getCustomerDetails",
  responseFields: {
    id: "id",
    name: "customer_bind",
    billing_contact: "name",
    billing_logic: "billing_logic",
    airtel_kam: "airtel_kam",
    protocol_type: "protocol_type",
    interface_type: "interface_type",
    ECPaaS: "ECPaaS",
  },
};

const supplierList = {
  name: "getSupplierDetails",
  responseFields: {
    id: "id",
    name: "supplier_bind",
    airtel_kam: "airtel_kam",
    billing_contact: "name",
    billing_logic: "billing_logic",
    protocol_type: "protocol_type",
    interface_type: "interface_type",
    ECPaaS: "ECPaaS",
  },
};
const destnationDetails = {
  name: "getOperatorDetails",
  responseFields: {
    id: "id",
    country_name: "country_name",
    operator_name: "operator_name",
    country_code: "country_code",
    bilateral_flag: "bilateral_flag",
  },
};
const LcDetails = {
  name: "getLcrDetails",
  responseFields: {
    lcr_policy_id: "id",
    lcr_name: "lcr_name",
    lcr_type: "lcr_type",
  },
};
const cdrStatusDetails = {
  name: "getCdrStatusDetails",
  responseFields: {
    id: "id",
    description: "description",
  },
};
const operatorDetails = {
  name: "getOperatorDetails",
  responseFields: {
    id: "id",
    bilateral_flag: "bilateral_flag",
    country_name: "country_name",
    operator_name: "operator_name",
  },
};
const destinationPrimeDetails = {
  name: "getDestinationPrimeDetails",
  responseFields: {
    id: "id",
    description: "description",
  },
};
const sourcePrimeDetails = {
  name: "getSourcePrimeDetails",
  responseFields: {
    id: "id",
    description: "description",
  },
};
const derivedReportFields = [
  "Total Submission",
  "Successful Submission",
  "Submission Failure / Blocked Messages Count",
  "Successful Delivery",
  "Delivery Failure",
  "Pending Delivery",
  "Delivery Error",
  "Success Percentage",
  "Submission Efficiency(%)",
  "Delivery Efficiency(%)",
  "Failure Percentage",
  "Next hop Success",
];
// changed Error Description to Error, VisitedOpr Country to Visiting Operator Name,Supplier Interface to Supplier Interface Type
//Merged Delivery error and Error count
//Added a new field "Content Category"

const columnMapping = {
  Datetime: "timestamp",
  "A Number": "a_number",
  "B Number": "b_number",
  // "Customer Id": null,
  "Customer Bind": "customer_bind",
  "Customer Interface": "customer_interface_type",
  "Customer Name": "customer_name",
  "Customer Protocol": "traffic_type_customer",
  // "Destination Interface": null,
  "Destination IMSI": "dest_imsi",
  "Destination MCC": "destination_mcc_final",
  "Destination MNC": "destination_mnc_final",
  "Destination Operator Country": "destination_country_name",
  "Destination Operator Name": "destination_operator_name",
  "Destination Operator Id": "destination_operator_code",
  "Origin IMSI": "orig_imsi",
  "Source Protocol": "source_protocol",
  "Source MCC": "source_mcc",
  "Source MNC": "source_mnc",
  "SourceOpr Country": "source_country_name",
  "Source MCCMNC": "source_mcc_mnc",
  "Source Operator Name": "source_operator_name",
  "Supplier Bind": "supplier_bind",
  "Supplier Interface Type": "supplier_interface_type",
  "Supplier Name": "supplier",
  "Supplier Protocol": "traffic_type_supplier",
  "Traffic Type": "traffic_type",
  "Customer Billing Logic": "customer_billing_logic",
  "Supplier Billing Logic": "supplier_billing_logic",
  "Destination Op Visited": "visiting_operator",
  "VisitedOpr Country": "visited_opr_country",
  // "Destination VMSC": null,
  // "Origin VMSC": null,
  "Destination Country Code": "destination_country_code",
  "Total Submission": "total_submissions",
  "Successful Submission": "submission_success",
  "Submission Failure / Blocked Messages Count": "submission_error",
  "Total Delivery": "total_deliveries",
  "Successful Delivery": "count_successful_delivery_final",
  "Delivery Failure": "delivery_failure_count_final",
  "Pending Delivery": "delivery_pending_final",
  "Submission Efficiency(%)": "submission_efficiency",
  "Delivery Efficiency(%)": "final_delivery_efficiency",
  "Average Submission Latency": "avg_submission_latency_final",
  "Average Hub Latency": "average_latency_td_tr_final",
  "Average Delivery latency": "average_latency_final",
  "No. of Delievery retries": "delivery_retries",
  "Error Description": "error_description",
  "Error Count / Delivery Error": "delivery_failure_count_final",
  "Success Percentage": "percentage_successful",
  "Failure Percentage": "percentage_failure",
  "Next hop Success": "next_hop_success_new",
  "Destination MSG": "destination_msg",
  Status: "status",
  "Destination Name": "destination",
  "Error Code": "error_code",
  "Event Date": "event_date",
  "Message ID": "event_id",
  "LCR Name": "lcr_name",
  "Result Code": "result_code",
  Supplier: "supplier",
  "Time of Arrival": "time_of_arrival",
  "Time of Delivery": "time_of_delivery",
  "Transaction Type": "transaction_type",
  Count: "number_of_records_new",
  "DestOpr MCCMNC": "destination_mcc_mnc_final",
  "Content Category": "content_category",
  "Source Prime": "src_prime",
  "Customer Interconnect": "customer_interconnect",
  "Supplier Interconnect": "supplier_interconnect",
  "Customer Unit Rate": "unit_new_rate_round",
  "customer currency": "customer_currency_new",
  "supplier currency": "supplier_currency_new",
  "Visiting Operator ID": "visiting_operator_id",
  "Source IP": "source_ip",
  "Destination IP": "destination_ip",
  "Source Operator Code": "source_operator_code",
  "Message Terminated": "supplier_billing_count_final",
  "Message Processed": "customer_billing_logic_count_final",
  "Supplier Unit Cost": "final_delivery_unit_cost_round",
  "Source MNP supplier": "source_mnp_supplier",
  "Destination MNP supplier": "destination_mnp_supplier",
  "DRC Retry Count": "delivery_retry",
  "Next Hop Success Percent (%)": "next_hop_success_percent_final",
  "Spec LCR": "spec_lcr",
  "Customer System ID": "customer_system_id",
  "Supplier System ID": "supplier_system_id",
  "Oracle ID": "oracle_account_no",
  "Source NOP ID": "src_nop_id",
  Category: "category",
  Carrier: "carrier",
  "Customer KAM": "customer_kam",
  "Source Routing ID": "src_routing_id",
  "Source Hub": "src_hub",
  "Source Country Code": "source_country_code",
  //"Customer Traffic Type": "traffic_type_customer",
  //"Supplier Traffic Type": "traffic_type_supplier",
  "Destination NOP ID": "dest_nop_id",
  "Percentage Routing": "percentage_routing_final",
  "Destination Hub": "dest_hub",
  "CDR Type Description": "cdr_type_description",
  "CDR Type Supplier": "cdr_type_supplier",
  "Destination Prime": "dest_prime",
  Vertical: "vertical",
  "Average Revenue (EUR)": "unit_new_rate_euro",
  "Cost (EUR)": "final_termination_charges_euro",
  "Messaging Fee (EUR)": "cust_message_fee_euro",
  "Average Cost (EUR)": "final_delivery_unit_cost_euro",
  "Margin (EUR)": "final_margin_euro",
  "Average Margin (EUR)": "final_average_margin_euro",
  event_id: "event_id",
  "Destination MCCMNC": "destination_mcc_mnc_final",
  Status: "status",
  "Supplier KAM": "supplier_kam",
  "Destination Operator Code": "destination_operator_code",
  "Status Code": "status_code",
  PERR: "perr",
  "SUB RES ID": "sub_res_id",
  "Result Code": "result_code",
  "True Delivery Success": "true_delivery_success",
  "Pending Delivery": "delivery_pending",
  "True Delivery Efficiency": "true_delivery_efficiency",
  "Total Submissions": "total_submissions",
  "Submission Success": "submission_success",
  "Submission Failure": "submission_failure",
  "Delivery Success": "delivery_success",
  "Delivery Failure": "delivery_failure",
  "Label1": "cc_label1",
  "Label2": "cc_label2",
  "Master Brand": "cc_master_brand"
};

const rawCdr = {
  name: "fetchRawCdr",
  responseFields: [
    "event_id",
    "a_number",
    "b_number",
    "time_of_arrival",
    "time_of_delivery",
    "status",
    "error_description",
    "transaction_type",
    "customer_name",
    "customer_bind",
    "supplier",
    "supplier_bind",
    "event_date",
    "traffic_type",
    "supplier_interface_type",
    "destination_country_name",
    "destination_mcc_final",
    "destination_mnc_final",
    "destination_mcc_mnc_final",
    "destination",
    "lcr_name",
    "visiting_operator",
    "error_code",
    "sub_res_id",
    "result_code",
    "perr",
    "status_code",
    "orig_imsi",
    "dest_imsi",
    "delivery_retry",
  ],
  cdrTypeMultiResponseFields: [
    "time_of_arrival",
    "time_of_delivery",
    "status",
    "transaction_type",
    "error_description",
    "error_code",
    "sub_res_id",
    "perr",
  ],
  filters: {
    customer_name: "Customer Name",
    customer_bind: "Customer Bind",
    supplier: "Supplier Name",
    supplier_bind: "Supplier Bind",
    traffic_type: "Traffic Type",
    supplier_interface_type: "Supplier Interface",
    event_id: "Message ID",
    a_number: "A Number",
    b_number: "B Number",
    destination_mcc_final: "Destination MCC",
    destination_mnc_final: "Destination MNC",
    destination: "Destination Name",
    destination_country_name: "Destination Country",
    transaction_type: "Transaction Type",
  },
};
const customerRoleFields = [
  "Customer Name",
  "Customer Bind",
  "Customer Interface",
  "Customer Billing Logic",
  "Customer Interconnect",
  "Source Prime",
  "Customer Protocol",
  "Destination MCC",
  "Destination MNC",
  "Destination Operator Country",
  "Destination Operator Name",
  "Destination OpcustomerR Visited",
  "Status",
  "LCR Name",
  "Spec LCR",
  "Spec LCR",
  "A Number",
  "B Number",
];
const supplierRoleFields = [
  "Supplier Name",
  "Supplier Bind",
  "Supplier Interface Type",
  "Supplier Billing Logic",
  "Supplier Interconnect",
  "Supplier Protocol",
  "Destination Prime",
  "Destination MCC",
  "Destination MNC",
  "Destination Operator Country",
  "Destination Operator Name",
  "Destination Op Visited",
  "Status",
  "LCR Name",
  "Spec LCR",
  "A Number",
  "B Number",
];
const alertConfig = {
  "Delivery Drop": {
    parameters: [
      "Destination",
      "Supplier",
      "Customer",
      "Supplier Destination",
      "Customer Destination",
      "Customer-Supplier Destination",
    ],
  },
  Error: {
    parameters: [
      "Destination",
      "Supplier",
      "Customer",
      "Supplier Destination",
      "Customer Destination",
      "Customer-Supplier Destination",
    ],
  },
  "Volume Drop or Spike": {
    parameters: ["Destination", "Customer", "Customer Destination"],
  },
  "Delivery Report Pending": {
    parameters: [
      "Supplier",
      "Customer",
      "Supplier Destination",
      "Customer Destination",
    ],
  },
};
const percentageFields = [
  "Submission Efficiency(%)",
  "Delivery Efficiency(%)",
  "Success Percentage(%)",
  "Failure Percentage(%)",
  "Next Hop Success Percent (%)",
];

const FieldsInMegaAPI = [
  "A Number",
  "Customer Bind",
  "Source Operator Name",
  "Destination IMSI",
  "Destination Operator Code",
  "Origin IMSI",
  "Source MCC",
  "Source MNC",
  "Source Country Name",
  "SourceOpr Country",
  "Supplier Bind",
  "Traffic Type",
  "Customer Billing Logic",
  "Supplier Billing Logic",
  "Destination Country Code",
  "DestOpr MCCMNC",
  "Destination MSG",
  "Average Submission Latency",
  "Average Hub Latency",
  "Average Delivery latency",
  "Error Description",
  "Content Category",
  "Source Prime",
  "Customer Interconnect",
  "Supplier Interconnect",
  "Customer Unit Rate",
  "customer currency",
  "supplier currency",

  "Visiting Operator ID",

  "Source IP",
  "Destination IP",
  "Source Operator Code",
  "Message Terminated",
  "VisitedOpr Country",
  "Message Processed",
  "Supplier Unit Cost",
  "Destination Op Visited",
  // "Delivery Efficiency(%)",
  // "Pending Delivery",
  "Success Percentage",
  "Failure Percentage",
  "Next Hop Success Percent (%)",
  "Source MNP supplier",
  "Destination MNP supplier",
  "DRC Retry Count",
  "B Number",
  "Spec LCR",
  "LCR Name",
  "Customer System ID",
  "Supplier System ID",
  "Oracle ID",
  "Source NOP ID",
  "Category",
  "Carrier",
  "Customer KAM",
  "Source Routing ID",
  "Source Hub",
  "Source Country Code",
  "Customer Traffic Type",
  "Supplier Traffic Type",
  "Customer Protocol",
  "Supplier Protocol",
  "Destination NOP ID",
  "Percentage Routing",
  "Destination Hub",
  "CDR Type Description",
  "CDR Type Supplier",
  "Destination Prime",
  "Vertical",
  "Average Revenue (EUR)",
  "Cost (EUR)",
  "Messaging Fee (EUR)",
  "Average Cost (EUR)",
  "Margin (EUR)",
  "Average Margin (EUR)",
  "Messages Terminated",
  "Status",
  "Supplier KAM",
  "True Delivery Success",
  "True Delivery Efficiency",
  "Label1",
  "Label2",
  "Master Brand"
];

// Freeze recursively (shallow + nested)
function deepFreeze(obj) {
  Object.keys(obj).forEach((key) => {
    if (
      obj[key] &&
      typeof obj[key] === "object" &&
      !Object.isFrozen(obj[key])
    ) {
      deepFreeze(obj[key]);
    }
  });
  return Object.freeze(obj);
}

const panelVisualizationTypes = {
  lineGraph: "Line Graph",
  barGraph: "Bar Graph",
  pieChart: "Pie Chart",
  tableReport: "Table Report",
  multiAxisGaph: "MultiAxis Graph",
};

const panelProperties = {
  "Line Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Source Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interface Type",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "LCR Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Spec LCR",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Status",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Operator Code",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Visiting Operator ID",
        condition: ["Equal", "Not Equal"],
      },
    ],
    interval: ["Minute", "Hour", "Day", "Week"],
    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
        "Message Terminated",
        "Message Processed",
        "True Delivery Success",
        "True Delivery Efficiency"
      ],
    },
  },
  "Bar Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Source Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interface Type",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "LCR Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Spec LCR",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Status",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Operator Code",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Visiting Operator ID",
        condition: ["Equal", "Not Equal"],
      },
    ],

    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        //  "Error Count"
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
        "Message Terminated",
        "Message Processed",
        "True Delivery Success",
        "True Delivery Efficiency"

      ],
      "X-Axis": [
        "Customer Name",
        "Supplier Name",
        "Destination Operator Name",
        "Source Operator Name",
        "A Number",
        "Destination Operator Country",
        "DestOpr MCCMNC",
        "Label1",
        "Label2",
        "Master Brand"

      ],
    },
  },
  "Pie Chart": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Source Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interface Type",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "LCR Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Spec LCR",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Status",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Operator Code",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Visiting Operator ID",
        condition: ["Equal", "Not Equal"],
      },
    ],

    columns: {
      derivedFields: [
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
        "Message Terminated",
        "Message Processed",
        "True Delivery Success",
        "True Delivery Efficiency"

      ],
    },
  },
  "Table Report": {
    filters: [
      {
        field: "Customer Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Source Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Prime",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interface Type",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "LCR Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Spec LCR",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Status",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      // {
      //   field: "Destination Interface",
      //   condition: ["Equal", "Not Equal", "Like", "Not Like"],
      // },
      {
        field: "Destination IMSI",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination MSG",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },

      // {
      //   field: "Traffic Type",
      //   condition: ["Equal", "Not Equal", "Like", "Not Like"],
      //   datatype: "dropdown",
      // },
      /* {
                    field: "Source Interface",
                    condition: [
                        "Equal",
                        "Not Equal",
                        "Like",
                        "Not Like"
                    ]
                },*/
      {
        field: "VisitedOpr Country",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Operator Code",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Visiting Operator ID",
        condition: ["Equal", "Not Equal"],
      },
    ],

    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Submission Latency",
        "Average Hub Latency",
        "Average Delivery latency",
        "Error Description",
        "Message Terminated",
        "Message Processed",
        "DRC Retry Count",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
        "Message Terminated",
        "Message Processed",
        "True Delivery Success",
        "True Delivery Efficiency"
        //  "Error Count",

        /* "MNP Summary Hits",
                         "MNP Mitto Count",
                         "MNP Cache Count",
                         "MNP SourceGW Name",
                         "MNP SourceGW Total Count",
                         "MNP SourceGW Success Count",
                         "MNP SourceGW Failure Count",
                         "MNP DestGW Name",
                         "MNP DestGW Total Count",
                         "MNP DestGW Success Count",
                         "MNP DestGW Failure Count",
                         "SRI Msgs Route",
                         "SRI Msgs Status",
                         "SRI Msgs Error Code",
                         "SRI Msgs Error Count"
     */
      ],
      tableFields: [
        "Datetime",
        "A Number",
        "B Number",
        "Customer Bind",
        "Customer Interface",
        "Customer Name",
        "Customer Protocol",
        // "Destination Interface",
        "Destination IMSI",
        "Destination MCC",
        "Destination MNC",
        "Destination Operator Country",
        "Destination MCCMNC",
        "Destination Operator Name",
        "Destination Operator Id",
        "Origin IMSI",
        // "Source Interface",
        "Source MCC",
        "Source MNC",
        "SourceOpr Country",
        "Source MCCMNC",
        "Source Operator Name",
        "Supplier Bind",
        "Supplier Name",
        "Supplier Protocol",
        "Traffic Type",
        "Destination Country Code",
        "Content Category",
        "Source Prime",
        "Customer Interconnect",
        "Supplier Interconnect",
        "Destination Op Visited",
        "Visiting Operator ID",
        "VisitedOpr Country",
        "Customer Unit Rate",
        "Supplier Currency",
        "Customer Currency",
        "Source IP",
        "Destination IP",
        "Source Operator Code",
        "Supplier Unit Cost",
        "Error Code",
        "Source MNP supplier",
        "Destination MNP supplier",
        "LCR Name",
        "Spec LCR",
        "Customer System ID",
        "Supplier System ID",
        "Oracle ID",
        "Source NOP ID",
        "Category",
        "Carrier",
        "Customer KAM",
        "Source Routing ID",
        "Source Hub",
        "Source Country Code",
        // "Customer Traffic Type",
        // "Supplier Traffic Type",
        "Destination NOP ID",
        "Percentage Routing",
        "Destination Hub",
        "CDR Type Description",
        "CDR Type Supplier",
        "Destination Prime",
        "Vertical",
        "Customer Billing Logic",
        "Supplier Billing Logic",
        "Supplier Interface Type",
        "Status",
        "Supplier KAM",
        "Label1",
        "Label2",
        "Master Brand"
      ],
      "Aggregation Type": ["Count", "Sum", "Average", "Minimum", "Maximum"],
    },
  },
  "MultiAxis Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interface Type",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Billing Logic",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "LCR Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Spec LCR",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Status",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Interconnect",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Operator Code",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Visiting Operator ID",
        condition: ["Equal", "Not Equal"],
      },
    ],
    interval: ["Second", "Minute", "Hour", "Day", "Week"],

    columns: {
      derivedFields: [
        "Successful Submission and Submission Efficiency(%)",
        "Successful Delivery and Delivery Efficiency(%)",
      ],
    },
  },
};

const panelFilters = {
  customer_name: "Customer Name",
  customer_bind: "Customer Bind",
  src_prime: "Source Prime",
  dest_prime: "Destination Prime",
  supplier: "Supplier Name",
  supplier_bind: "Supplier Bind",
  destination_operator_name: "Destination Operator",
  destination_country: "Destination Country",
  customer_interface_type: "Interface Type",
  traffic_type_customer: "Customer Protocol",
  traffic_type_supplier: "Supplier Protocol",
  destination_mcc_final: "Destination MCC",
  destination_mnc_final: "Destination MNC",
  lcr_name: "LCR Name",
  spec_lcr: "Spec LCR",
  status: "Status",
  b_number: "B Number",

  // Billing Logic Type,
};

const customerFields = {
  customer_bind: "Customer Bind",
  customer_interface_type: "Customer Interface",
  customer_name: "Customer Name",
  customer: "Customer",
  traffic_type_customer: "Customer Protocol",
  src_prime: "Source Prime",
  customer_interconnect: "Customer Interconnect",
  unit_new_rate_round: "Customer Unit Rate",
  customer_currency_new: "Customer Currency",
  source_operator_code: "Source Operator Code",
  customer_system_id: "Customer System ID",
  src_nop_id: "Source NOP ID",
  customer_kam: "Customer KAM",
  src_routing_id: "Source Routing ID",
  src_hub: "Source Hub",
  cdr_type_description: "CDR Type Description",
  vertical: "Vertical",
  customer_billing_logic: "Customer Billing Logic",
  source_operator_name: "Source Operator Name",
  total_submissions: "Total Submission",
  submission_success: "Successful Submission",
  submission_error: "Submission Failure / Blocked Messages Count",
  submission_efficiency: "Submission Efficiency(%)",
  avg_submission_latency_final: "Average Submission Latency",
  average_latency_td_tr_final: "Average Hub Latency",
  unit_new_rate_euro: "Average Revenue (EUR)",
  cust_message_fee_euro: "Messaging Fee (EUR)",
  customer_billing_logic_count_final: "Message Processed",
};

const supplierFields = {
  // null: "Destination Interface",
  supplier_bind: "Supplier Bind",
  supplier: "Supplier Name",
  traffic_type_supplier: "Supplier Protocol",
  supplier_interconnect: "Supplier Interconnect",
  supplier_currency_new: "Supplier Currency",
  final_delivery_unit_cost_round: "Supplier Unit Cost",
  supplier_system_id: "Supplier System ID",
  dest_nop_id: "Destination NOP ID",
  dest_hub: "Destination Hub",
  cdr_type_supplier: "CDR Type Supplier",
  dest_prime: "Destination Prime",
  supplier_billing_logic: "Supplier Billing Logic",
  supplier_interface_type: "Supplier Interface Type",
  supplier_kam: "Supplier KAM",
  final_termination_charges_euro: "Cost (EUR)",
  final_delivery_unit_cost_euro: "Average Cost (EUR)",
  supplier_billing_count_final: "Message Terminated",
  next_hop_success_new: "Next hop Success",
};

const bothFields = {
  total_deliveries: "Total Delivery",
  count_successful_delivery_final: "Successful Delivery",
  delivery_failure_count_final: "Delivery Failure",
  delivery_pending_final: "Pending Delivery",
  final_delivery_efficiency: "Delivery Efficiency(%)",
  average_latency_final: "Average Delivery latency",
  error_description: "Error Description",
  delivery_retry: "DRC Retry Count",
  final_margin_euro: "Margin (EUR)",
  final_average_margin_euro: "Average Margin (EUR)",
};

const derivedFields = {
  number_of_records_new: "Count",
  submission_success: "Sub / Next Hop Sub",
  submission_error: "Submission Error",
  count_successful_delivery_final: "Success Delivered SMS",
  delivery_failure_count_final: "Delivery Failure",
  total_submissions: "Total Submission",
  delivery_retry: "Delivery Retry",
  next_hop_success_new: "Next Hop Success",
  final_delivery_efficiency: "Delivery Efficiency",
  total_deliveries: "Total Delivery",
  total_sri_count: "Total SRI Count",
  sri_success: "SRI Success",
  sri_failure: "SRI Failure",
  mt_counts: "MT Counts",
  only_hlr_dip: "Only HLR Dip",
  source_mnp_success_count: "Source MNP Success Count",
  source_mnp_failure_count: "Source MNP Failure Count",
  source_mnp_average_cost_euro: "Src MNP Average Cost (Euro)",
  soure_mnp_cost_euro: "Source MNP Cost (EUR)",
  destination_mnp_success_count: "Dest MNP Success Count",
  destination_mnp_failure_count: "Dest MNP Failure Count",
  destination_mnp_average_cost_euro: "Dest MNP Average Cost (Euro)",
  destination_mnp_cost_euro: "Destination MNP Cost (EUR)",
  customer_billing_logic_count_final: "Processed CDR",
  unit_new_rate_round: "Avg Revenue",
  cust_message_fee: "Messaging Fee",
  euro_new_rate_round: "Exchange Rate",
  cust_message_fee_euro: "Revenue (EUR)",
  delivery_success_supp_billing: "Messages Terminated",
  unit_cost_of_delivery: "Termination Rate",
  unit_cost_of_delivery_in_euro: "Termination Rate(EUR)",
  termination_charges_new_in_euro: "Termination Credit(EUR)",
  source_mnp_count: "Source MNP Count",
  destination_mnp_supplier: "Destination MNP",
  destination_mnp_count: "Destination MNP Count",
  unit_new_rate_euro: "Avg Revenue (EUR)",
  supplier_billing_count_final: "Termination Messages",
  final_termination_charges_euro: "Termination Fee (Euro)",
  final_delivery_unit_cost_euro: "Avg Cost (EUR)",
  hlr_count: "HRL Count",
  destination_hlr_cost_euro: "Dest HLR Cost (EUR)",
  final_termination_charges: "Termination Fee",
  final_delivery_unit_cost_round: "Cost",
  success_sms: "Sum of Success",
  billing_error: "Billing Error",
  convertion_rate_1hr: "Average of CR%",
  best_convertion_rate_1hr: "Average of Best Route CR%",
  convertion_rate_24hr: "Average of CR% 24h",
  best_convertion_rate_24hr: "Average of Best Route CR% 24h",
  difference_from_best: "Average of Price (Delivery gap%)",
  sms_sent: "Sum of Attempt",
  "Submission Messages": "Submission Messages",
  "Source MNP Count": "Source MNP Count",
  "Destination MNP Count": "Destination MNP Count",
  "Unit Rate/Slab Rate": "Unit Rate/Slab Rate",
  MRC: "MRC",
  "Fixed Fee/OTC": "Fixed Fee/OTC",
  "Total per SMS Charges": "Total per SMS Charges",
  "Total Messaging Fee": "Total Messaging Fee",
  "Message Fee(EUR)": "Message Fee(EUR)",
  supplier_billing_count_multiple: "Messages Terminated",
  cust_message_fee_euro_multiple: "Revenue (EUR)",
  customer_billing_logic_count_multiple: "Delivered SMS",
  termination_charges_euro_multiple: "Cost (EUR)",
  percentage_routing_final: "Percentage Routing",
};

module.exports = {
  reportsMapping,
  maxPageLimit,
  graphqlServerUri,
  restapiUri,
  reportClassification: deepFreeze(reportClassification),
  customerList,
  supplierList,
  destnationDetails,
  LcDetails,
  cdrStatusDetails,
  operatorDetails,
  destinationPrimeDetails,
  sourcePrimeDetails,
  derivedReportFields,
  columnMapping,
  rawCdr,
  customerRoleFields,
  supplierRoleFields,
  timestampParam,
  percentageFields,
  FieldsInMegaAPI,
  // aggregationFieldMapping,
  panelVisualizationTypes,
  panelProperties,
  panelFilters,
  customerFields,
  supplierFields,
  bothFields,
  derivedFields,
};
