
const rolePermissions = {
  view: 1,
  update: 2, // 2^0
  delete: 4, // 2^1
  create: 8,
  download: 16
},
      
      
   resources = {
    USER_MANAGEMENT: "User Management",
    ROLE_MANAGEMENT: "Role Management",
    DASHBOARD_MANAGEMENT: "Dashboard Management",
    PANEL_MANAGEMENT: "Panel Management",
    REPORT_MANAGEMENT: "Report Management",
    GROUP_MANAGEMENT: "Group Management",
    ALERT_MANAGEMENT: "Alert Management",
    LOG_MANAGEMENT: "Logs Management",
    CARD_MANAGEMENT: "Card Management",
    CDR_SEARCH : "CDR Search"
  },
      
      
 
  adminRole = {
    name: "Super admin role",
    description: "Super admin role",
    isSuperAdminRole: true,
    resources: {
      "Dashboard Management":
        rolePermissions.view | rolePermissions.create | rolePermissions.update,
      "Role Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Panel Management":
      rolePermissions.view |
      rolePermissions.create |
      rolePermissions.update |
      rolePermissions.delete,
      "User Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Card Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Group Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Alert Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Report Management":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
      "Logs Management": rolePermissions.view,
      "CDR Search":
        rolePermissions.view |
        rolePermissions.create |
        rolePermissions.update |
        rolePermissions.delete,
    },
  },
  defaultUserRole = {
    name: "Default user role",
    description: "Default User role",
    resources: {
      "Dashboard Management": rolePermissions.view,
      "Report Management": rolePermissions.view | rolePermissions.download,
      "Logs Management": rolePermissions.view,
    },
  },

  preCreatedUserRoles = [
    "Customer_role",
    "Supplier_role",
    "Customer_Supplier_role",
    "Super admin role",
    "Default user role",
  ]
  // client supplier predefined roles



 const adminUser = {
  name: 'Super admin',
  email: '<EMAIL>',
  password: 'Password@123',
  isEmailVerified: true,
  isSuperAdmin: true,
},
  auditLogEvents = {
    LOGIN: "Login",
    USER_MANAGEMENT: "User management",
    ROLE_MANAGEMENT: "Role management",
    GROUP_MANAGEMENT: "Group management",
    CARD_MANAGEMENT: "Card management",
    PANEL_MANAGEMENT: "Panel management",
    DASHBOARD_MANAGEMENT: "Dashboard management",
    LOGIN_FAILURE: "Login failure",
    // EMAIL_SEND_FAIL: "Email sending failed",
    // DOWNLOAD_FAILED: "Failed downloading reports",
    // DOWNLOAD_INITIATED: "Download initiated",
  };

  const userTypes = {
    CUSTOMER: "Customer",
    SUPPLIER: "Supplier",
    BOTH: "Both"
  }

module.exports = {
  rolePermissions,
  adminRole,
  adminUser,
  resources,
  auditLogEvents,
  defaultUserRole,
  preCreatedUserRoles,
  userTypes
};
