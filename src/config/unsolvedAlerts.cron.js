const logger = require("./logger");
const { CronJob } = require("cron");
const { notifyUnsolvedAlert } = require("../services/cron.service");
const config = require("./config");
const { unsolvedAlertNotifyEnabled } = config.alerts;

async function callBack() {
  try {
    if (!unsolvedAlertNotifyEnabled) {
      logger.info(
        `Unsolved alerts notification is disabled. "UNSOLVED_ALERT_NOTIFY_ENABLED" : ${unsolvedAlertNotifyEnabled}`
      );
      return;
    }
    logger.info(
      `========[ Unsolved Alerts notification Cron Job Started ]==========`
    );
    await notifyUnsolvedAlert();
    logger.info(
      `[Unsolved Alerts notification Cron Job Completed Successfully ]`
    );
  } catch (error) {
    logger.error(
      `[ Unsolved Alerts notification Cron Job Failed ]: ${error.message}`
    );
  } finally {
    logger.info(
      `=========[ Unsolved Alerts notification Cron Job Ended ]=========\n`
    );
  }
}
// Fetch Unsolved Alerts notification every hour
new CronJob(
  "0 * * * *", // Runs at the top of every hour
  callBack,
  null,
  true, // Start the job immediately
  "Asia/Kolkata" // Timezone
);

callBack(); // Call the function immediately for testing
