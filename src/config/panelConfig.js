
const panelVisualizationTypes = [
  "Line Graph",
  "Bar Graph",
  "Pie Chart",
  "Table Report",
  "MultiAxis Graph",
];


const panelProperties = {
  "Line Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
    ],
    interval: ["Minute", "Hour", "Day", "Week"],
    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
      ],
    },
  },
  "Bar Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
    ],

    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        //  "Error Count"
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
      ],
      "X-Axis": [
        "Customer Name",
        "Supplier Name",
        "Destination Operator Name",
        "Source Operator Name",
        "A Number",
        "Destination Operator Country",
        "DestOpr MCCMNC",
      ],
    },
  },
  "Pie Chart": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
    ],

    columns: {
      derivedFields: [
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
      ],
    },
  },
  "Table Report": {
    filters: [
      {
        field: "Customer Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Bind",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "B Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination Interface",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination IMSI",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
      {
        field: "Destination MSG",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },

      {
        field: "Traffic Type",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      /* {
                    field: "Source Interface",
                    condition: [
                        "Equal",
                        "Not Equal",
                        "Like",
                        "Not Like"
                    ]
                },*/
      {
        field: "VisitedOpr Country",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
    ],

    columns: {
      derivedFields: [
        "Total Submission",
        "Successful Submission",
        "Submission Failure / Blocked Messages Count",
        "Total Delivery",
        "Successful Delivery",
        "Delivery Failure",
        "Pending Delivery",
        "Submission Efficiency(%)",
        "Delivery Efficiency(%)",
        "Average Submission Latency",
        "Average Hub Latency",
        "Average Delivery latency",
        "Error Description",
        "Message Terminated",
        "Message Processed",
        "DRC Retry Count",
        "Average Revenue (EUR)",
        "Cost (EUR)",
        "Messaging Fee (EUR)",
        "Average Cost (EUR)",
        "Margin (EUR)",
        "Average Margin (EUR)",
        //  "Error Count",

        /* "MNP Summary Hits",
                         "MNP Mitto Count",
                         "MNP Cache Count",
                         "MNP SourceGW Name",
                         "MNP SourceGW Total Count",
                         "MNP SourceGW Success Count",
                         "MNP SourceGW Failure Count",
                         "MNP DestGW Name",
                         "MNP DestGW Total Count",
                         "MNP DestGW Success Count",
                         "MNP DestGW Failure Count",
                         "SRI Msgs Route",
                         "SRI Msgs Status",
                         "SRI Msgs Error Code",
                         "SRI Msgs Error Count"
     */
      ],
      tableFields: [
        "A Number",
        "B Number",
        "Customer Bind",
        "Customer Interface",
        "Customer Name",
        "Customer Protocol",
        "Destination Interface",
        "Destination IMSI",
        "Destination MCC",
        "Destination MNC",
        "Destination Operator Country",
        "Destination MCCMNC",
        "Destination Operator Name",
        "Destination Operator Id",
        "Origin IMSI",
        // "Source Interface",
        "Source MCC",
        "Source MNC",
        "SourceOpr Country",
        "Source MCCMNC",
        "Source Operator Name",
        "Supplier Bind",
        "Supplier Name",
        "Supplier Protocol",
        "Traffic Type",
        "Destination Country Code",
        "Content Category",
        "Source Prime",
        "Customer Interconnect",
        "Supplier Interconnect",
        "Destination Op Visited",
        "Visiting Operator ID",
        "VisitedOpr Country",
        "Customer Unit Rate",
        "Supplier Currency",
        "Customer Currency",
        "Source IP",
        "Destination IP",
        "Source Operator Code",
        "Supplier Unit Cost",
        "Error Code",
        "Source MNP supplier",
        "Destination MNP supplier",
        "LCR Name",
        "Spec LCR",
        "Customer System ID",
        "Supplier System ID",
        "Oracle ID",
        "Source NOP ID",
        "Category",
        "Carrier",
        "Customer KAM",
        "Source Routing ID",
        "Source Hub",
        "Source Country Code",
        "Customer Traffic Type",
        "Supplier Traffic Type",
        "Destination NOP ID",
        "Percentage Routing",
        "Destination Hub",
        "CDR Type Description",
        "CDR Type Supplier",
        "Destination Prime",
        "Vertical",
        "Customer Billing Logic",
        "Supplier Billing Logic",
      ],
      "Aggregation Type": ["Count", "Sum", "Average", "Minimum", "Maximum"],
    },
  },
  "MultiAxis Graph": {
    filters: [
      {
        field: "Customer Interface",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Customer Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },

      {
        field: "Supplier Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Supplier Protocol",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Name",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "Destination Operator Country",
        condition: ["Equal", "Not Equal"],
        datatype: "dropdown",
      },
      {
        field: "DestOpr MCCMNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MCC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Destination MNC",
        condition: ["Equal", "Not Equal"],
      },
      {
        field: "Source Operator Name",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
        datatype: "dropdown",
      },
      {
        field: "A Number",
        condition: ["Equal", "Not Equal", "Like", "Not Like"],
      },
    ],
    interval: ["Second", "Minute", "Hour", "Day", "Week"],

    columns: {
      derivedFields: [
        "Successful Submission and Submission Efficiency(%)",
        "Successful Delivery and Delivery Efficiency(%)",
      ],
    },
  },
};

const dynamicReports = {
  filters: {
    customer_name: "Customer Name",
    customer_bind: "Customer Bind",
    src_prime: "Source Prime",
    dest_prime: "Destination Prime",
    supplier: "Supplier Name",
    supplier_bind: "Supplier Bind",
    destination_operator_name: "Destination Operator",
    destination_country: "Destination Country",
    customer_interface_type: "Interface Type",
    traffic_type_customer: "Customer Protocol",
    traffic_type_supplier: "Supplier Protocol",
    destination_mcc_final: "Destination MCC",
    destination_mnc_final: "Destination MNC",
    lcr_name: "LCR Name",
    spec_lcr: "Spec LCR",
    status: "Status",
    b_number: "B Number"
  }
}

const panelFilters = {
  customer_name: "Customer Name",
  customer_bind: "Customer Bind",
  src_prime: "Source Prime",
  dest_prime: "Destination Prime",
  supplier: "Supplier Name",
  supplier_bind: "Supplier Bind",
  destination_operator_name: "Destination Operator",
  destination_country: "Destination Country",
  customer_interface_type: "Interface Type",
  traffic_type_customer: "Customer Protocol",
  traffic_type_supplier: "Supplier Protocol",
  destination_mcc_final: "Destination MCC",
  destination_mnc_final: "Destination MNC",
  lcr_name: "LCR Name",
  spec_lcr: "Spec LCR",
  status: "Status",
  b_number: "B Number"

  // Billing Logic Type,

}


module.exports = {
  panelVisualizationTypes,
  panelProperties,
  panelFilters
}