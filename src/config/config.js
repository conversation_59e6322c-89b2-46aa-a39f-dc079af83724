const dotenv = require('dotenv');
const path = require('path');
const Joi = require('joi');
const { fail } = require('assert');

dotenv.config({ path: path.join(__dirname,'../../.env') });

const envVarsSchema = Joi.object().
  keys({
    NODE_ENV: Joi.string().valid('production','development','demo','test').
default('development'),
    PORT: Joi.number().default(3000),
    CLIENT_URL: Joi.string().default('localhost:3000'),
    JWT_SECRET: Joi.string().required().
description('JWT secret key'),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number().default(30).
description('minutes after which access tokens expire'),
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number().default(30).
description('days after which refresh tokens expire'),
    SMTP_HOST: Joi.string().description('server that will send the emails'),
    SMTP_PORT: Joi.number().description('port to connect to the email server'),
    SMTP_USERNAME: Joi.string().description('username for email server'),
    SMTP_PASSWORD: Joi.string().description('password for email server'),
    EMAIL_FROM: Joi.string().description('the from field in the emails sent by the app'),

    GAMIL_OAUTH: Joi.number().default(0),
    EMAIL_ID: Joi.string().description("email id"),
    EMAIL_CLIENT_ID: Joi.string().description("email client id"),
    EMAIL_CLIENT_SECRET: Joi.string().description("email client secret"),
    EMAIL_REFRESH_TOKEN: Joi.string().description("email refresh token"),

    //mYsql DB settings
    MYSQL_HOST: Joi.string().required().
description('Mysql instance server'),
MYSQL_PORT: Joi.string().default(3306).
description('Mysql instance server'),

    MYSQL_USER: Joi.string().required().
description('Mysql user name'),
    MYSQL_PASSWORD: Joi.string().required().
description('Mysql password'),
    MYSQL_DB: Joi.string().required().
description('Mysql password'),
    MYSQL_POOL_MAX: Joi.number().default(5).
description('Mysql max pool connections'),
    MYSQL_POOL_MIN: Joi.number().default(0).
description('Mysql min pool connection'),

    IMAGE_ENDPOINT: Joi.string().default('http://localhost:9697/media'),
    LDAP_ENABLED: Joi.boolean(),
    LDAP_SERVER_URL: Joi.string(),
    LDAP_USER_GROUP: Joi.string(),
    LDAP_USER_DC: Joi.string(),
    HOURLY_REPORTS: Joi.string().default('Hourly Traffic Analysis'),
    DAILY_REPORTS: Joi.string().default('Customer Processing Fee Invoice Detail,Customer Termination Credit Details'),
    REPORTS_EMAIL: Joi.string(),
    DEFAULT_TIMEZONE : Joi.string().default('Asia/Kolkata'),
    REPORTS_DIRECTORY: Joi.string().default('./reports'),
    OFFLINE_DOWNLOAD_DIRECTORY: Joi.string().default('./offline_downloads'),
    NETWORK_REPORTS_DOWNLOAD_DIRECTORY: Joi.string().default('./network_reports'),
    NETWORK_REPORTS_NAME: Joi.string().required(),
    BILLING_REPORTS_DIRECTORY: Joi.string().default('./billing_reports'),
    BILLING_REPORTS_NAME: Joi.string().required(),
    FY_REPORTS_DIRECTORY: Joi.string().default('./fy_reports'),
    FY_REPORTS_NAME: Joi.string().required(),
    SECURED_FOLDER: Joi.string().default('./secured_folder'),
    NETWORK_REPORTS_NO_OF_DAYS_ALLOWED: Joi.number().default(3),
    DATA_SERVICE_URL: Joi.string().required().description('Data layer access URL'),
    PRIVATE_KEY_PATH: Joi.string().required().description('Path to the private key for encryption'),
    PUBLIC_KEY_PATH: Joi.string().required().description('Path to the public key for encryption'),
    /* pOSTGRES DB setting
        POSTGRES_URL: Joi.string().required().description('Postgres DB url'),
        POSTGRES_URL_TEST: Joi.string().required().description('Postgres DB test url'), */
    UNSOLVED_ALERT_NOTIFY_ENABLED: Joi.boolean(),
  }).
  unknown(),

 { value: envVars,error } = envVarsSchema.prefs({ errors: { label: 'key' } }).validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

module.exports = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  corsOrigin: envVars.CLIENT_URL || '*',
  clientURL: envVars.CLIENT_URL || 'http://localhost:3000',
  dataServiceUrl: envVars.DATA_SERVICE_URL,

  /*to be used if DB is Postgres*/
  postgres: {
    url: envVars.NODE_ENV === 'test'
? envVars.POSTGRES_URL_TEST
: envVars.POSTGRES_URL,
    options: {
      useCreateIndex: true,
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  },
  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
    resetPasswordExpirationMinutes: 10,
    emailVerificationExpirationDays: 15,
    otpVerificationExpirationMinutes: 10
  },

  mysql_config: {
    HOST: envVars.MYSQL_HOST,
    PORT: envVars.MYSQL_PORT,
    USER: envVars.MYSQL_USER,
    PASSWORD: envVars.MYSQL_PASSWORD,
    DB: envVars.MYSQL_DB,
    dialect: "mysql",
    pool: { //pool configuration
      max: envVars.MYSQL_POOL_MAX,//maximum number of connection in pool
      min: envVars.MYSQL_POOL_MIN,//minimum number of connection in pool
      acquire: envVars.MYSQL_ACQUIRE_TIME,//maximum time in ms that pool will try to get connection before throwing error
      idle: envVars.MYSQL_IDLE_TIME//maximum time in ms, that a connection can be idle before being released
    }
  },


  PRIVATE_KEY_PATH: envVars.PRIVATE_KEY_PATH,
  PUBLIC_KEY_PATH: envVars.PUBLIC_KEY_PATH,
  emailVerificationRequired: 0,
  GAMIL_OAUTH: envVars.GAMIL_OAUTH || 0,
  email: {
    smtp: {
      host: envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      pool: true,
      auth: {
        user: envVars.SMTP_USERNAME,
        pass: envVars.SMTP_PASSWORD,
      },
      tls: { rejectUnauthorized: false }
    },
    oauth2: {
      emailId: envVars.EMAIL_ID,
      clientId: envVars.EMAIL_CLIENT_ID,
      clientSecret: envVars.EMAIL_CLIENT_SECRET,
      refreshToken: envVars.EMAIL_REFRESH_TOKEN,
    },
    from: envVars.EMAIL_FROM,
  },

  dbToBeUsed: "mysql",
  image_endpoint: envVars.IMAGE_ENDPOINT,

  log: {
    level: envVars.LOG_LEVEL || 'debug',
    file: envVars.LOG_FILE_NAME || 'smshub-reports-be',
    max_size: envVars.MAX_SIZE || '1G',
    max_retention_days: envVars.MAX_RETENTION_DAYS || '180d'
  },
  enableLoggingToFile: 1,
  enableLoggingToConsole: 1,

  ldap_config: {
    enabled: envVars.LDAP_ENABLED || 0,
    host: envVars.LDAP_SERVER_URL,
    user_dc: envVars.LDAP_USER_DC,
    user_group: envVars.LDAP_USER_GROUP,
    admin_user:envVars.LDAP_ADMIN_USER,
    admin_password : envVars.LDAP_ADMIN_PASSWORD,
    usernameAttribute: envVars.LDAP_USERNAME_ATTRIBUTE

  },

  otpLength: 6,

  autoReports: {
    dailyReports: envVars.DAILY_REPORTS,
    hourlyReports: envVars.HOURLY_REPORTS,
    reportEmail: envVars.REPORTS_EMAIL
  },

  alerts: {
    unsolvedAlertNotifyEnabled: envVars.UNSOLVED_ALERT_NOTIFY_ENABLED || 0,
  },

  regexSymbols: ["%"],
  DEFAULT_TIMEZONE : envVars.DEFAULT_TIMEZONE || "Asia/Kolkata",
  reports_directory: envVars.REPORTS_DIRECTORY,
  secured_folder: envVars.SECURED_FOLDER,

  NETWORK_REPORTS: {
    reports_name: envVars.NETWORK_REPORTS_NAME,
    reports_directory: envVars.NETWORK_REPORTS_DOWNLOAD_DIRECTORY,
    no_of_days: envVars.NETWORK_REPORTS_NO_OF_DAYS_ALLOWED,
  },

  BILLING_REPORTS: {
    reports_name: envVars.BILLING_REPORTS_NAME,
    reports_directory: envVars.BILLING_REPORTS_DIRECTORY,
  },

  FY_REPORTS: {
    reports_name: envVars.FY_REPORTS_NAME,
    reports_directory: envVars.FY_REPORTS_DIRECTORY,
  },

  offline_downloads: {
    status: {
      COMPLETED: "Successfully downloaded",
      FAILED: "Failed",
      INITIATED: "In progress"
    },
    category: {
      static: "static",
      cdr: "cdr",
      network: "network",
      dynamic: "dynamic"
    },
    download_directory: envVars.OFFLINE_DOWNLOAD_DIRECTORY,
    initiate_offline_download_rows: 100000, // To initiate offline download
    message: "Your offline download is initiated, Please view the status of your download in Offline Downloads menu",
  },

  // Total number of rows in each file. It will be divided into multiple files if the number of rows is more than this
  // Maximum number of rows to be fetched for reports. It will add pagination if the number of rows is more than this
  NO_OF_ROWS_PER_FILE: 1000000,

  NO_OF_DATA_PER_QUERY: 100000, // Maximum number of rows to be fetched for reports. It will add pagination if the number of rows is more than this

  DATA_LIMIT_FOR_GRAPGH: 500000, // If The total count is greater than the limit, then we will fetch only the limit number of rows


  START_OF_YEAR: 'January',
  END_OF_YEAR: 'December',
  
  maskingOptions: {
    character: '?',
    position: 'first', // first, middle, last
    minPercentage: 40,
    maskingField:[
      'A Number',
      'B Number',
      'Destination IMSI',
      'Origin IMSI'
    ],
    chunkSize: 100000
  },
  paddingConfig: {
    fields: ['Source MNC', 'Destination MNC', 'Source MCCMNC', 'DestOpr MCCMNC'],
    paddingSize: 2,
    chunkSize: 10000
  },
  REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS: ['Facebook Traffic Report', 'WhatsApp Traffic Report'],

  GRAPH_CONFIG: {
    LINE: {
      name: "line",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    PIE: {
      name: "pie",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    BAR: {
      name: "bar",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    SCATTER_PLOT: {
      name: "scatter",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    GUAGE: {
      name: "guage",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    HEAT_MAP: {
      name: "heatMap",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    },
    RADIAL_PIE: {
      name: "radialPie",
      xAxisFieldsMax: 1,
      yAxisFieldsMax: 1,
      noOfDataMax: 1000,
    }
  }
};