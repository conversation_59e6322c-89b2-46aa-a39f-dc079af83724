const logger = require("./logger");
const { CronJob } = require("cron");
const { fetchAlertsHistory } = require("../services/cron.service");

async function callBack() {
  try {
    logger.info(`========[ Alerts History Cron Job Started ]==========`);
    await fetchAlertsHistory();
    logger.info(`[ Alerts History Cron Job Completed Successfully ]`);
  } catch (error) {
    logger.error(`[ Alerts History Cron Job Failed ]: ${error.message}`);
  } finally {
    logger.info(`=========[ Alerts History Cron Job Ended ]=========\n`);
  }
}
// Fetch Alerts History every 5 minutes
new CronJob(
  "*/5 * * * *", // Runs every 5 minutes
  callBack,
  null,
  true, // Start the job immediately
  "Asia/Kolkata" // Timezone
);

// callBack(); // Call the function immediately for testing
