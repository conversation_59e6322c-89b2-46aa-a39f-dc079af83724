const config = require("./config");
const alertConfig = require("./alertConfig");
const dateConfig = require("./dateConfig");


module.exports = {
    MAX_DASHBOARDS: 10,
    MAX_PANELS_PER_DASHBOARD: 5,
    MAX_Y_AXIS_FIELDS: 3, // Derived fields
    MAX_CARDS_PER_DASHBOARD: 4,
    FIELDS_WITH_MULTIPLE_CONDITIONS: [
        'A Number'
    ],
    MAX_EMAIL_ATTACHMENT_SIZE: 7, // in MB
    MAX_TIME_RANGE_RAW_CDR: 2880, //in min
    MAX_ROWS_FOR_EXCEL : 100000,
    MAX_ROWS_FOR_PDF : 50000,
    MAX_ROWS_FOR_CSV : 200000000,
    INITIATE_OFFLINE_DOWNLOAD: config.offline_downloads.initiate_offline_download_rows,
    OTP_EXPIRY: config.jwt.otpVerificationExpirationMinutes * 60, //in seconds
    FYR_START_DATE: dateConfig.FYR_START_DATE,
    FYR_END_DATE: dateConfig.FYR_END_DATE,
    MONTHLY_BILLING_REPORTS: config.BILLING_REPORTS.reports_name.split(",").map(name => name.trim()),
    FY_REPORTS: config.FY_REPORTS.reports_name.split(",").map(name => name.trim()),
    REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS: config.REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS,
    ALERT_NOTIFICATION_SEC: alertConfig.alertConfig.notificationSec, // 5 minutes
    GRAPH_CONFIG: config.GRAPH_CONFIG,
}