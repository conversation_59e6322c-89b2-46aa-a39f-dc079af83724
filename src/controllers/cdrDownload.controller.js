const { cdrDownloadService } = require("../services");
const catchAsync = require("../utils/catchAsync");
const pick = require("../utils/pick");
const path = require("path");
const fs = require("fs");
const { cdr_downloads } = require("../config/config");
const { adminUser } = require("../config/roles");

const createCdrDownload = catchAsync(async (req, res) => {
  const payload = { ...req.body };

  let response = await cdrDownloadService.createCdrDownload(payload);
  res.status(200).send(response);
});

const getCdrDownloadData = catchAsync(async (req, res) => {
  const filter = pick(req.query, ["search"]);
  const options = pick(req.query, ["sortBy", "limit", "page"]);

  const { user } = req;
  console.log("user.name->", user.name)
  if (user.name.toLowerCase() != adminUser.name.toLowerCase()) {
    filter.userId = user.id; // Assuming `user.id` contains the user's unique identifier
  }
  console.log("filter->", filter)


  let response = await cdrDownloadService.getCdrDownloadData(filter, options, req.user);
  res.status(200).send(response);
});

const getDownloadById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { user } = req;

  let response = await cdrDownloadService.getDownloadById(id, user);
  res.status(200).send(response);
});

const deleteCdrDownload = catchAsync(async (req, res) => {
  const { id } = req.params;

  let response = await cdrDownloadService.deleteCdrDownload(id);
  res.status(200).send(response);
});

const downloadCrdFile = catchAsync(async (req, res) => {
  // console.log("req.params is ", req.params);
  const { id } = req.params;
  const { user } = req;

  // Fetch file data from the database
  let cdrData = await cdrDownloadService.getDownloadById(id, user);

  if (!cdrData) {
    return res.status(404).json({ message: "File not found" });
  }

  switch (cdrData.status) {
    case cdr_downloads.status.INITIATED:
      return res.status(404).json({ message: "File is in progress" });
    case cdr_downloads.status.FAILED:
      return res.status(404).json({ message: "File download failed" });
  }

  // Construct file path
  // const filePath = path.resolve(
  //   cdr_downloads.download_directory,
  //   cdrData.data.fileName
  // );
  const filePath = cdrData.data.filePath;

  // console.log("filePath is ", filePath);

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ message: "File not found on the server" });
  }

  // Send file for download
  res.download(filePath, cdrData.fileName, (err) => {
    if (err) {
      console.error("Error while sending the file:", err);
      return res.status(500).json({ message: "Unable to download the file" });
    }
  });
});

module.exports = {
  createCdrDownload,
  getCdrDownloadData,
  getDownloadById,
  deleteCdrDownload,
  downloadCrdFile,
};
