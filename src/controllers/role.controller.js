const httpStatus = require("http-status");
const pick = require("../utils/pick");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { auditLogEvents, resources } = require("../config/roles");
const { roleService, auditLogService } = require("../services"),
  createRole = catchAsync(async (req, res) => {
    const payload = { ...req.body };
    payload.createdBy = req.user.id;
    if (payload.resources && payload.resources.length > 0) {
      let result = [];
      for (let i = 0; i < payload.resources.length; i++) {
        console.log(payload.resources[i]);
        if (
          payload.resources[i].name == resources.USER_MANAGEMENT ||
          payload.resources[i].name == resources.ROLE_MANAGEMENT
        ) {
          return res.status(400).json({
            message: payload.resources[i].name + " permission not allowed",
          });
        }
        // if (payload.resources[i].name != resources.USER_MANAGEMENT && payload.resources[i].name != resources.ROLE_MANAGEMENT) {
        result.push(payload.resources[i]);
        //  }
      }
      payload.resources = result;
    }
    let role = await roleService.createRole(payload);
    const { createdAt, updatedAt, ...filteredRole } = role.dataValues;

    const response = {
      status: "OK",
      message: "Role created successfully",
      result: {
        name: role.name,
        id: role.id,
      },
    };
    const auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.ROLE_MANAGEMENT,
      action: `Created new role ${role.name}`,
      details: filteredRole,
    };

    auditLogService.addAuditLog(auditLogPayload);
    res.status(200).json(response);
  }),
  getAllRoles = catchAsync(async (req, res) => {
    let pageno = req.query.page ? req.query.page : 1;
    // const filter = {createdBy : req.user.id};
    const filter = pick(req.query, ["name", "role", "search"]),
      options = pick(req.query, ["sortBy", "limit", "page"]);
    let roleList = await roleService.getAllRoles(req.user, filter, options);
    res.status(200).json(roleList);
  }),
  getAllRoleNames = catchAsync(async (req, res) => {
    let roleList = await roleService.getAllRoleNames(req.user);
    res.status(200).json(roleList);
  }),
  getRole = catchAsync(async (req, res) => {
    const { roleId } = req.params;
    let combinedResponse = await roleService.getRole(req.user, roleId);
    res.status(200).json(combinedResponse);
  }),
  updateRole = catchAsync(async (req, res) => {
    const payload = { ...req.body };
    let { roleId } = req.params;

    if (payload.resources && payload.resources.length > 0) {
      let res = [];
      for (let i = 0; i < payload.resources.length; i++) {
        if (
          payload.resources[i].name != resources.USER_MANAGEMENT &&
          payload.resources[i].name != resources.ROLE_MANAGEMENT
        ) {
          res.push(payload.resources[i]);
        }
      }
      payload.resources = res;
    }

    let role = await roleService.updateRole(payload, roleId);
    const { createdAt, updatedAt, ...filteredRole } = role.dataValues;

    const response = {
      status: "OK",
      message: "Role updated successfully",
      result: {
        name: role.name,
        roleId: role.roleId,
      },
    };
    const auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.ROLE_MANAGEMENT,
      action: `Updated role ${role.name}`,
      details: filteredRole,
    };
    auditLogService.addAuditLog(auditLogPayload);
    res.status(200).json(response);
  }),
  deleteRole = catchAsync(async (req, res) => {
    const { roleId } = req.params;
    let response = await roleService.deleteRole(roleId),
      auditLogPayload = {
        username: req.user.name,
        userId: req.user.id,
        roleName: req.user.role.name,
        event: auditLogEvents.ROLE_MANAGEMENT,
        action: `Deleted role ${response.result.name}`,
      };
    auditLogService.addAuditLog(auditLogPayload);
    (response.message = "Role deleted successfully"),
      res.status(200).json(response);
  });

module.exports = {
  createRole,
  getAllRoles,
  getRole,
  updateRole,
  deleteRole,
  getAllRoleNames,
};
