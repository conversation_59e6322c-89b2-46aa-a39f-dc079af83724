const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { clientURL ,image_endpoint} = require('../config/config');
const { authService,userService,tokenService,emailService } = require('../services'),


 login = catchAsync(async (req,res) => {
  const { email,password,timezone } = req.body;
  let resetPassword = false;
  const user = await authService.loginUserWithEmailAndPassword(email,password);
  if (user.profileImage) {
    user.profileImage = image_endpoint + user.profileImage; 
  }
  if(!user.isEmailVerified) {
    let token = await tokenService.generateResetPasswordToken(email);
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: "Welcome to Smshub!",
      template: 'change_password.template',
      context: {
        user: user.name,
        resetPasswordUrl: `${clientURL}/auth/reset-password/${token}`
  
      },
    },
     emailResponse = await emailService.MailService(emailObj);
    if (!emailResponse.isSuccess) {
      return res.status(500).send({ message: "Failed sending mail" });
    }
    resetPassword = true;
    const tokens = await tokenService.generateAuthTokens(user,timezone);
   
   return res.
    cookie('refreshToken',tokens.refresh.token,{
      maxAge: tokens.refresh.maxAge,
      httpOnly: true,
      sameSite: 'none',
      secure: true,
    }).
    send({ resetPassword,
user,
token: tokens.access,
refreshToken: tokens.refresh.token });
   
  }

  if(user.isSuperAdmin) {
    const tokens = await tokenService.generateAuthTokens(user,timezone);
    
    res.
      cookie('refreshToken',tokens.refresh.token,{
        maxAge: tokens.refresh.maxAge,
        httpOnly: true,
        sameSite: 'none',
        secure: true,
      }).
      send({ resetPassword,
user,
token: tokens.access });
    
  }
  else {
    console.log("sending otp mail")
    await authService.sendOTP(user);
    res.status(200).send({ "message": "Email verification mail sent",
"otpSent": true,
"email": user.email })
  }

  
}),

 logout = catchAsync(async (req,res) => {
  await authService.logout(req.cookies.refreshToken);
  res.status(httpStatus.NO_CONTENT).send();
}),

 refreshTokens = catchAsync(async (req,res) => {
  const { user,tokens } = await authService.refreshAuth(req.cookies.refreshToken);
  if (user.profileImage) {
    user.profileImage = image_endpoint + user.profileImage; 
   }
  res.
    cookie('refreshToken',tokens.refresh.token,{
      maxAge: tokens.refresh.maxAge,
      httpOnly: true,
      sameSite: 'none',
      secure: true,
    }).
    send({ user,
token: tokens.access });
}),

 forgotPassword = catchAsync(async (req,res) => {
// const resetPasswordToken = await tokenService.generateResetPasswordToken(req.body.email);
  await authService.sendResetPasswordEmail(req.body.email);
  res.status(httpStatus.NO_CONTENT).send();
}),

 resetPassword = catchAsync(async (req,res) => {
  await authService.resetPassword(req.body.token,req.body.password);
  res.status(httpStatus.OK).send();
}),

 emailVerification = catchAsync(async (req,res) => {
  const user = await authService.emailVerification(req.query.token);
  res.send({ isEmailVerified: Boolean(user.isEmailVerified) });
}),


 verifyOTP = catchAsync(async (req,res) => {
  const user = await authService.verifyOTP(req.body.otp,req.body.email),
   tokens = await tokenService.generateAuthTokens(user,req.body.timezone);
   if (user.profileImage) {
    user.profileImage = image_endpoint + user.profileImage; 
   }
  res.
    cookie('refreshToken',tokens.refresh.token,{
      maxAge: tokens.refresh.maxAge,
      httpOnly: true,
      sameSite: 'none',
      secure: true,
    }).
    send({ resetPassword,
user,
token: tokens.access });
  
});

module.exports = {
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  emailVerification,
  verifyOTP
};