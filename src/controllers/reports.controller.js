const dayjs = require("dayjs");
const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const {
  reportsService,
  staticReportsService,
  networkReportsService,
  cdrReportsService,
  userService,
} = require("../services");
const logger = require("../config/logger");
const { autoReports, DEFAULT_TIMEZONE } = require("../config/config");
const {
  derivedReportFields,
  reportsMapping,
} = require("../config/reportConfig");
const { adminUser } = require("../config/roles");
const {
  processReportsByGranularity,
} = require("../services/reports.autosave.service");
const config = require("../config/config");
const {
  zipSubdirectories,
  zipSubContents,
} = require("../utils/saveCsvOffline");
const reportConfig = require("../config/reportConfig");

const getStaticReports = catchAsync(async (req, res) => {
  const filter = pick(req.body, ["search"]);
  const options = pick(req.body, ["limit", "page"]);
  let response = await staticReportsService.getStaticReports(
    req.user,
    req.body,
    options,
    res
  );

  // if (req.body?.selectedColumns) {
  //   await reportsService.updateReportItem(
  //     req.user.id,
  //     req.body.reportName,
  //     req.body.selectedColumns
  //   );
  // }

  if (response) {
    res.status(200).send(response);
  }
});

const getFilterFields = catchAsync(async (req, res) => {
  let response = await reportsService.getFilterFields(req.user, req.query);
  res.status(200).send(response);
});

const getReportTypes = catchAsync(async (req, res) => {
  let response = await reportsService.getReportTypes(req.user, req.query);
  res.status(200).send(response);
});

const getCustomerList = catchAsync(async (req, res) => {
  let response = await reportsService.getCustomerList(req.user);
  res.status(200).send(response);
});

const getSupplierList = catchAsync(async (req, res) => {
  let response = await reportsService.getSupplierList(req.user);
  res.status(200).send(response);
});

const getDerivedFieldsList = catchAsync(async (req, res) => {
  res.status(200).send(derivedReportFields);
});

const getDestinationDetails = catchAsync(async (req, res) => {
  const filter = pick(req.body, ["search"]);

  let response = await reportsService.getDestinationDetails(
    req.user,
    req.query
  );
  res.status(200).send(response);
});

const getLcrDetails = catchAsync(async (req, res) => {
  let response = await reportsService.getLcrDetails(req.user, req.query);
  res.status(200).send(response);
});

const getcdrStatus = catchAsync(async (req, res) => {
  let response = await reportsService.getcdrStatus(req.user, req.query);
  res.status(200).send(response);
});

const getDestinationPrimeDetails = catchAsync(async (req, res) => {
  let response = await reportsService.getDestinationPrimeDetails(
    req.user,
    req.query
  );
  res.status(200).send(response);
});

const getSourcePrimeDetails = catchAsync(async (req, res) => {
  let response = await reportsService.getSourcePrimeDetails(
    req.user,
    req.query
  );
  res.status(200).send(response);
});

const sendAutoReportsEmail = catchAsync(async (req, res) => {
  const currentTime = dayjs();
  let reportPayload;
  const filter = pick(req.body, ["search"]),
    options = pick(req.body, ["limit", "page"]);
  let user = {};

  user.isSuperAdmin = true;
  user.timezone = DEFAULT_TIMEZONE;
  if (req.query.type == "hour") {
    const lastHour = currentTime.subtract(1, "hour").format("YYYY-MM-DD HH");
    let hourlyReports = autoReports.hourlyReports.split(",");
    for (let i = 0; i < hourlyReports.length; i++) {
      reportPayload = {
        reportName: hourlyReports[i],
        defaultViewBy: "hour",
        type: "CSV",
        sendEmail: 1,
        mailList: autoReports.reportEmail.split(","),
        startDate: `${lastHour}:00:00`,
        endDate: `${lastHour}:59:59`,
      };

      try {
        logger.info(`Sending auto reports mail : ${reportPayload}`);

        let response = await staticReportsService.getStaticReports(
          user,
          reportPayload,
          options
        );
      } catch (err) {
        logger.error("Failed sending email ", err);
      }
    }
  } else {
    const lastDay = currentTime.subtract(1, "day").format("YYYY-MM-DD");

    let dailyReports = autoReports.dailyReports.split(",");
    for (let i = 0; i < dailyReports.length; i++) {
      reportPayload = {
        reportName: dailyReports[i],
        defaultViewBy: "day",
        type: "CSV",
        sendEmail: true,
        mailList: autoReports.reportEmail.split(","),
        startDate: `${lastDay} 00:00:00`,
        endDate: `${lastDay} 23:59:59`,
      };

      try {
        let response = await staticReportsService.getStaticReports(
          user,
          reportPayload,
          options
        );
      } catch (err) {
        logger.error("Failed sending email ", err);
      }
    }
  }

  res.sendStatus(200);
});

const getrawcdrsData = catchAsync(async (req, res) => {
  options = pick(req.body, ["limit", "page"]);

  let response = await cdrReportsService.getrawcdrs(
    req.user,
    req.body,
    options,
    res
  );
  if (response) {
    res.status(200).send(response);
  }
});

const downloadNetworkReports = catchAsync(async (req, res) => {
  const payload = req.body;
  let response;

  const reportsNames = req.body.reportName
    ? [req.body.reportName]
    : config.NETWORK_REPORTS.reports_name.split(",").map((name) => name.trim());

  console.log(
    "reportsNames->",
    config.NETWORK_REPORTS.reports_name,
    reportsNames
  );

  for (const reportName of reportsNames) {
    payload.reportName = reportName;
    // Fetch the response data for the report

    response = await networkReportsService.downloadNetworkReports(payload);
  }

  if (response) {
    res.status(200).send({ message: "Succesfully downloaded" });
  } else {
    res.status(404).send({ message: "No data available for the report" });
  }
});

const downloadBillingReport = catchAsync(async (req, res) => {
  const user = await userService.getUserByEmail(adminUser.email);

  const reportsNames = req.body.reportName
    ? [req.body.reportName]
    : config.BILLING_REPORTS.reports_name.split(",").map((name) => name.trim());

  if (req.body.timezone) user.timezone = req.body.timezone;
  if (!user.timezone) user.timezone = DEFAULT_TIMEZONE;

  const startDate = req.body.startDate
    ? req.body.startDate.trim()
    : dayjs()
        .tz(user.timezone)
        .subtract(3, "day")
        .startOf("day")
        .format("YYYY-MM-DD 00:00:00");
  const endDate = req.body.endDate
    ? req.body.endDate.trim()
    : dayjs()
        .tz(user.timezone)
        .subtract(1, "day")
        .format("YYYY-MM-DD 23:59:59");

  logger.info(`Downloading billing reports for ${startDate} to ${endDate}`);

  let isResSended = false;

  if (req.body.isRefresh) {
    res.status(200).send({
      data: {
        message:
          "The report refresh has been initiated. Please check the configured report path after a while for the updated files.",
      },
    });
    isResSended = true;
  }

  for (const reportName of reportsNames) {
    const originalPayload = {
      reportName,
      defaultViewBy: "day",
      download: 1,
      startDate,
      endDate,
    };

    await processReportsByGranularity({
      user,
      originalPayload,
      unit: "day",
      isResSended,
    });
  }

  await zipSubContents(config.BILLING_REPORTS.reports_directory, 2);

  if (!isResSended) {
    res.status(200).send("Requested reports processed and saved per day.");
  }
});

const downloadFYreports = catchAsync(async (req, res) => {
  const user = await userService.getUserByEmail(adminUser.email);

  const reportsNames = req.body.reportName
    ? [req.body.reportName]
    : config.FY_REPORTS.reports_name.split(",").map((name) => name.trim());

  if (originalPayload.timezone) user.timezone = originalPayload.timezone;

  if (!user.timezone) user.timezone = DEFAULT_TIMEZONE;

  const startDate = req.body.startDate
    ? req.body.startDate.trim()
    : dayjs()
        .tz(user.timezone)
        .subtract(1, "month")
        .startOf("month")
        .format("YYYY-MM-DD 00:00:00");
  const endDate = req.body.endDate
    ? req.body.endDate.trim()
    : dayjs()
        .tz(user.timezone)
        .subtract(1, "month")
        .endOf("month")
        .format("YYYY-MM-DD 00:00:00");

  const originalPayload = {
    defaultViewBy: "month",
    download: 1,
    startDate,
    endDate,
  };

  // console.log("originalPayload->", originalPayload);
  logger.info(
    `Downloading FY report for ${originalPayload.startDate} to ${originalPayload.endDate}`
  );

  let isResSended = false;

  if (req.body.isRefresh) {
    res.status(200).send({
      data: {
        message:
          "The report refresh has been initiated. Please check the configured report path after a while for the updated files.",
      },
    });
    isResSended = true;
  }

  for (const reportName of reportsNames) {
    const originalPayload = {
      reportName,
      defaultViewBy: "day",
      download: 1,
      startDate,
      endDate,
    };

    await processReportsByGranularity({
      user,
      originalPayload,
      unit: "month",
      isFYReport: true,
      isResSended,
    });
  }

  await zipSubContents(config.FY_REPORTS.reports_directory, 1);

  if (!isResSended) {
    res.status(200).send("Reports processed and saved per month.");
  }
});

const createSelectedColumns = catchAsync(async (req, res) => {
  const user = req.user;
  const payload = req.body;

  const userPreference = await reportsService.createSelectedColumns(
    user,
    payload
  );

  res.status(201).json(userPreference);
});

const getSelectedColumns = catchAsync(async (req, res) => {
  const user = req.user

  const preferences = await reportsService.getSelectedColumns(user.id, req.query);
  if (!preferences) {
    return res.status(404).json({ message: "Preferences not found" });
  }

  res.status(200).send(preferences);
});



// WRITING MY CODE HERE




const getNonAggregateFields = catchAsync(async (req, res) => {
  const {type} = req.query;

  let fields;

  if (type === "customer"){
    fields = Object.values(reportConfig.customerList.responseFields);
  }
  else if(type === "supplier") {
    fields = Object.values(reportConfig.supplierList.responseFields);
  }
  else{
    return res.status(400).json({
      message: "Invalid",
    });
  }

  res.status(200).json(fields);
});







module.exports = {
  getStaticReports,
  getFilterFields,
  getReportTypes,
  getCustomerList,
  getSupplierList,
  getDerivedFieldsList,
  getDestinationDetails,
  getLcrDetails,
  getcdrStatus,
  getDestinationPrimeDetails,
  getSourcePrimeDetails,
  sendAutoReportsEmail,
  getrawcdrsData,
  downloadNetworkReports,
  downloadBillingReport,
  downloadFYreports,
  createSelectedColumns,
  getSelectedColumns,
  // HERE
  getNonAggregateFields,

};
