const logger = require("../config/logger");
const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const { alertsService, auditLogService } = require("../services");
const { auditLogEvents } = require("../config/roles");
const { parse } = require("json2csv");
const XLSX = require("xlsx");
const PDFDocument = require("pdfkit-table");
const { alertExportFields } = require("../config/alertConfig");

const createGroup = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  payload.createdBy = req.user.id;
  let group = await alertsService.createGroup(payload),
    response = {
      status: "OK",
      result: {
        name: group.name,
        id: group.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.GROUP_MANAGEMENT,
      action: `Created new group ${group.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getGroups = catchAsync(async (req, res) => {
  let pageno = req.query.page ? req.query.page : 1;
  const filter = pick(req.query, ["search", "subGroup"]),
    options = pick(req.query, ["sortBy", "limit", "page"]);
  let groups = await alertsService.getGroups(req.user, filter, options);
  res.status(200).json(groups);
});

const getGroup = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await alertsService.getGroupById(id);
  res.status(200).json(response);
});

const updateGroup = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  let { id } = req.params,
    group = await alertsService.updateGroup(id, payload),
    response = {
      status: "OK",
      result: {
        name: group.name,
        id: group.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.GROUP_MANAGEMENT,
      action: `Updated group ${group.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const deleteGroup = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await alertsService.deleteGroup(id),
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.GROUP_MANAGEMENT,
      action: `Deleted group ${response.name}`,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const createAlert = catchAsync(async (req, res) => {
  const payload = { ...req.body, user: req.user };
  let response = await alertsService.createAlert(payload);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Create Alert `,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlerts = catchAsync(async (req, res) => {
  let response = await alertsService.getAlerts(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Alerts`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertById = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertById(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Alert`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const deleteAlert = catchAsync(async (req, res) => {
  let response = await alertsService.deleteAlert(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Deleted Alert ${response.name}`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const updateAlert = catchAsync(async (req, res) => {
  let response = await alertsService.updateAlert(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Updated Alert ${response.name}`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertsMetadata = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertsMetadata(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Alerts metadata`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getErrorDescriptionList = catchAsync(async (req, res) => {
  let response = await alertsService.getErrorDescriptionList(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Error Description list`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getNodeList = catchAsync(async (req, res) => {
  let response = await alertsService.getNodeList();
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Node list`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getResultCodeList = catchAsync(async (req, res) => {
  let response = await alertsService.getResultCodeList(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get result code list`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const viewAlertHistoryById = catchAsync(async (req, res) => {
  let response = await alertsService.viewAlertHistoryById(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `View alert history`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertHistoryById = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertHistoryById(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get alert history`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertNameList = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertNameList(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get alert name list`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertHistory = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertHistory(req);
  const { download, downloadType } = req.query;

  if (Number(download)) {
    console.log("alertExportFields->", alertExportFields);

    const data = response.rows;
    const exportFields = Object.keys(alertExportFields); // Get field keys
    const mappedFields = exportFields.map((field) => ({
      label: alertExportFields[field],
      value: field,
    }));

    if (downloadType.toLowerCase() === "csv") {
      try {
        const csvData = await parse(data, { fields: mappedFields });

        res.setHeader("Content-Disposition", "attachment; filename=alert_history.csv");
        res.setHeader("Content-Type", "text/csv");
        return res.status(200).send(csvData);
      } catch (error) {
        logger.error("Error generating CSV:", error);
        return res.status(500).send("Failed to generate CSV");
      }
    }

    if (downloadType.toLowerCase() === "pdf") {
      try {
        const rows = data.map((alert) => exportFields.map((field) => alert[field] || ""));
        const pdfBuffer = await new Promise((resolve) => {
          const doc = new PDFDocument({ bufferPages: true, margin: 30, size: "A4" });

          const table = {
            title: "Alert History",
            headers: exportFields.map((field) => alertExportFields[field]),
            rows,
          };

          let buffers = [];
          doc.on("data", buffers.push.bind(buffers));
          doc.on("end", () => resolve(Buffer.concat(buffers)));

          doc.table(table);
          doc.end();
        });

        res.setHeader("Content-Disposition", "attachment; filename=alert_history.pdf");
        res.setHeader("Content-Type", "application/pdf");
        return res.send(pdfBuffer);
      } catch (error) {
        logger.error("Error generating PDF:", error);
        return res.status(500).send("Failed to generate PDF");
      }
    }

    // Default to Excel (xlsx) if no valid downloadType is provided
    try {
      const formattedData = data.map((alert) =>
        exportFields.reduce((acc, field) => {
          acc[alertExportFields[field]] = alert[field] || "";
          return acc;
        }, {})
      );
  
      const worksheet = XLSX.utils.json_to_sheet(formattedData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Alert History");
  
      // Convert workbook to a binary buffer
      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
  
      // Set headers correctly for binary download
      res.setHeader("Content-Disposition", "attachment; filename=alert_history.xlsx");
      res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      res.setHeader("Content-Length", buffer.length);
  
      // Send the buffer as a binary file
      return res.status(200).end(buffer);
    } catch (error) {
      logger.error("Error generating Excel:", error);
      return res.status(500).send("Failed to generate Excel");
    }
  }

  auditLogService.addAuditLog({
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get Alert Histories list`,
  });

  res.status(200).json(response);
});

const updateAlertHistories = catchAsync(async (req, res) => {
  let response = await alertsService.updateAlertHistories(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Update Alert Histories`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getAlertNotifications = catchAsync(async (req, res) => {
  let response = await alertsService.getAlertNotifications(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `GET Alert notifications`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});


// HERE

const getNonDeletedAlertNames = catchAsync(async (req, res) => {
  console.log("getNonDeletedNames Controller")
  // const result = await alertsService.getNonDeletedAlertNames()
  console.log("HIT: /alert/history/alert-names")
  console.log("Request URL:", req.originalUrl);

  res.status(200).json({ status : "OK", result })
})

module.exports = {
  createGroup,
  getGroup,
  getGroups,
  deleteGroup,
  updateGroup,
  createAlert,
  getAlerts,
  getAlertById,
  updateAlert,
  deleteAlert,
  getAlertsMetadata,
  getErrorDescriptionList,
  getNodeList,
  getResultCodeList,
  viewAlertHistoryById,
  getAlertHistoryById,
  getAlertHistory,
  getAlertNameList,
  updateAlertHistories,
  getAlertNotifications,
  getNonDeletedAlertNames,
};
