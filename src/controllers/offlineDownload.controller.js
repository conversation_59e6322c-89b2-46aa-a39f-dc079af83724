const { offlineDownloadService } = require("../services");
const catchAsync = require("../utils/catchAsync");
const pick = require("../utils/pick");
const path = require("path");
const fs = require("fs");
const { offline_downloads } = require("../config/config");
const { adminUser } = require("../config/roles");
const getContentType = require("../utils/getContentType");

const createOfflineDownload = catchAsync(async (req, res) => {
  const payload = { ...req.body };

  let response = await offlineDownloadService.createOfflineDownload(payload);
  res.status(200).send(response);
});

const getOfflineDownloadData = catchAsync(async (req, res) => {
  const filter = pick(req.query, ["search"]);
  const options = pick(req.query, ["sortBy", "limit", "page"]);

  const { user } = req;
  console.log("user.name->", user.name)
  if (user.name.toLowerCase() != adminUser.name.toLowerCase()) {
    filter.userId = user.id; // Assuming `user.id` contains the user's unique identifier
  }
  console.log("filter->", filter)


  let response = await offlineDownloadService.getOfflineDownloadData(filter, options, req.user);
  console.log("get offline donwload done")
  res.status(200).send(response);
});

const getDownloadById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { user } = req;

  let response = await offlineDownloadService.getDownloadById(id, user);
  res.status(200).send(response);
});

const deleteOfflineDownload = catchAsync(async (req, res) => {
  const { id } = req.params;

  let response = await offlineDownloadService.deleteOfflineDownload(id);
  res.status(200).send(response);
});

const downloadFile = catchAsync(async (req, res) => {
  // console.log("req.params is ", req.params);
  const { id } = req.params;
  const { user } = req;

  // Fetch file data from the database
  let OfflineData = await offlineDownloadService.getDownloadById(id, user);

  if (!OfflineData) {
    return res.status(404).json({ message: "File not found" });
  }

  switch (OfflineData.status) {
    case offline_downloads.status.INITIATED:
      return res.status(404).json({ message: "File is in progress" });
    case offline_downloads.status.FAILED:
      return res.status(404).json({ message: "File download failed" });
  }

  // Construct file path
  // const filePath = path.resolve(
  //   Offline_downloads.download_directory,
  //   OfflineData.data.fileName
  // );
  const filePath = OfflineData.data.filePath;

  console.log("filePath is ", filePath);

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ message: "File not found on the server" });
  }

  const contentType = getContentType(filePath);
  res.setHeader('Content-Type', contentType);

  // Send file for download
  res.download(filePath, OfflineData.fileName, (err) => {
    if (err) {
      console.error("Error while sending the file:", err);
      return res.status(500).json({ message: "Unable to download the file" });
    }
  });
});

module.exports = {
  createOfflineDownload,
  getOfflineDownloadData,
  getDownloadById,
  deleteOfflineDownload,
  downloadFile,
};
