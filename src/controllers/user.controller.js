const httpStatus = require("http-status");
const multer = require("multer");
const path = require("path");
const pick = require("../utils/pick");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { userService, emailService, auditLogService } = require("../services");
const { image_endpoint, clientURL } = require("../config/config");
const { auditLogEvents } = require("../config/roles");
const logger = require("../config/logger");
const globalConfig = require("../config/globalConfig");
const generator = require("generate-password");

(createUser = catchAsync(async (req, res) => {
  let newFileName = null;
  const storage = multer.diskStorage({
      destination: "public/media",
      filename: (req, file, cb) => {
        newFileName = `user_${Date.now()}${path.extname(file.originalname)}`;
        cb(null, newFileName);
      },
    }),
    // define the maximum size for uploading

    maxSize = 3 * 1024 * 1024,
    upload = multer({
      storage,
      limits: { fileSize: maxSize },
      fileFilter: (req, file, cb) => {
        // set the filetypes, it is optionals
        const filetypes = /jpeg|jpg|png|gif|bmp/,
          mimetype = filetypes.test(file.mimetype),
          extname = filetypes.test(
            path.extname(file.originalname).toLowerCase()
          );

        if (mimetype && extname) {
          return cb(null, true);
        }

        cb(`${"Error: Failed uploading image"} ${filetypes}`);
      },

      // mypic is the name of file attribute
    }).any();

  await upload(req, res, async (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        return res
          .status(httpStatus.BAD_REQUEST)
          .send({ message: err.message });
      }
      return res.status(httpStatus.BAD_REQUEST).send({ message: err });
    }
    const payload = req.body;
    console.log("payload recieved is : ", payload);
    payload.profileImage = newFileName;
    if (payload.status && payload.status.length == 0) {
      payload.status = 1;
    }
    try {
      if (req.user) {
        payload.createdBy = req.user.id;
      }
      const password = generator.generate({
        length: 10,
        numbers: true,
        uppercase: true,
        strict: true,
      });
      payload.password = password;
      const user = await userService.createUser(payload);

      if (payload.isLdapUser != true && payload.isLdapUser != "true") {
        (emailObj = {
          from: process.env.EMAIL_FROM,
          to: payload.email,
          subject: "Welcome to Smshub!",
          template: "new_user.template",
          context: {
            user: payload.name,
            email: payload.email,
            password: payload.password,
            loginUrl: clientURL,
          },
        }),
          (emailResponse = await emailService.MailService(emailObj));
        if (!emailResponse.isSuccess) {
          //Delete created user
          userService.deleteUserById(user.id);
          return res.status(500).send({
            message: "Unable to create user. Please try again after sometime",
          });
        }
      }
      let auditLogPayload = {
        username: req.user.name,
        userId: req.user.id,
        roleName: req.user.role.name,
        event: auditLogEvents.USER_MANAGEMENT,
        action: `Created user ${payload.name}`,
        details: payload,
      };
      auditLogService.addAuditLog(auditLogPayload);
      res.status(201).send(user);
    } catch (err) {
      logger.error(`error is : ${err}`);
      res.status(500).send({ message: err.message });
    }
  });
})),
  (getUsers = catchAsync(async (req, res) => {
    if (req.user) {
      req.query.createdBy = req.user.id;
    }
    const filter = pick(req.query, ["name", "role", "search"]),
      options = pick(req.query, ["sortBy", "limit", "page"]),
      result = await userService.queryUsers(req.user, filter, options);
    for (let i = 0; i < result.data.length; i += 1) {
      if (result.data[i].profileImage) {
        result.data[i].profileImage =
          image_endpoint + result.data[i].profileImage;
      }
    }
    res.send(result);
  })),
  (getUser = catchAsync(async (req, res) => {
    const user = await userService.getUserById(req.params.userId);
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, "User not found");
    }
    if (user.profileImage) {
      user.profileImage = image_endpoint + user.profileImage;
    }

    res.send(user);
  })),
  (updateUser = catchAsync(async (req, res) => {
    logger.info("user update controller");
    const userOld = await userService.getUserById(req.params.userId);
    if (!userOld) {
      throw new ApiError(httpStatus.NOT_FOUND, "User not found");
    }
    let newFileName = null;
    const storage = multer.diskStorage({
        destination: "public/media",
        filename: (req, file, cb) => {
          newFileName = `user_${Date.now()}${path.extname(file.originalname)}`;
          cb(null, newFileName);
        },
      }),
      // define the maximum size for uploading

      maxSize = 3 * 1024 * 1024,
      upload = multer({
        storage,
        limits: { fileSize: maxSize },
        fileFilter: (req, file, cb) => {
          // set the filetypes, it is optionals
          const filetypes = /jpeg|jpg|png|gif|bmp/,
            mimetype = filetypes.test(file.mimetype),
            extname = filetypes.test(
              path.extname(file.originalname).toLowerCase()
            );

          if (mimetype && extname) {
            return cb(null, true);
          }
          cb(`${"Error: Failed uploading image "}} ${filetypes}`);
        },
      }).any();

    await upload(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          return res
            .status(httpStatus.BAD_REQUEST)
            .send({ message: err.message });
        }
        return res.status(httpStatus.BAD_REQUEST).send({ message: err });
      }

      const payload = req.body;
      if (req.files && req.files.length > 0) {
        payload.profileImage = newFileName;
      } else {
        payload.profileImage = null;
      }
      try {
        const user = await userService.updateUser(userOld, payload);
        if (user.profileImage) {
          user.profileImage = image_endpoint + user.profileImage;
        }
        let auditLogPayload = {
          username: req.user.name,
          userId: req.user.id,
          roleName: req.user.role.name,
          event: auditLogEvents.USER_MANAGEMENT,
          action: `Updated user ${user.name}`,
          details: payload,
        };
        auditLogService.addAuditLog(auditLogPayload);
        res.status(httpStatus.OK).send(user);
      } catch (err) {
        logger.error(`error in user update : ${err.message}`);
        res.status(err.statusCode).send({ message: err.message });
      }
    });
  })),
  (deleteUser = catchAsync(async (req, res) => {
    const user = await userService.getUserById(req.params.userId);
    if (user) {
      await userService.deleteUserById(req.params.userId);
      let auditLogPayload = {
        username: req.user.name,
        userId: req.user.id,
        roleName: req.user.role.name,
        event: auditLogEvents.USER_MANAGEMENT,
        action: `Deleted user ${user.name}`,
      };
      auditLogService.addAuditLog(auditLogPayload);
      res.status(httpStatus.OK).send({ message: "User deletion success" });
    } else {
      res.status(httpStatus.BAD_REQUEST).send({ message: "User not found" });
    }
  })),
  (getAllAuditLogs = catchAsync(async (req, res) => {
    const filter = pick(req.query, [
        "event",
        "role",
        "search",
        "startDate",
        "endDate",
      ]),
      options = pick(req.query, [
        "sortBy",
        "limit",
        "page",
        "download",
        "type",
      ]);
    console.log("user is ", req.user.timezone);
    logs = await auditLogService.getLogs(req.user, filter, options);
    res.send(logs);
  })),
  (getAuditLogEvents = catchAsync(async (req, res) => {
    let events = Object.values(auditLogEvents);
    res.send(events);
  })),
  (getGlobalConfig = catchAsync(async (req, res) => {
    res.send(globalConfig);
  }));

module.exports = {
  createUser,
  getUsers,
  getUser,
  updateUser,
  deleteUser,
  getAllAuditLogs,
  getAuditLogEvents,
  getGlobalConfig,
};
