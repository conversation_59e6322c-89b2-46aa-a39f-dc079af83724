const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const { dashboardService, auditLogService } = require("../services");
const { auditLogEvents } = require("../config/roles");
const logger = require("../config/logger");

const createDashboard = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  payload.createdBy = req.user.id;
  let dashboard = await dashboardService.createDashboard(payload),
    response = {
      status: "OK",
      message: "Dashboard created successfully",
      result: {
        name: dashboard.name,
        id: dashboard.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.DASHBOARD_MANAGEMENT,
      action: `Created new dashboard ${dashboard.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getDashboards = catchAsync(async (req, res) => {
  let pageno = req.query.page ? req.query.page : 1;
  const filter = pick(req.query, ["search", "createdBy"]),
    options = pick(req.query, ["sortBy", "limit", "page"]);
  let dashboards = await dashboardService.getDashboards(
    req.user,
    filter,
    options
  );
  res.status(200).json(dashboards);
});

const getDashboard = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await dashboardService.getDashboard(id);
  res.status(200).json(response);
});

const getDashboardView = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await dashboardService.getDashboardView(req.user, id, req.query);
  res.status(200).json(response);
});

const updateDashboard = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  let { id } = req.params,
    dashboard = await dashboardService.updateDashboard(id, payload),
    response = {
      status: "OK",
      message: "Dashboard updated successfully",
      result: {
        name: dashboard.name,
        id: dashboard.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.DASHBOARD_MANAGEMENT,
      action: `Updated dashboard ${dashboard.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const deleteDashboard = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await dashboardService.deleteDashboard(id),
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.DASHBOARD_MANAGEMENT,
      action: `Deleted dashboard ${response.name}`,
    };
  response.message = "Dashboard deleted successfully";
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

module.exports = {
  createDashboard,
  getDashboard,
  getDashboards,
  updateDashboard,
  deleteDashboard,
  getDashboardView,
};
