const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const { panelService, auditLogService } = require("../services");
const { auditLogEvents } = require("../config/roles");
const logger = require("../config/logger");
const reportConfig = require("../config/reportConfig");

const createPanel = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  payload.createdBy = req.user.id;
  let panel = await panelService.createPanel(req.user, payload),
    response = {
      status: "OK",
      message: "Panel created successfully",
      result: {
        name: panel.name,
        id: panel.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.PANEL_MANAGEMENT,
      action: `Created new panel ${panel.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getPanels = catchAsync(async (req, res) => {
  let pageno = req.query.page ? req.query.page : 1;
  const filter = pick(req.query, ["search", "type", "createdBy"]),
    options = pick(req.query, ["sortBy", "limit", "page"]);
  let panels = await panelService.getPanels(req.user, filter, options);
  res.status(200).json(panels);
});

const getPanel = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await panelService.getPanel(id);
  res.status(200).json(response);
});

const updatePanel = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  let { id } = req.params,
    panel = await panelService.updatePanel(req.user, id, payload),
    response = {
      status: "OK",
      message: "Panel updated successfully",
      result: {
        name: panel.name,
        id: panel.id,
      },
    },
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.PANEL_MANAGEMENT,
      action: `Updated panel ${panel.name}`,
      details: payload,
    };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const deletePanel = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await panelService.deletePanel(id),
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.PANEL_MANAGEMENT,
      action: `Deleted panel ${response.name}`,
    };
  response.message = "Panel deleted successfully";
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getPanelVisualizationTypes = catchAsync(async (req, res) => {
  let response = Object.values(reportConfig.panelVisualizationTypes);
  res.status(200).json(response);
});
const getPanelProperties = catchAsync(async (req, res) => {
  let { type } = req.query;
  if (!type) {
    type = "Line Graph";
  }
  let response = JSON.parse(JSON.stringify(reportConfig.panelProperties[type]));
  if (!response) {
    res.status(400).json({ message: "Invalid visualization type" });
  } else {
    if (!req.user.isSuperAdmin) {
      let filters = JSON.parse(JSON.stringify(response.filters)),
        j = 0;
      for (let i = 0; i < response.filters.length; i++, j++) {
        if (req.user.type == "Customer") {
          if (
            !reportConfig.customerRoleFields.includes(response.filters[i].field)
          ) {
            filters.splice(j, 1);
            j--;
          }
        }
        if (req.user.type == "Supplier") {
          if (
            !reportConfig.supplierRoleFields.includes(response.filters[i].field)
          ) {
            filters.splice(j, 1);
            j--;
          }
        }
      }
      response.filters = filters;

      let columns;
      switch (type) {
        case "Table Report":
          columns = JSON.parse(JSON.stringify(response.columns.tableFields));
          j = 0;
          for (let i = 0; i < response.columns.tableFields.length; i++, j++) {
            if (req.user.type == "Customer") {
              if (
                !reportConfig.customerRoleFields.includes(
                  response.columns.tableFields[i]
                )
              ) {
                columns.splice(j, 1);
                j--;
              }
            }
            if (req.user.type == "Supplier") {
              if (
                !reportConfig.supplierRoleFields.includes(
                  response.columns.tableFields[i]
                )
              ) {
                columns.splice(j, 1);
                j--;
              }
            }
          }
          response.columns.tableFields = columns;
          break;
        case "Bar Graph":
          j = 0;
          columns = JSON.parse(JSON.stringify(response.columns["X-Axis"]));
          for (
            let i = 0, j = 0;
            i < response.columns["X-Axis"].length;
            i++, j++
          ) {
            if (req.user.type == "Customer") {
              if (
                !reportConfig.customerRoleFields.includes(
                  response.columns["X-Axis"][i]
                )
              ) {
                columns.splice(j, 1);
                j--;
              }
            }
            if (req.user.type == "Supplier") {
              if (
                !reportConfig.supplierRoleFields.includes(
                  response.columns["X-Axis"][i]
                )
              ) {
                columns.splice(j, 1);
                j--;
              }
            }
          }
          response.columns["X-Axis"] = columns;
      }
    }
    response.type = type;
    res.status(200).json(response);
  }
});

const getPanelPreview = catchAsync(async (req, res) => {
  const payload = req.body;
  const response = await panelService.getPanelData(req.user, payload, res);
  // console.log("response->", response)
  res.status(200).send(response);
});

module.exports = {
  createPanel,
  getPanel,
  getPanels,
  updatePanel,
  deletePanel,
  getPanelVisualizationTypes,
  getPanelProperties,
  getPanelPreview,
};
