const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const { grafanaWebhookService } = require("../services");
const logger = require("../config/logger");
const httpStatus = require("http-status");
const { fetchAlertsHistory } = require("../services/cron.service");

exports.grafanaWebhook = catchAsync(async (req, res) => {
  const emailResponse = await grafanaWebhookService.grafanaWebhook(req);
  if (!emailResponse.isSuccess) {
    return res
      .status(httpStatus.INTERNAL_SERVER_ERROR)
      .send({ message: "Failed sending mail" });
  }
  setTimeout(() => {
    fetchAlertsHistory();
  }, 2 * 1000);
  res.status(httpStatus.OK).json({ message: "Email sent successfully" });
});
