const pick = require("../utils/pick");
const catchAsync = require("../utils/catchAsync");
const { cardsService, auditLogService } = require("../services");
const { auditLogEvents } = require("../config/roles");
const logger = require("../config/logger");

const createCard = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  payload.createdBy = req.user.id;
  let card = await cardsService.createCard(payload, req.user);

  const response = {
    status: "OK",
    message: "Card created successfully",
    result: {
      name: card.name,
      id: card.id,
    },
  };
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.CARD_MANAGEMENT,
    action: `Created new card ${card.name}`,
    details: payload,
  };
  await auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getCards = catchAsync(async (req, res) => {
  let pageno = req.query.page ? req.query.page : 1;
  const filter = pick(req.query, ["search", "createdBy", "values", "userAnalysis", "timezone"]);
  const options = pick(req.query, ["sortBy", "limit", "page"]);

  let groups = await cardsService.getCards(req.user, filter, options);
  res.status(200).json(groups);
});

const getCard = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await cardsService.getCard(req.user, id, req.query);
  res.status(200).json(response);
});

const updateCard = catchAsync(async (req, res) => {
  const payload = { ...req.body };
  let { id } = req.params;
  const card = await cardsService.updateCard(id, payload, req.user);

  const response = {
    status: "OK",
    message: "Card updated successfully",
    result: {
      name: card.name,
      id: card.id,
    },
  };
  const auditLogPayload = {
    username: req.user.name,
    roleName: req.user.role.name,
    userId: req.user.id,
    event: auditLogEvents.CARD_MANAGEMENT,
    action: `Updated card ${card.name}`,
    details: payload,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const deleteCard = catchAsync(async (req, res) => {
  const { id } = req.params;
  let response = await cardsService.deleteCard(id),
    auditLogPayload = {
      username: req.user.name,
      userId: req.user.id,
      roleName: req.user.role.name,
      event: auditLogEvents.CARD_MANAGEMENT,
      action: `Deleted card ${response.name}`,
    };
  (response.message = "Card deleted successfully"),
    auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

const getCardDetails = catchAsync(async (req, res) => {
  const payload = req.body;
  const response = await cardsService.getCardDetails(payload, req.user);
  res.status(200).json(response);
});

module.exports = {
  createCard,
  getCard,
  getCards,
  updateCard,
  deleteCard,
  getCardDetails,
};
