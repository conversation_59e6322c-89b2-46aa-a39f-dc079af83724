const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");
const { alertsNotificationHistoryService } = require("../services");
const auditLogService = require("../services/audit-log.service");
const { auditLogEvents } = require("../config/roles");

/**
 * Get alert notification logs
 */
const getAlertsNotificationHistory = catchAsync(async (req, res) => {
  console.log("get notification")
  const result = await alertsNotificationHistoryService.getAlertsNotificationHistory(req);
  
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: "Get alert notification logs",
  };
  auditLogService.addAuditLog(auditLogPayload);
  
  res.status(httpStatus.OK).json({
    status: "OK",
    ...result,
  });
});

/**
 * Get alert notification log by ID
 */
const getAlertsNotificationHistoryById = catchAsync(async (req, res) => {
  const { alertHistoryId } = req.params;
  const result = await alertsNotificationHistoryService.getAlertsNotificationHistoryById(alertHistoryId);
  
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get alert notification log by ID: ${alertHistoryId}`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  
  res.status(httpStatus.OK).json({
    status: "OK",
    result,
  });
});


const viewAlertsNotificationById = catchAsync(async (req, res) => {
  const { alertHistoryId } = req.params;
  const result = await alertsNotificationHistoryService.viewAlertsNotificationById(alertHistoryId);
  
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get alert notification log by ID: ${alertHistoryId}`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  
  res.status(httpStatus.OK).json({
    status: "OK",
    result,
  });
});

/**
 * Update alert notification log status
 */
const updateAlertsNotificationHistoryStatus = catchAsync(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;
  
  const result = await alertsNotificationHistoryService.updateAlertsNotificationHistoryStatus(id, updateData);
  
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Update alert notification log status: ${id}`,
    details: updateData,
  };
  auditLogService.addAuditLog(auditLogPayload);
  
  res.status(httpStatus.OK).json({
    status: "OK",
    message: "Alert notification log updated successfully",
    result,
  });
});

const updateAlertNotificationHistories = catchAsync(async (req, res) => {
  let response = await alertsNotificationHistoryService.updateAlertNotificationHistories(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Update alert notification log status`,
  };
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});


const getAlertNameList = catchAsync(async (req, res) => { 
  let response = await alertsNotificationHistoryService.getAlertNameList(req);
  const auditLogPayload = {
    username: req.user.name,
    userId: req.user.id,
    roleName: req.user.role.name,
    event: auditLogEvents.ALERT_MANAGEMENT,
    action: `Get alert name list`,
  };  
  auditLogService.addAuditLog(auditLogPayload);
  res.status(200).json(response);
});

module.exports = {
  getAlertsNotificationHistory,
  getAlertsNotificationHistoryById,
  viewAlertsNotificationById,
  updateAlertsNotificationHistoryStatus,
  updateAlertNotificationHistories,
  getAlertNameList
};
