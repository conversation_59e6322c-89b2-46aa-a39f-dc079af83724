const httpStatus = require('http-status');
const { Op,ValidationError } = require('sequelize');
const { models } = require('../models');
const ApiError = require('../utils/ApiError');
const roleService = require('./role.service')
const XLSX = require('xlsx');
const logger = require('../config/logger');
const { convertToClientTime } = require('../utils/misc')


const PDFDocument = require("pdfkit-table"),


  { User,Role,Audit_log } = models,


  addAuditLog = async (payload) => {
    try {
    const log = await Audit_log.create(payload);
    return log;
    }
    catch (e) {
     
      if (e instanceof ValidationError) {
          e.errors.forEach((error) => {
              let message;
              switch (error.validatorKey) {
                  default:
                      logger.error(error.message);

              }
          });
      }
      else {
          
          logger.error(e);
      }
    }
  },


  getLogs = async (user,filter,options) => {
    let logs,
      filterClause = {};

    if (!user.isSuperAdmin) {
      filterClause = { userId: user.id }
    }

    let limit,offset,page;
    if (options) {
      limit = Number(options.limit) || 10;
      page = Number(options.page) || 1;
      offset = (page - 1) * limit;
    }

    if (Object.keys(filter).length == 0) {
      if (options.download) {
        logs = await Audit_log.findAndCountAll({
          order: [
            [
              'createdAt',
              'DESC'
            ]
          ],
          raw: true,
          where: filterClause
        });
      }
      else {
        logs = await Audit_log.findAndCountAll({
          limit,
          offset,
          order: [
            [
              'createdAt',
              'DESC'
            ]
          ],
          where: filterClause
        });
      }
    }
    else {
      let filterQuery = [];

      if (filter.search) {
        let searchQuery = filter.search;
        var condition = filter.search
          ? {
            [Op.or]: {
              username: { [Op.like]: `%${searchQuery}%` },
              roleName: { [Op.like]: `%${searchQuery}%` },
              event: { [Op.like]: `%${searchQuery}%` },
              action: { [Op.like]: `%${searchQuery}%` }
            }
          }
          : null;
        filterQuery.push(condition)
      }
      if (filter.startDate) {
        var condition = filter.startDate
          ? { createdAt: { [Op.gte]: filter.startDate } }
          : null;
        filterQuery.push(condition)
      }
      if (filter.endDate) {
        var condition = filter.endDate
          ? { createdAt: { [Op.lte]: filter.endDate } }
          : null;
        filterQuery.push(condition)
      }
      if (filter.role) {
        filterQuery.push({ roleName: { [Op.like]: `%${filter.role}%` } })
      }
      if (filter.event) {
        filterQuery.push({ event: filter.event} )
      }

      if (Object.keys(filterClause).length > 0) {
        filterQuery.push(filterClause)
      }
      // filter = "{[Op.or]: [name: { [Op.like]: '%' " + searchQuery + "'%' },description: { [Op.like]: '%' + searchQuery2 + '%' }]}"
      if (options.download) {
        logs = await Audit_log.findAndCountAll({
          where: filterQuery,
          order: [
            [
              'createdAt',
              'DESC'
            ]
          ],
          raw: true
        });
      }
      else {
        logs = await Audit_log.findAndCountAll({
          limit,
          offset,
          where: filterQuery,
          order: [
            [
              'createdAt',
              'DESC'
            ]
          ]
        });
      }
    }

    if (options.download) {
      let response = logs.rows;

      const row = [];
      for (let i = 0; i < response.length; i += 1) {
        let rowdata = [];
        createdDate = await convertToClientTime(response[i].createdAt,user.timezone);
        rowdata = [
          createdDate,
          response[i].username,
          response[i].roleName,
          response[i].event,
          response[i].action
        ]
        row.push(rowdata);
      }

      let heading = [
        "Timestamp",
        "Username",
        "Role",
        "Event",
        "Activity"
      ];
      if (options.type == "PDF") {
        const pdfBuffer = new Promise((resolve) => {
          let doc = new PDFDocument({
            bufferPages: true,
            margin: 30,
            size: 'A4'
          });

          const table = {
            title: "Audit Logs",
            subtitle: "",
            headers: heading,
            rows: row
          };

          let buffers = [];
          doc.on('data',buffers.push.bind(buffers));
          doc.on('end',() => {

            let pdfData = Buffer.concat(buffers);
            resolve(pdfData);

          });

          doc.table(table);
          // done!
          doc.end();
        })

        return pdfBuffer
      }
      else if (options.type == "CSV") {
        let csv = row.join("\n");
        csv = `${heading.toString()}\n${csv}`;
        return Buffer.from(csv);

      }

      wb = XLSX.utils.book_new();

      // create empty sheet
      const ws = XLSX.utils.aoa_to_sheet([heading]);

      // add row to sheet
      XLSX.utils.sheet_add_aoa(ws,row,{ origin: -1 });

      XLSX.utils.book_append_sheet(wb,ws,"Sheet1");
      buf = XLSX.write(wb,{
        type: 'buffer',
        bookType: 'xlsx'
      });
      return buf;

    }
    let result = logs.rows;
    
    for (let i = 0; i < result.length; i++) {
      result[i].dataValues.createdAt = await convertToClientTime(result[i].dataValues.createdAt,user.timezone);
      result[i].dataValues.updatedAt = await convertToClientTime(result[i].dataValues.updatedAt,user.timezone);
    }

    let responseTobeSent = {
      totalCount: logs.count,
      pageNo: page,
      pageSize: limit,
      data: result
    }
    return responseTobeSent;

  },


  /**
   * delete log by id
   * @param {ObjectId} userId
   * @returns {Promise<User>}
   */
  deletelogById = async (id) => {

    await Audit_log.destroy({ where: { id } });

  };

module.exports = {
  addAuditLog,
  getLogs,
  deletelogById
};