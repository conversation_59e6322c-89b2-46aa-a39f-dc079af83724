const httpStatus = require("http-status");
const { Op, ValidationError } = require("sequelize");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const graphqlService = require("./graphql.service");
const userService = require("./user.service");
const reportsService = require("./reports.service");
const {
  convertToClientTime,
  getCustomerSupplierFilter,
  convertToUTC,
  addCustomerBindClause,
} = require("../utils/misc");
const {
  derivedReportFields,
  columnMapping,
} = require("../config/reportConfig");
const logger = require("../config/logger");
const dayjs = require("dayjs");
const config = require("../config/config");
const {
  handleRegularFilters,
  handleOtherFilters,
} = require("../utils/build-advanced-filter");
const reportConfig = require("../config/reportConfig");
const roleConfig = require("../config/roles");
const findFilterFlag = require("../utils/findFilterFlag");
const getUserDetails = require("../utils/getUserDetails");

const { Card } = models;

const createCard = async (payload, user) => {
  let card;
  if (
    !payload.reportField ||
    !derivedReportFields.includes(payload.reportField)
  ) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Not a valid report field");
  }
  let cardList = await Card.findAll({
    where: {
      reportField: payload.reportField,
      createdBy: payload.createdBy,
    },
  });
  if (cardList && cardList.length > 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Card is already created with Derived field. Please choose different field "
    );
  }
  try {
    if (!payload?.filterFlag) {
      const filterFlag = findFilterFlag(payload, user, true);
      payload.filterFlag = filterFlag;
    }

    card = await Card.create(payload);
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(httpStatus.BAD_REQUEST, "Card name used before");

          case "is_null":
          case "notEmpty":
            if (error.path == "name") {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                "Name cannot be empty"
              );
            } else if (error.path == "reportField") {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                "Report field cannot be empty"
              );
            }
        }
      });
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, e.message);
    }
  }
  return card;
};

const getCard = async (user, id, filter) => {
  let cardWithValue;
  const card = await Card.findOne({
    where: { id },
    raw: true,
  });
  if (!card) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Card doesnot exists");
  }
  if (filter.values) {
    cardWithValue = await fetchCardWithValue(user, card, filter);
    return cardWithValue;
  }
  return card;
};

const getCardByName = async (name) => {
  const card = await Card.findOne({ where: { name } });
  if (!card) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Card doesnot exists");
  }
  return card;
};

const getCards = async (user, filter, options) => {
  let limit, offset, page;

  if (options) {
    limit = Number(options.limit) || 20;
    page = Number(options.page) || 1;
    offset = (page - 1) * limit;
  }
  // let createdByFlag = filter.createdBy || "self";
  let createdBy;
  if (user.isSuperAdmin && filter.createdBy) {
    createdBy = filter.createdBy;
  } else {
    createdBy = user.id;
  }
  filter.userId = filter.createdBy
  delete filter.createdBy;
  /*  if (createdByFlag == "others") {
          let dynamicDashboardAdmin = user.role.dataValues.dynamicDashboard,
              cardsShared = [];
          if (dynamicDashboardAdmin) {
 
              for (let i = 0; i < dynamicDashboardAdmin.count; i++) {
                  try {
                      let dd = await dashboardService.getDashboardcards(dynamicDashboardAdmin[i])
                      if (dd.cards) {
                          cardsShared.push(dd.cards)
                      }
                  }
                  catch (err) {
                      console.log("dashboard not found ", err);
                  }
              }
          }
          let roleCards = user.role.dataValues.cards;
          if (roleCards && roleCards.length > 0) {
              for (let j = 0; j < roleCards.length; j++) {
                  let hasCard = cardsShared.some((cardShared) => cardShared.id === roleCards[j])
                  if (!hasCard) {
                      let details = Card.findByPk(roleCards[j]);
                      if (details) {
                          cardsShared.push(details);
                      }
                  }
 
              }
          }
          responseTobeSent = {
              totalCount: cardsShared.count,
              pageNo: page,
              pageSize: limit,
              data: cardsShared.slice(offset, offset + limit)
          }
      }
      else {*/
  let logs = [];
  if (filter.timezone) user.timezone = filter.timezone;
  if (Object.keys(filter).length == 0) {
    logs = await Card.findAndCountAll({
      limit,
      offset,
      order: [["createdAt", "DESC"]],
      where: { createdBy },
      raw: true,
    });
  } else if (filter.search) {
    let searchQuery = filter.search;
    // filter = "{[Op.or]: [name: { [Op.like]: '%' " + searchQuery + "'%' },description: { [Op.like]: '%' + searchQuery2 + '%' }]}"
    logs = await Card.findAndCountAll({
      limit,
      offset,
      where: {
        [Op.or]: {
          name: { [Op.like]: `%${searchQuery}%` },
        },
        createdBy,
      },
      raw: true,
      order: [["createdAt", "DESC"]],
    });
  } else {
    logs = await Card.findAndCountAll({
      limit,
      offset,
      order: [["createdAt", "DESC"]],
      where: { createdBy },
      raw: true,
    });
  }

  for (let i = 0; i < logs.rows.length; i++) {
    logs.rows[i].createdAt = await convertToClientTime(
      logs.rows[i].createdAt,
      user.timezone
    );
    logs.rows[i].updatedAt = await convertToClientTime(
      logs.rows[i].updatedAt,
      user.timezone
    );
  }

  let responseTobeSent = {
    totalCount: logs.count,
    pageNo: page,
    pageSize: limit,
    data: logs.rows,
  };

  if (filter.values) {
    cardFields = [];
    for (let j = 0; j < responseTobeSent.data.length; j++) {
      const card = responseTobeSent.data[j];
      const cardDetails = await fetchCardWithValue(user, card, filter);
      responseTobeSent.data[j] = cardDetails;
    }
  }
  return responseTobeSent;
};

const updateCard = async (cardId, payload, user) => {
  let card = await Card.findByPk(cardId);
  if (!card) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Card doesnot exists");
  }
  if (payload.reportField) {
    let cardList = await Card.findAll({
      where: {
        reportField: payload.reportField,
        createdBy: card.createdBy,
        id: { [Op.ne]: cardId },
      },
    });
    if (cardList && cardList.length > 0) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Card is already created with Derived field. Please choose different field "
      );
    }
  }
  try {
    if (!payload?.filterFlag) {
      const filterFlag = findFilterFlag(payload, user, true);
      payload.filterFlag = filterFlag;
    }

    Object.assign(card, payload);
    await card.save();
    return card;
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(httpStatus.BAD_REQUEST, "Card name used before");
        }
      });
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, e.message);
    }
  }
};

const deleteCard = async (id) => {
  let card = await Card.findByPk(id);
  if (!card) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Card doesnot exists");
  }

  await Card.destroy({ where: { id } });
  return card;
};

const getCardDetails = async (payload, user) => {
  const cardsWithValues = [];

  payload.negativeReport = payload.reportFilters.negative_report ? true : false;
  delete payload?.reportFilters?.negative_report;

  try {
    const { cardIds = [] } = payload;

    if (!cardIds.length) {
      throw new ApiError(httpStatus.BAD_REQUEST, "No card IDs provided");
    }

    // Fetch all card definitions
    const cards = await Card.findAll({
      where: { id: cardIds },
      raw: true,
    });

    if (!user?.timezone) user.timezone = config.DEFAULT_TIMEZONE;

    if (!cards.length) {
      throw new ApiError(httpStatus.NOT_FOUND, "No cards found");
    }

    for (let j = 0; j < cards.length; j++) {
      let card = cards[j];
      if (payload.reportFilters) {
        card.reportFilters = payload.reportFilters;
      }

      if (payload.startDate && payload.endDate) {
        card.startDate = payload.startDate;
        card.endDate = payload.endDate;
      }
      const cardDetails = await fetchCardWithValue(user, card);
      cardsWithValues.push(cardDetails);
    }

    return cardsWithValues;
  } catch (error) {
    throw error;
  }
};

const fetchCardWithValue = async (user, card, filter = {}) => {

  const fieldName = columnMapping[card.reportField];
  if (!fieldName) return card; // Skip if no mapping exists

  const cardFields = [fieldName];

  // For user analysis use user based filters
  if (filter.userAnalysis && filter.userId) {
    user = await getUserDetails(filter.userId)
  }

  if (!user.timezone) user.timezone = config.DEFAULT_TIMEZONE;

  // Prepare date range
  let startDate = card.startDate
    ? card.startDate
    : dayjs().tz(user.timezone).format("YYYY-MM-DD 00:00:00");
  let endDate = card.endDate
    ? card.endDate
    : dayjs().tz(user.timezone).format("YYYY-MM-DD HH:mm:ss");

  const reportReqData = reportConfig.reportsMapping["Dynamic Report"];

  let whereClause = "";
  let reportFilterClause = "";
  const filterFlag = findFilterFlag(card, user, true);

  // Build reportFilters for whereClause
  if (card.reportFilters) {
    const reportFilters = card.reportFilters;
    const { otherFilters = {} } = reportReqData || {};
    const otherFilterFields = Object.keys(otherFilters);

    const cleanedOtherPayload = {};
    const cleanedRegularFilters = {};
    const andGroups = [];

    // Separate other filters and regular filters
    for (const [key, value] of Object.entries(reportFilters)) {
      if (
        (Array.isArray(value) && value.length === 0) ||
        value === null ||
        value === undefined ||
        value === ""
      ) {
        continue; // skip empty
      }

      if (otherFilterFields.includes(key)) {
        cleanedOtherPayload[key] = value;
      } else {
        cleanedRegularFilters[key] = value;
      }
    }

    // Construct raw query for other filters
    if (Object.keys(cleanedOtherPayload).length !== 0) {
      andGroups.push(
        ...(await handleOtherFilters(
          cleanedOtherPayload,
          reportReqData,
          card,
          user
        ))
      );
    }
    // Construct raw query for regular filters
    if (Object.keys(cleanedRegularFilters).length !== 0) {
      andGroups.push(
        ...handleRegularFilters(cleanedRegularFilters, reportReqData)
      );
    }

    reportFilterClause = andGroups.length > 0 ? andGroups.join(" and ") : null;

    delete card.reportFilters;
  }

  if (reportFilterClause) whereClause = reportFilterClause;

  // Build user-specific card
  let customerResponse = null;
  if (!user.isSuperAdmin) {
    customerResponse = await getCustomerSupplierFilter({
      user,
      reportFields: cardFields,
      megaAPI: true,
      filterFlag,
    });
  }
  if (customerResponse) whereClause = customerResponse;

  const bindClause = await addCustomerBindClause(user, cardFields, true);
  if (bindClause) {
    whereClause = whereClause ? `${whereClause} and ${bindClause}` : bindClause;
  }

  // Build GraphQL query
  let tempQuery = `getSmsCdr(viewBy :day , startTime :"${startDate}", endTime : "${endDate}", timeZone : "${user.timezone}"`;
  if (whereClause) tempQuery += `, where : "${whereClause}"`;
  tempQuery += `){items{${fieldName}}}`;

  // Fetch data
  const graphqlResponse = await graphqlService.getGraphqlData({
    name: "getSmsCdr",
    payload: tempQuery,
  });

  const data = graphqlResponse.items;

  // Map back value
  if (Array.isArray(data) && data.length > 0) {
    card.value = data[0][fieldName];
  }

  return card;
};

module.exports = {
  createCard,
  getCard,
  getCards,
  updateCard,
  deleteCard,
  getCardDetails,
  fetchCardWithValue,
};
