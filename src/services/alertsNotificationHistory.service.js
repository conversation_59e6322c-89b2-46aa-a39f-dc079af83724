
const { models } = require("../models");
const { Op } = require("sequelize");
const { alertConfig, alertGrafanaStates } = require("../config/alertConfig");
const { usePageLimit, getOrDefaultDateRange } = require("../utils/helper");
const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");

const { AlertsModel, AlertNotificationHistory } = models;

const createAlertNotificationHistorys = async (payload) => {
  try {
    const { alertId, name, description, state, startsAt, everyRunMinute, intervalStart, intervalEnd, status, category, alertType, triggeredAt, notificationPayload } = payload;
    const history = await AlertNotificationHistory.create({
      alert_id: alertId,
      name,
      description,
      state,
      startsAt,
      every_run_minute: everyRunMinute,
      interval_start: intervalStart,
      interval_end: intervalEnd,
      status,
      category,
      alert_type: alertType,
      triggered_at: triggeredAt,
      notification_payload: notificationPayload
    });
    return history;
  } catch (error) {
    console.error("error in create notification history", error)
    throw error
  }
};

/**
 * Get alert notification history with pagination and filtering
 * @param {Object} req - Request object with query parameters
 * @returns {Object} - Paginated list of alert notification history
 */
const getAlertsNotificationHistory = async (req) => {
  try {
    const { limit, offset } = usePageLimit(req);
    const { endDate, startDate } = await getOrDefaultDateRange(req);

    const whereClause = {
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
    };

    // Add status filter if provided
    if (req.query.status) {
      whereClause.status = {
        [Op.in]: req.query.status.split(",").map((s) => s.trim()),
      };
    }

    // Add alert_type filter if provided
    if (req.query.alert_type) {
      whereClause.alert_type = {
        [Op.like]: `%${req.query.alert_type}%`,
      };
    }

    // Add search functionality
    if (req.query.search) {
      const searchTerm = req.query.search;
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${searchTerm}%` } },
        { description: { [Op.like]: `%${searchTerm}%` } },
        { alert_type: { [Op.like]: `%${searchTerm}%` } },
      ];
    }

    const sortBy = [];
    if (req.query.sortBy) {
      const [field, direction] = req.query.sortBy.split(":");
      sortBy.push([field, (direction || "DESC").toUpperCase()]);
    } else {
      sortBy.push(["createdAt", "DESC"]);
    }

    const result = await AlertNotificationHistory.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: sortBy,
      attributes: [
        "id",
        "alert_id",
        "name",
        "description",
        "state",
        "startsAt",
        "every_run_minute",
        "interval_start",
        "interval_end",
        "status",
        "category",
        "alert_type",
        "createdAt",
        "updatedAt",
      ],
    });

    return {
      results: result.rows,
      totalResults: result.count,
      page: Math.floor(offset / limit) + 1,
      limit,
      totalPages: Math.ceil(result.count / limit),
    };
  } catch (error) {
    console.error("Error getting alert notification history:", error);
    throw error;
  }
};

/**
 * Get alert notification history by ID
 * @param {number} id - Alert notification history ID
 * @returns {Object} - Alert notification history details
 */
const getAlertsNotificationHistoryById = async (id) => {
  try {
    console.log("id->", id)
    const history = await AlertNotificationHistory.findByPk(id);
    if (!history) {
      throw new Error("Alert notification history not found");
    }
    return history;
  } catch (error) {
    console.error("Error getting alert notification history by ID:", error);
    throw error;
  }
};



const viewAlertsNotificationById = async (id) => {
  const { buildObjectFromAlert, extractValuesFromValueString } = require("./grafanaWebhook.service");

  try {
    const history = await AlertNotificationHistory.findByPk(id);
    if (!history) {
      throw new Error("Alert notification history not found");
    }

    const { notification_payload } = history;

    const labels = notification_payload.labels;
    const extractedValues = extractValuesFromValueString(notification_payload.valueString);
    // console.log("extractedValues", extractedValues);

    let obj = buildObjectFromAlert(labels, extractedValues, history.alert_type);
    let customerName = "";
    if (labels) {
      customerName = labels.customer_name;
      // customerName = labels.replace(/customer_name=([^ ]+)/, "$1");
      // customerNameArr.push(customerName);
    }

    if (customerName) {
      obj = {
        Customer: customerName,
        ...obj,
      };
    }
    return [Object.keys(obj), Object.values(obj)];

  } catch (error) {
    console.error("Error getting alert notification history by ID:", error);
    throw error;
  }
}

/**
 * Update alert notification history status
 * @param {number} id - Alert notification history ID
 * @param {Object} updateData - Data to update
 * @returns {Object} - Updated alert notification history
 */
const updateAlertsNotificationHistoryStatus = async (id, updateData) => {
  try {
    const history = await AlertNotificationHistory.findByPk(id);
    if (!history) {
      throw new Error("Alert notification history not found");
    }

    // Validate status if provided
    if (updateData.status) {
      const validStatuses = [
        alertConfig.alertsHistoryStatus.ANALYZING,
        alertConfig.alertsHistoryStatus.OPEN,
        alertConfig.alertsHistoryStatus.CLOSED,
        alertConfig.alertsHistoryStatus.FIXED,
        alertConfig.alertsHistoryStatus.DELETED,
      ];

      if (!validStatuses.includes(updateData.status)) {
        throw new Error(`Invalid status. Valid statuses are: ${validStatuses.join(", ")}`);
      }
    }

    await history.update(updateData);
    return history;
  } catch (error) {
    console.error("Error updating alert notification history:", error);
    throw error;
  }
};

const updateAlertNotificationHistories = async (req) => {
  const { alertHistoryIds } = req.body;

  const options = {};
  if (req._rollbackTxn) options.transaction = req._rollbackTxn;

  // Fetch all alerts by IDs
  const alertHistories = await AlertNotificationHistory.findAll({
    where: { id: { [Op.in]: alertHistoryIds } },
  });

  if (alertHistories.length === 0) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "No alert histories found for the provided IDs"
    );
  }

  // Prepare payload for update
  const payload = {
    status: req.body.status,
    updated_by: req.user.id,
  };

  // Update associated alerts in the AlertsModel
  const alertIdsToUpdate = alertHistories.map((alertH) => alertH.alert_id);
  await AlertsModel.update(
    { updated_at: new Date(), updated_by: req.user.id },
    { where: { id: { [Op.in]: alertIdsToUpdate } }, ...options }
  );

  // Update alert histories in bulk
  await AlertNotificationHistory.update(payload, {
    where: { id: { [Op.in]: alertHistoryIds } },
    ...options,
  });

  return {
    message: `${alertHistories.length} alert histories updated successfully`,
  };
};


const getAlertNameList = async (req) => {

  const sortBy = [];

  if (req.query.sortBy) {
    // Accept formats like "createdAt:desc" or "createdAt:asc"
    const [field, direction] = req.query.sortBy.split(":");
    sortBy.push([field, (direction || "DESC").toUpperCase()]);
  }

    const { startDate, endDate } = req.query;

  // const { endDate, startDate } = await getOrDefaultDateRange(req);
  console.log("endDate, startDate: ", endDate, startDate);
  const whereClause = {
    status: {
      [Op.notIn]: [alertConfig.alertsHistoryStatus.DELETED],
    },
    // state: { [Op.in]: [alertGrafanaStates.ALERTING] },
    createdAt: {
      [Op.between]: [startDate, endDate],
    },
  };
  if (req.query.status) {
    whereClause.status = {
      [Op.in]: req.query.status.split(",").map((m) => m.trim()),
    };
  }

  const searchQuery = req.query.search || "";

  if (searchQuery) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'name'
      { category: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
      { alert_type: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
    ];
  }


  const alertsHistory = await AlertNotificationHistory.findAll({
      where: whereClause,
      order: sortBy,
    attributes: ["id", "alert_id", "name", "status"],
    raw: true,
  });

  // Group by name
  const groupedAlerts = alertsHistory.reduce((acc, alert) => {
    if (!acc[alert.name]) {
      acc[alert.name] = [];
    }
    acc[alert.name].push(alert);
    return acc;
  }, {});

  // Convert groupedAlerts object into an array
  const groupedAlertsArray = Object.entries(groupedAlerts).map(
    ([name, items]) => {
      const alertId = items.find((f) => f.name === name).alert_id;
      return {
        label: name,
        value: items.map(({ id }) => id).toString(),
        alert_id: alertId,
        alerts: items.map(({ id, status }) => ({ id, status })),
      };
    }
  );
  return groupedAlertsArray;
};

module.exports = {
  createAlertNotificationHistorys,
  getAlertsNotificationHistory,
  getAlertsNotificationHistoryById,
  viewAlertsNotificationById,
  updateAlertsNotificationHistoryStatus,
  updateAlertNotificationHistories,
  getAlertNameList,
};