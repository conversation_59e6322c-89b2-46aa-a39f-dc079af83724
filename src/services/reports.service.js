const httpStatus = require("http-status");
const XLSX = require("xlsx");
const PDFDocument = require("pdfkit-table");

const ApiError = require("../utils/ApiError");
const graphqlService = require("./graphql.service");
const emailService = require("./email.service");
const groupService = require("./alerts.service");
const axios = require("axios");
const globalConfig = require("../config/globalConfig");
const path = require("path");
const fs = require("fs");
const zlib = require("zlib");
const rolesConfig = require("../config/roles");

const {
  getViewByValue,
  getCustomerSupplierFilter,
  convertToClientTime,
  formatReportDate,
  addCustomerBindClause,
} = require("../utils/misc");

const auditLogService = require("./audit-log.service");
const { auditLogEvents } = require("../config/roles");
const { ConnectionError } = require("sequelize");
const logger = require("../config/logger");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { models } = require("../models");
const { cdr_downloads } = require("../config/config");
const cdrDownloadService = require("./cdrDownload.service");
const { cdr_download, Report, UserReportPreferences } = models;
const { Parser } = require("json2csv");
const moment = require("moment");

dayjs.extend(utc);
dayjs.extend(timezone);

const customReportConfig = require("../config/customReportConfig");

const constructCSV = require("../utils/generateCsv");
const constructPDF = require("../utils/generatePdf");
const constructExcel = require("../utils/generateExcelFile");
const panelService = require("./panel.service");
const config = require("../config/config");
const reportConfig = require("../config/reportConfig");
const { separateTableAndDerivedFields } = require("../utils/helper");

const getWhereClause = async ({ user, payload, options }) => {
  let reportReqData = reportConfig.reportsMapping[payload.reportName],
    limit = 10,
    page = 1;
  if (options) {
    limit = options.limit || 10;
    page = options.page || 1;
  }

  let searchClause = "",
    whereClause = "";

  if (payload.timezone) user.timezone = payload.timezone;

  if (!user.isSuperAdmin) {
    let customerResponse = await getCustomerSupplierFilter({
      user,
      reportFields: Object.keys(reportReqData.responseFields),
    });
    if (customerResponse) whereClause = customerResponse;
  }
  let bindClause = await addCustomerBindClause(
    user,
    Object.keys(reportReqData.responseFields)
  );
  if (bindClause) {
    if (whereClause.length > 0) {
      whereClause = whereClause + " and " + bindClause;
    } else {
      whereClause = bindClause;
    }
  }

  if (payload.startDate) {
    startDate = payload.startDate;
  }
  // startDate = dayjs.tz(payload.startDate, user.timezone).utc().format("YYYY-MM-DD HH:mm:ss");
  else {
    startDate = dayjs.utc().format("YYYY-MM-DD 00:00:00");
  }

  if (payload.endDate) {
    endDate = payload.endDate;
  }
  //  endDate = dayjs.tz(payload.endDate, user.timezone).utc().format("YYYY-MM-DD HH:mm:ss");
  else {
    endDate = dayjs.utc().format("YYYY-MM-DD HH:mm:ss");
  }

  if (payload.search) {
    let fields = reportReqData.searchFields;

    for (let k = 0; k < fields.length; k++) {
      if (searchClause.length > 0) {
        searchClause = `${searchClause} or `;
      }
      searchClause = `${searchClause + fields[k]} ILIKE '%${payload.search}%' `;
    }

    if (searchClause.length > 0) {
      if (whereClause.length > 0) {
        whereClause = `(${whereClause}) and (${searchClause})`;
      } else {
        whereClause = searchClause;
      }
    }
  }
  return { whereClause, startDate, endDate };
};

const getStaticReports = async (user, payload, options) => {
  if (Object.keys(customReportConfig).includes(payload.reportName)) {
    const customReport = await customReports(payload, user);
    return customReport;
  }

  let query;
  if (!payload.reportName || !reportConfig.reportsMapping[payload.reportName]) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Report name not found");
  }

  const { whereClause } = await getWhereClause({ user, payload, options });

  if (payload.download && payload.download == 1) {
    // if (payload.reportName === "Network Reports" ) {
    //   let buf = await downloadNetworkReports(
    //     user,
    //     payload,
    //     reportReqData,
    //     whereClause
    //   );
    //   return buf.buf;
    // }
    console.time("download report");

    let buf = await downloadStaticReports(
      user,
      payload,
      reportReqData,
      whereClause
    );
    console.timeEnd("download report");

    return buf.buf;
  }
  if (payload.sendEmail && payload.sendEmail == 1) {
    let downloadData = await downloadStaticReports(
        user,
        payload,
        reportReqData,
        whereClause
      ),
      fileSizeInMB = (
        Buffer.byteLength(downloadData.buf) /
        (1024 * 1024)
      ).toFixed(2);
    if (fileSizeInMB > globalConfig.MAX_EMAIL_ATTACHMENT_SIZE) {
      throw new ApiError(413, "File size too large");
    }
    let groupMembers = [];
    if (payload.groupList) {
      groupMembers = await groupService.getGroupMembers(payload.groupList);
    }
    let emailList = payload.mailList.concat(groupMembers);
    emailList = emailList.filter(
      (item, index) => emailList.indexOf(item) === index
    );

    if (!emailList || emailList.length == 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Minimum 1 email ID required");
    }

    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: emailList.toString(),
      subject: payload.reportName,
      template: "email_report.template",
      context: {
        REPORT_NAME: payload.reportName,
        STARTDATE: downloadData.startDate,
        ENDDATE: downloadData.endDate,
      },
      attachments: {
        // encoded string as an attachment
        filename: payload.reportName + downloadData.extension,
        content: downloadData.buf,
        encoding: "base64",
      },
    };
    emailResponse = await emailService.MailService(emailObj);
  }

  let { viewBy } = reportReqData.defaultFilters;
  if (
    !reportReqData.defaultFilters.useDefaultViewBy ||
    reportReqData.defaultFilters.useDefaultViewBy == false
  ) {
    viewBy = await getViewByValue(startDate, endDate);
  }

  let pagemetaData =
    "{ \
      current_page, \
      last_page, \
      page_size, \
      total_items \
    }";

  let tempQuery;
 if (whereClause?.length > 0) {
    tempQuery = `${reportReqData.name
      }(viewBy :${viewBy},pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}",timeZone :"${user.timezone
      }"  where: " ${whereClause}"){page_meta${pagemetaData} items{${Object.keys(
        reportReqData.responseFields
      ).toString()}}}`;
  } else {
    tempQuery = `${reportReqData.name
      }(viewBy :${viewBy},pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}", timeZone :"${user.timezone
      }"){page_meta${pagemetaData} items{${Object.keys(
        reportReqData.responseFields
      ).toString()}}}`;
  }
  let response = await graphqlService.getGraphqlData({
      name: reportReqData.name,
      payload: tempQuery,
    }),
    data = response.items;
  const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

  data.map(async (obj) => {
    obj[reportConfig.timestampParam] = await formatReportDate(
      obj[reportConfig.timestampParam],
      viewBy
    );

    Object.keys(obj).map((key) => {
      if (regexPattern.test(obj[key])) {
        obj[key] = obj[key].split("+")[0];
        obj[key] = dayjs(obj[key]).format("YYYY-MMM-DD HH:mm:ss");
      }

      if (reportReqData.responseFields[key]) {
        obj[reportReqData.responseFields[key]] = obj[key];
        delete obj[key];
      }
    });
  });

  let responseTobeSent = {
    totalCount: response.page_meta.total_items,
    pageNo: response.page_meta.current_page,
    pageSize: response.page_meta.page_size,
    data,
  };
  return responseTobeSent;
};

const handleDownload = async (payload, responseData) => {
  try {
    let downloadableData = responseData.split("\n");
    if (downloadableData.length < 2) {
      throw new ApiError(httpStatus.NOT_FOUND, "No record found");
    }
    const headers = downloadableData[0].split("\t");
    let rows = [];
    for (let i = 1; i < downloadableData.length; i++) {
      if (!downloadableData[i]) {
        downloadableData[i] = "-";
      } else {
        downloadableData[i] = downloadableData[i].replace(/\\N/g, "-");
      }
      let columns = downloadableData[i].split("\t");
      const regexPattern =
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;
      if (columns.length > 0) {
        for (let j = 0; j < columns.length; j++) {
          if (regexPattern.test(columns[j])) {
            columns[j] = dayjs(columns[j]).format("YYYY-MMM-DD HH:mm:ss");
          } else if (columns[j] && !isNaN(columns[j])) {
            columns[j] = parseFloat(columns[j]);
          }
        }
        rows.push(columns);
      }
    }
    if (rows.length === 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
    }
    switch (payload.type) {
      case "EXCEL":
        return await constructExcel(headers, rows, payload);
      case "PDF":
        return await constructPDF(headers, rows, payload);
      default:
        return await constructCSV(headers, rows, payload);
    }
  } catch (error) {
    console.error("Error in handleDownload:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Error processing download"
    );
  }
};

const customReports = async (payload, user) => {
  let customerList = [];
  let supplierList = [];

  if (!user.isSuperAdmin || !user.allCustomerList || !user.allSupplierList) {
    customerList = user.customerList?.map((customer) => customer.name) || [];
    supplierList = user.supplierList?.map((supplier) => supplier.name) || [];
  }

  const reportKey = Object.keys(customReportConfig).find(
    (key) => key === payload.reportName
  );

  if (reportKey) {
    const reportConfig = customReportConfig[reportKey];

    // Check if timeZoneMapping exists and payload.timeZone is a valid key
    if (
      reportConfig.timeZoneMapping &&
      reportConfig.timeZoneMapping[payload.timezone]
    ) {
      payload.reportName = reportConfig.timeZoneMapping[payload.timezone];
    } else {
      payload.reportName = reportConfig.reportName;
    }
  }
  const apiUrl = `${process.env.DATA_SERVICE_URL}${customReportConfig[reportKey]?.apiEndpoint}`;

  const updatedPayload = {
    ...payload,
    download: payload.sendEmail
      ? "1"
      : payload.download === ""
      ? "0"
      : payload.download,
  };

  const findReportKey = (reportName) => {
    return Object.keys(customReportConfig).find((key) => {
      const config = customReportConfig[key];
      if (config.reportName === reportName) {
        return true;
      }
      if (config.timeZoneMapping) {
        return Object.values(config.timeZoneMapping).includes(reportName);
      }
      return false;
    });
  };

  if (!user.isSuperAdmin) {
    const reportKey = findReportKey(payload.reportName);
    if (
      [
        "Supplier Wise Cost Report",
        "Slab Based Billing Report (Supplier)",
      ].includes(reportKey)
    ) {
      updatedPayload.roleSearch = supplierList;
    } else if (
      [
        "Customer Wise Revenue Report",
        "Slab Based Billing Report (Customer)",
      ].includes(reportKey)
    ) {
      updatedPayload.roleSearch = customerList;
    }
  }

  if (payload.sendEmail) {
    delete updatedPayload.mailList;
    delete updatedPayload.groupList;
    delete updatedPayload.sendEmail;
  }

  try {
    const response = await graphqlService.getGraphqlCustomData({
      apiUrl,
      payload: updatedPayload,
    });
    // const response = await axios.post(apiUrl, updatedPayload, {
    //   headers: { "Content-Type": "application/json" },
    // });
    // if (response.status !== 200) {
    //   console.log("response.status->", response.status)
    //   throw new ApiError(
    //     response.status,
    //     response.statusText || "API call failed"
    //   );
    // }

    if (Number(payload.download) === 1) {
      if (payload.totalCount > config.NO_OF_ROWS_PER_FILE) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Maximum response size reached `
        );
      }
      const downloadedData = await handleDownload(payload, response.data);
      return downloadedData.buf;
    }

    if (payload.sendEmail) {
      if (payload.totalCount > config.NO_OF_ROWS_PER_FILE) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Maximum response size reached `
        );
      }
      const downloadData = await handleDownload(payload, response.data);
      const fileSizeInMB = (
        Buffer.byteLength(downloadData.buf) /
        (1024 * 1024)
      ).toFixed(2);

      if (fileSizeInMB > globalConfig.MAX_EMAIL_ATTACHMENT_SIZE) {
        throw new ApiError(413, "File size too large");
      }
      let groupMembers = [];

      if (payload.groupList) {
        groupMembers = await groupService.getGroupMembers(payload.groupList);
      }

      let emailList = [...new Set([...payload.mailList, ...groupMembers])];

      if (!emailList || emailList.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Minimum 1 email ID required"
        );
      }

      const emailObj = {
        from: process.env.EMAIL_FROM,
        to: emailList.toString(),
        subject: reportKey,
        template: "email_report.template",
        context: {
          REPORT_NAME: reportKey,
          STARTDATE: payload.startDate,
          ENDDATE: payload.endDate,
        },
        attachments: {
          filename:
            payload.type === "CSV"
              ? reportKey + ".csv"
              : payload.type === "PDF"
              ? reportKey + ".pdf"
              : reportKey + ".xlsx",
          content: downloadData.buf,
          encoding: "base64",
        },
      };

      return emailService.MailService(emailObj);
    }
    const { items, page_meta, ...rest } = response.data;
    return {
      ...rest,
      data: items,
      pageNo: page_meta.current_page,
      pageSize: page_meta.page_size,
      totalCount: page_meta.total_items,
    };
  } catch (error) {
    console.error("Error in dynamicReports:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

const getNetworkReports = async (
  user,
  payload,
  whereClause,
  options,
  pagemetaData
) => {
  const reportReqData = reportConfig.reportsMapping[payload.reportName];

  const limit = options.limit || 10;
  const page = options.page || 1;

  const startDate = payload.startDate
    ? payload.startDate
    : dayjs.utc().format("YYYY-MM-DD 00:00:00");
  const endDate = payload.endDate
    ? payload.endDate
    : dayjs.utc().format("YYYY-MM-DD HH:mm:ss");

  let { viewBy } = reportReqData.defaultFilters;
  if (
    !reportReqData.defaultFilters.useDefaultViewBy ||
    reportReqData.defaultFilters.useDefaultViewBy == false
  ) {
    viewBy = await getViewByValue(startDate, endDate);
  }

  let combinedData = [];
  let totalItems = 0;

  // Generate daily date ranges
  const dailyData = await generateDailyData({}, startDate, endDate);

  for (const dateRange of dailyData) {
    // if (combinedData.length >= limit) break; // Stop if we have enough data

   const tempQuery = `${reportReqData.name
      }(viewBy :${viewBy},pageNumber: ${page}, pageSize : ${limit}, startTime :"${dateRange.data.start_time
      }", endTime : "${dateRange.data.end_time}", timeZone :"${user.timezone}"${whereClause?.length > 0 ? `, where: " ${whereClause}"` : ""
      }){page_meta${pagemetaData} items{${Object.keys(
        reportReqData.responseFields
      ).toString()}}}`;

    console.log("\n", "query from get network report->", tempQuery, "\n");

    const response = await graphqlService.getGraphqlData({
      name: reportReqData.name,
      payload: tempQuery,
    });

    // console.log("response->", response);

    combinedData = combinedData.concat(response.items); // Add the items
    totalItems += response.page_meta.total_items; // Track total items count
  }

  // // Apply the limit to the final combined data if necessary
  combinedData = combinedData.slice(0, limit);

  const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

  combinedData.map(async (obj) => {
    obj[reportConfig.timestampParam] = await formatReportDate(
      obj[reportConfig.timestampParam],
      viewBy
    );

    await Promise.all(
      Object.keys(obj).map((key) => {
        if (regexPattern.test(obj[key])) {
          obj[key] = obj[key].split("+")[0];
          obj[key] = dayjs(obj[key]).format("YYYY-MMM-DD HH:mm:ss");
        }

        if (reportReqData.responseFields[key]) {
          obj[reportReqData.responseFields[key]] = obj[key];
          delete obj[key];
        }
      })
    );
  });

  let responseTobeSent = {
    totalCount: totalItems,
    pageNo: page,
    pageSize: limit,
    data: combinedData,
  };
  return responseTobeSent;
};

const generateDailyData = async (baseData, startDate, endDate) => {
  const start = moment(startDate, "YYYY-MM-DD HH:mm:ss");
  const end = moment(endDate, "YYYY-MM-DD HH:mm:ss");
  const dataArray = [];

  while (start.isBefore(end)) {
    const nextDay = start.clone().endOf("day"); // Ensure the next day ends at 23:59:59

    dataArray.push({
      ...baseData,
      data: {
        ...baseData.data,
        start_time: start.format("YYYY-MM-DD HH:mm:ss"),
        end_time: nextDay.isAfter(end)
          ? end.format("YYYY-MM-DD HH:mm:ss")
          : nextDay.format("YYYY-MM-DD HH:mm:ss"),
      },
    });

    // Move to the next day's start (00:00:00)
    start.add(1, "day").startOf("day");
  }

  // console.log("Generated Data Array:", dataArray);
  return dataArray;
};

const fetchAndCombineData = async (convertedData, whereClause) => {
  try {
    const responses = await Promise.all(
      convertedData.map(async (data) => {
        if (whereClause?.length > 0) {
          data.data.where = whereClause;
        }

        return axios(data)
          .then((response) => response.data)
          .catch((error) => {
            if (error.response && error.response.data) {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                error.response.data.detail
              );
            } else {
              // const auditLogPayload = {
              //   username: user.email,
              //   userId: user.id,
              //   roleName: user.role.name,
              //   event: auditLogEvents.DOWNLOAD_FAILED,
              //   action: `Failed downloading ${error.message}`,
              // };
              // auditLogService.addAuditLog(auditLogPayload);
              throw new ApiError(
                httpStatus.INTERNAL_SERVER_ERROR,
                "Something went wrong. Please try again after some time."
              );
            }
          });
      })
    );

    // Combine the result data
    const combinedResult = responses.join("\n");

    // Process the result (e.g., splitting into lines for further processing)
    const downloadableData = combinedResult.split("\n");

    return downloadableData;
  } catch (error) {
    console.error("Error during data fetching:", error);
    throw error;
  }
};

const processDownloadableData = (data) => {
  const headerRow = data.find((row) => row.trim() !== "");
  const filteredData = data.filter((row, index) => {
    // Keep the first occurrence of the header row and all non-header rows
    return row !== headerRow || index === data.indexOf(headerRow);
  });

  // Remove any empty rows
  const cleanedData = filteredData.filter((row) => row.trim() !== "");

  return cleanedData;
};

const downloadNetworkReports = async (payload) => {
  let date = new Date();
  let reportReqData = reportsMapping[payload.reportName];
  let searchClause = "";

  if (payload.search) {
    let fields = reportReqData.searchFields;

    for (let k = 0; k < fields.length; k++) {
      if (searchClause.length > 0) {
        searchClause = `${searchClause} or `;
      }
      searchClause = `${searchClause + fields[k]} ILIKE '%${payload.search}%' `;
    }

    if (searchClause.length > 0) {
      if (whereClause.length > 0) {
        whereClause = `(${whereClause}) and (${searchClause})`;
      } else {
        whereClause = searchClause;
      }
    }
  }

  // const startDate = payload.startDate
  //   ? payload.startDate
  //   : dayjs.utc().format("YYYY-MM-DD 00:00:00");
  // const endDate = payload.endDate
  //   ? payload.endDate
  //   : dayjs.utc().format("YYYY-MM-DD HH:mm:ss");

  const startDate = payload.startDate
    ? payload.startDate
    : dayjs.utc().subtract(3, "day").format("YYYY-MM-DD 00:00:00");

  const endDate = payload.endDate
    ? payload.endDate
    : dayjs.utc().subtract(1, "day").format("YYYY-MM-DD 23:59:59");

  // Parse dates with dayjs for validation
  const startDateObj = dayjs.utc(startDate);
  const endDateObj = dayjs.utc(endDate);

  // Ensure start and end dates are within 3 days
  const diffInDays = endDateObj.diff(startDateObj, "day");
  console.log("diffInDays", diffInDays);
  if (diffInDays > network_reports_no_of_days) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Start date and end date must be within ${network_reports_no_of_days} days, now it is ${diffInDays}.`
    );
  }

  let { viewBy } = reportReqData.defaultFilters;
  if (payload.defaultViewBy) {
    viewBy = payload.defaultViewBy;
  } else if (
    reportReqData.defaultFilters.useDefaultViewBy == undefined ||
    reportReqData.defaultFilters.useDefaultViewBy == false
  ) {
    viewBy = await getViewByValue(startDate, endDate);
  }

  let whereClause = "";
  if (searchClause?.length > 0) {
    whereClause = `(${searchClause})`;
  }

  const baseData = {
    method: "post",
    url: `${restapiUri}/api/v1/download_report`,
    data: {
      view_by: viewBy,
      report_name: reportReqData.normalizedName,
      order_by: "timestamp",
      fields: Object.keys(reportReqData.responseFields),
      time_zone: payload.timezone,
    },
  };

  // console.log("baseData->", baseData)

  // const convertedData = await generateDailyData(baseData, startDate, endDate);

  let result = null;
  try {
    const convertedData = await generateDailyData(baseData, startDate, endDate);
    if (whereClause?.length > 0) {
      data.data.where = whereClause;
    }

    const fetchedData = await fetchAndCombineData(convertedData, whereClause);

    // console.log("fetchedData->", fetchedData)

    downloadableData = processDownloadableData(fetchedData);

    // console.log("Final Downloadable Data:", downloadableData);
  } catch (error) {
    console.error("Error:", error.message);
  }

  // uTC date time convertion

  if (downloadableData.length < 2) {
    throw new ApiError(httpStatus.NOT_FOUND, "No record found"); // handle if there are no or invalid rows
  }

  // split the header row to get column names
  const headers = downloadableData[0].split("\t");

  // console.log("headers are : ", headers);
  // find index of columns with datetime values (assuming createdAt and updatedAt here)
  datetime = headers.indexOf("timestamp");

  rows = [];
  // iterate over each row (starting from the second row, as the first row is headers)
  for (let i = 1; i < downloadableData.length; i++) {
    //console.log("data is : " ,downloadableData[i])

    if (downloadableData[i] == null || downloadableData[i] == undefined) {
      downloadableData[i] = "-";
    } else {
      downloadableData[i] = downloadableData[i].replace(/\\N/g, "-");
    }
    let columns = downloadableData[i].split("\t");
    const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

    if (columns.length > 0) {
      for (let j = 0; j < columns.length; j++) {
        if (regexPattern.test(columns[j])) {
          columns[j] = columns[j].split("+")[0];
          columns[j] = dayjs(columns[j]).format("YYYY-MMM-DD HH:mm:ss");
        } else if (columns[j] && !isNaN(columns[j])) {
          // Check if cell value is numeric
          columns[j] = parseFloat(columns[j]);
        }
      }

      rows.push(columns);
    }
  }

  if (rows.length == 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
  }
  /*
    startDate = await convertToClientTime(startDate, user.timezone);
    endDate = await convertToClientTime(endDate, user.timezone);
    */
  let csv = rows.join("\n");
  let updatedCsv;

  // console.log("reportsMapping->", reportsMapping["Network Reports"]);

  if (payload.reportName === "Network Reports" && csv) {
    const responseFields = reportsMapping[payload.reportName].responseFields;

    // Parse the CSV into an array of objects
    const parsedCsv = csv.split("\n").map((line) => {
      const rowValues = line.split(",");

      // Validate row data
      if (
        rowValues.length < Object.keys(responseFields).length ||
        rowValues.includes("")
      ) {
        return null; // Skip invalid or empty rows
      }

      // Map row values to responseFields dynamically
      const mappedRow = {};
      Object.keys(responseFields).forEach((key, index) => {
        const headerName = responseFields[key];
        mappedRow[headerName] = isNaN(rowValues[index])
          ? rowValues[index]
          : parseInt(rowValues[index], 10); // Convert numeric fields to integers
      });

      return mappedRow;
    });

    // Filter out invalid rows
    const validCsvRows = parsedCsv.filter((row) => row !== null);

    // Add sequence numbers
    updatedCsv = addSequenceNumbers(validCsvRows);
  }

  // console.log("updatedCsv->", updatedCsv)

  // Convert JSON to CSV using json2csv
  const fields = Object.keys(updatedCsv[0]); // Extract headers dynamically
  const parser = new Parser({ fields });
  csv = parser.parse(updatedCsv);

  // Final CSV format
  // console.log("Final CSV:\n", csv);

  const flepath = await saveNetworkReports(
    csv,
    startDate,
    endDate,
    payload.timezone
  );

  // return true

  return Buffer.from(csv);
  // return {
  //   buf: Buffer.from(csv),
  //   startDate,
  //   endDate,
  //   extension: ".csv",
  // };
};

const saveNetworkReports = async (csv, startDate, endDate, timezone) => {
  const responseFields = reportsMapping["Network Reports"].responseFields;

  const datePosition = Object.values(responseFields).indexOf("Date") + 1;
  const filenamePosition =
    Object.values(responseFields).indexOf("CDR File Name") + 1;

  const formattedTimezone = formatTimezone(timezone);
  const {
    firstStartOfMonth,
    firstEndOfMonth,
    secondStartOfMonth,
    secondEndOfMonth,
  } = calculateMonthlyBoundaries(startDate, endDate);

  // csv = prepareCsvHeader(csv, secondStartOfMonth, secondEndOfMonth);

  const { headers, rows } = splitCsvContent(csv);

  const currentMonthHeader = prepareCsvHeader(
    headers,
    secondStartOfMonth,
    secondEndOfMonth
  );
  const prevousMonthHeader = prepareCsvHeader(
    headers,
    firstStartOfMonth,
    firstEndOfMonth
  );

  ensureDirectoriesExist();

  const { previousMonthRows, currentMonthRows } = splitRowsByMonth(
    rows,
    secondStartOfMonth,
    datePosition
  );

  // console.log("previousMonthRows->", previousMonthRows);

  if (previousMonthRows.length > 0) {
    handleMonthData(
      prevousMonthHeader,
      previousMonthRows,
      formattedTimezone,
      firstStartOfMonth,
      firstEndOfMonth,
      datePosition,
      filenamePosition
    );

    const previousMonthFilePath = finalizeCsvFile(
      formattedTimezone,
      firstStartOfMonth,
      firstEndOfMonth
    );
  }

  handleMonthData(
    currentMonthHeader,
    currentMonthRows,
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth,
    datePosition,
    filenamePosition
  );

  const currentMonthFilePath = finalizeCsvFile(
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth
  );

  return currentMonthFilePath;
};

// Helper functions
const calculateMonthlyBoundaries = (startDate, endDate) => ({
  firstStartOfMonth: dayjs(startDate).startOf("month").format("YYYY-MM-DD"),
  firstEndOfMonth: dayjs(startDate).endOf("month").format("YYYY-MM-DD"),
  secondStartOfMonth: dayjs(endDate).startOf("month").format("YYYY-MM-DD"),
  secondEndOfMonth: dayjs(endDate).endOf("month").format("YYYY-MM-DD"),
});

const formatTimezone = (timezone) => timezone.replace("/", "-").toLowerCase();

const prepareCsvHeader = (header, startOfMonth, endOfMonth) =>
  `Reports for ${startOfMonth} to ${endOfMonth}\n${header}`;

const splitCsvContent = (csv) => {
  const lines = csv.split("\n");
  return {
    headers: lines.slice(0, 1).join("\n"),
    rows: lines.slice(1),
  };
};

const ensureDirectoriesExist = () => {
  if (!fs.existsSync(secured_folder)) {
    fs.mkdirSync(secured_folder, { recursive: true });
  }
  if (!fs.existsSync(network_reports_directory)) {
    fs.mkdirSync(network_reports_directory, { recursive: true });
  }
};

const splitRowsByMonth = (rows, secondStartOfMonth, datePosition) => {
  const previousMonthRows = [];
  const currentMonthRows = [];

  rows.forEach((row) => {
    // Extract and clean the date part from the row
    const rowDate = row.split(",")[datePosition].trim().replace(/"/g, ""); // Remove quotes

    // Parse the rowDate and secondStartOfMonth using dayjs
    const parsedRowDate = dayjs(rowDate);
    const parsedSecondStartOfMonth = dayjs(secondStartOfMonth);

    // Check validity of both dates
    if (!parsedRowDate.isValid()) {
      console.error(`Invalid date parsing for rowDate: "${rowDate}"`);
      return;
    }

    if (!parsedSecondStartOfMonth.isValid()) {
      console.error(
        `Invalid date parsing for secondStartOfMonth: "${secondStartOfMonth}"`
      );
      return;
    }

    // Compare dates to split rows
    if (parsedRowDate.isBefore(parsedSecondStartOfMonth)) {
      previousMonthRows.push(row);
    } else {
      currentMonthRows.push(row);
    }
  });

  return { previousMonthRows, currentMonthRows };
};

const handleMonthData = (
  headers,
  monthRows,
  formattedTimezone,
  startOfMonth,
  endOfMonth,
  datePosition,
  filenamePosition
) => {
  const securedFilePath = generateFilePath(
    formattedTimezone,
    startOfMonth,
    endOfMonth
  );

  let existingData = [];
  if (fs.existsSync(securedFilePath)) {
    const existingCsv = fs.readFileSync(securedFilePath, "utf-8");
    const { existingHeaders, existingRows } = parseExistingCsv(existingCsv);

    // Handle new rows by removing duplicates and adding the new row
    existingData = monthRows.reduce((acc, newRow) => {
      return handleDuplicateRow(newRow, acc, filenamePosition);
    }, existingRows);

    // Update row indices (if needed)
    const updatedRows = updateRowIndices(existingData, datePosition);

    // Combine the headers and rows for final CSV
    const finalCsv = `${existingHeaders.join("\n")}\n${updatedRows.join("\n")}`;
    fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
  } else {
    const finalCsv = `${headers}\n${monthRows.join("\n")}`;
    fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
  }
};

const finalizeCsvFile = (
  formattedTimezone,
  secondStartOfMonth,
  secondEndOfMonth
) => {
  const securedFilePath = generateFilePath(
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth
  );
  const networkReportsFilePath = path.join(
    network_reports_directory,
    path.basename(securedFilePath)
  );

  fs.copyFileSync(securedFilePath, networkReportsFilePath);
  console.log(`CSV file saved and copied to: ${networkReportsFilePath}`);

  return networkReportsFilePath;
};

const generateFilePath = (formattedTimezone, start, end) =>
  path.join(
    secured_folder,
    `network_reports_${start}-${end}-${formattedTimezone}.csv`
  );

const parseExistingCsv = (csv) => {
  const lines = csv.split("\n");
  return {
    existingHeaders: lines.slice(0, 2),
    existingRows: lines.slice(2),
  };
};

const handleDuplicateRow = (newRow, existingRows, filenamePosition) => {
  const newCdrFileName = newRow.split(",")[filenamePosition]; // File name (assuming it's in the 3rd column, index 2)

  // Remove the existing row with the same file name
  const updatedRows = existingRows.filter(
    (existingRow) => existingRow.split(",")[filenamePosition] !== newCdrFileName
  );

  // Add the new row to the updated rows
  updatedRows.push(newRow);

  return updatedRows;
};

const updateRowIndices = (rows, datePosition) => {
  const sortedRows = rows.sort((rowA, rowB) => {
    const dateA = new Date(rowA.split(",")[datePosition].replace(/"/g, ""));
    const dateB = new Date(rowB.split(",")[datePosition].replace(/"/g, ""));
    return dateA - dateB; // Sort in ascending order
  });

  // Update the serial number (index 0) based on the sorted rows
  return sortedRows.map((row, index) => {
    const rowParts = row.split(",");
    if (rowParts.length > 0) {
      rowParts[0] = (index + 1).toString(); // Update serial number
    }
    return rowParts.join(",");
  });
};

const downloadStaticReports = async (
  user,
  payload,
  reportReqData,
  searchClause
) => {
  if (payload.totalCount > config.NO_OF_ROWS_PER_FILE) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Maximum response size reached `
    );
  }

  let date = new Date();

  console.log("donwload static report");

  if (payload.startDate) {
    startDate = payload.startDate;
  }
  // startDate = dayjs.tz(payload.startDate, user.tim).toISOString();
  else {
    startDate = dayjs.utc().format("YYYY-MM-DD 00:00:00");
  }

  if (payload.endDate) {
    endDate = payload.endDate;
  } else {
    endDate = dayjs.utc().format("YYYY-MM-DD HH:MM:ss");
  }

  let { viewBy } = reportReqData.defaultFilters;
  if (payload.defaultViewBy) {
    viewBy = payload.defaultViewBy;
  } else if (
    reportReqData.defaultFilters.useDefaultViewBy == undefined ||
    reportReqData.defaultFilters.useDefaultViewBy == false
  ) {
    viewBy = await getViewByValue(startDate, endDate);
  }

  let whereClause = "";
  if (searchClause?.length > 0) {
    whereClause = `(${searchClause})`;
  }

  console.time("graphql request");
  let data = {
    method: "post",
    url: `${reportConfig.restapiUri}/api/v1/download_report`,
    data: {
      view_by: viewBy,
      report_name: reportReqData.normalizedName,
      start_time: startDate.trim(),
      end_time: endDate.trim(),
      order_by: "timestamp",
      fields: Object.keys(reportReqData.responseFields),
      time_zone: user.timezone,
    },
  };

  // console.log("data is ", data);

  if (whereClause?.length > 0) {
    data.data.where = whereClause;
  }
  let result = null;
  console.log("sending axios req to download 1", data);
  await axios(data)
    .then((response) => {
      console.log("response received from graphql");
      let responseData = response.data;
      result = responseData;
    })
    .catch((error) => {
      logger.error("error is : " + error);
      if (error.response && error.response.data) {
        throw new ApiError(httpStatus.BAD_REQUEST, error.response.data.detail);
      } else {
        // let auditLogPayload = {
        //   username: user.email,
        //   userId: user.id,
        //   roleName: user.role.name,
        //   event: auditLogEvents.DOWNLOAD_FAILED,
        //   action: `Failed downloading ${error.message}`,
        // };
        // auditLogService.addAuditLog(auditLogPayload);
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Something went wrong.Pls try again after sometime"
        );
      }
    });
  // uTC date time convertion

  console.timeEnd("graphql request");

  let downloadableData = result.split("\n");

  // console.log("downloadable data is : ", downloadableData);
  if (downloadableData.length < 2) {
    throw new ApiError(httpStatus.NOT_FOUND, "No record found"); // handle if there are no or invalid rows
  }

  // split the header row to get column names
  const headers = downloadableData[0].split("\t");

  // console.log("headers are : ", headers);
  // find index of columns with datetime values (assuming createdAt and updatedAt here)
  datetime = headers.indexOf("timestamp");

  rows = [];
  // iterate over each row (starting from the second row, as the first row is headers)
  for (let i = 1; i < downloadableData.length; i++) {
    //console.log("data is : " ,downloadableData[i])

    if (downloadableData[i] == null || downloadableData[i] == undefined) {
      downloadableData[i] = "-";
    } else {
      downloadableData[i] = downloadableData[i].replace(/\\N/g, "-");
    }
    let columns = downloadableData[i].split("\t");
    const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

    if (columns.length > 0) {
      for (let j = 0; j < columns.length; j++) {
        if (regexPattern.test(columns[j])) {
          columns[j] = columns[j].split("+")[0];
          columns[j] = dayjs(columns[j]).format("YYYY-MMM-DD HH:mm:ss");
        } else if (columns[j] && !isNaN(columns[j])) {
          // Check if cell value is numeric
          columns[j] = parseFloat(columns[j]);
        }
      }

      rows.push(columns);
    }
  }

  // console.log("rows are : ", rows);

  if (rows.length == 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
  }
  /*
    startDate = await convertToClientTime(startDate, user.timezone);
    endDate = await convertToClientTime(endDate, user.timezone);
    */
  let heading = Object.values(reportReqData.responseFields);

  try {
    switch (payload.type) {
      case "EXCEL":
        return constructExcel(heading, rows, payload);
      case "PDF":
        return await constructPDF(heading, rows, payload);
      default:
        return await constructCSV(heading, rows, payload);
    }
  } catch (error) {
    console.error("Error in download cds:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Error processing download"
    );
  }
};

const getFilterFields = async (user, filters) => {
  try {
    let reportReqData = reportConfig.reportsMapping[filters.reportName];

    if (filters.reportName === "rawCdr") {
      reportReqData = reportConfig.rawCdr;
    }
    if (!reportReqData) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Report configuration not found for: ${filters.reportName}`
      );
    }

    let filterFields = Object.keys(reportReqData.filters || []);

    if (filters.reportName === "Dynamic Report") {
      logger.info(`User Type is: ${user.type}`);
      logger.info(`Filter flag is: ${filters.filterFlag}`);

      const isCustomer = (type) => type === rolesConfig.userTypes.CUSTOMER;
      const isSupplier = (type) => type === rolesConfig.userTypes.SUPPLIER;
      const isBoth = (type) => type === rolesConfig.userTypes.BOTH;

      // Pre-map role to field keys
      const roleFieldMap = {
        [rolesConfig.userTypes.CUSTOMER]: Object.keys(
          reportConfig.customerFields
        ),
        [rolesConfig.userTypes.SUPPLIER]: Object.keys(
          reportConfig.supplierFields
        ),
      };

      let flagReverseFields = [];

      if (!user.isSuperAdmin) {
        if (isCustomer(user.type)) {
          flagReverseFields = roleFieldMap[rolesConfig.userTypes.SUPPLIER];
        } else if (isSupplier(user.type)) {
          flagReverseFields = roleFieldMap[rolesConfig.userTypes.CUSTOMER];
        } else if (isBoth(user.type) && filters?.filterFlag) {
          const oppositeType = isCustomer(filters.filterFlag)
            ? rolesConfig.userTypes.SUPPLIER
            : isSupplier(filters.filterFlag)
            ? rolesConfig.userTypes.CUSTOMER
            : null;

          flagReverseFields = oppositeType ? roleFieldMap[oppositeType] : [];
        }
      }

      filterFields = filterFields.filter(
        (field) => !flagReverseFields.includes(field)
      );
    }

    const otherFilterFields = Object.keys(reportReqData.otherFilters || []);

    if (filterFields.length === 0) {
      logger.warn(`No search fields found for report: ${filters.reportName}`);
    } else {
      logger.info(
        `Found ${filterFields.length} search fields for report: ${filters.reportName}`
      );
    }

    if (otherFilterFields.length === 0) {
      logger.warn(
        `No other search fields found for report: ${filters.reportName}`
      );
    } else {
      logger.info(
        `Found ${otherFilterFields.length} other search fields for report: ${filters.reportName}`
      );
    }

    return { filters: filterFields, otherFilters: otherFilterFields };
  } catch (error) {
    logger.error("Error in getFilterFields:", error);

    // If it's already an ApiError, throw it directly
    if (error instanceof ApiError) {
      throw error;
    }

    // For unexpected errors
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to retrieve search fields"
    );
  }
};

const getReportTypes = async (user, filters) => {
  // console.log("filters->", filters);
  const userService = require("./user.service");
  const superAdmin = await userService.getUserByEmail(
    rolesConfig.adminUser.email
  );

  const panels = await panelService.getDynamicPanels(user);

  const dynamicReports = [];

  for (const panel of panels) {
    if (panel.dynamicReport) {
      const createdBy = panel.createdBy;
      const creater =
        superAdmin.id === createdBy ? superAdmin.name : "Other User";
      dynamicReports.push({
        id: panel.id,
        name: panel.name,
        visualizationType: panel.visualizationType,
        createdBy,
        creater,
        viewBy: ["minute", "hour", "day", "week", "month"],
      });
    }
  }

  const adminAssignedPanels = user?.role?.dynamicReports
    ? Object.keys(user.role.dynamicReports).filter(
        (key) => user.role.dynamicReports[key] != 0
      )
    : [];

  let panelData = [];
  if (adminAssignedPanels.length > 0) {
    panelData = await panelService.getPanelByNames(
      adminAssignedPanels,
      superAdmin
    );

    const panelResults = panelData
      .filter((panel) => panel.dynamicReport)
      .map((panel) => {
        const createdBy = panel.createdBy;
        const creater =
          superAdmin.id === createdBy ? superAdmin.name : "Other User";

        return {
          id: panel.id,
          name: panel.name,
          visualizationType: panel.visualizationType,
          createdBy,
          creater,
          viewBy: ["minute", "hour", "day", "week", "month"],
        };
      });

    dynamicReports.push(...panelResults);
  }

  const reports = structuredClone(reportConfig.reportClassification);

  if (filters.type === "REPORT_SUB_TYPE") {
    reports["Dynamic Reports"] = dynamicReports;

    if (filters.subtype) {
      if (reports[filters.subtype]) {
        return reports[filters.subtype];
      }
      throw new ApiError(httpStatus.BAD_REQUEST, "Report type not found");
    }

    return reports;
  }

  if (filters.type === "REPORT_TYPE" && !user.isSuperAdmin) {
    const reportMap = {};
    const staticReports = Object.keys(user.role.staticReports).filter(
      (key) => user.role.staticReports[key] !== 0
    );
    let dynamicReports = user.role.dynamicReports
      ? Object.keys(user.role.dynamicReports).filter(
          (key) => user.role.dynamicReports[key] !== 0
        )
      : [];

    const panels = await panelService.getDynamicPanels(user);
    const panelNames = panels.map((panel) => panel.name);

    // Merge dynamic reports with panel names
    dynamicReports = [...new Set([...dynamicReports, ...panelNames])];

    const reportsByCategory = { ...reportConfig.reportClassification };

    // Build reportMap
    Object.keys(reportsByCategory).forEach((category) => {
      reportMap[category] = reportsByCategory[category].map((r) => r.name);
    });

    // Add dynamic reports
    reportMap["Dynamic Reports"] = dynamicReports;

    // Prepare output categories that user has access to
    const userReportSet = new Set([...staticReports, ...dynamicReports]);

    const output = Object.keys(reportMap).filter((category) =>
      reportMap[category].some((reportName) => userReportSet.has(reportName))
    );

    return output;
  }

  return Object.keys(reportConfig.reportClassification);
};

const getCustomerList = async (user, internalCall = false) => {
  const reportReqData = reportConfig.customerList;

  // Build GraphQL query
  const tempQuery = `${reportReqData.name}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;

  let response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  // Rename keys based on responseFields mapping
  response = response.map((res) => {
    const updated = { ...res };
    Object.keys(res).forEach((key) => {
      if (
        key !== reportReqData.responseFields[key] &&
        reportReqData.responseFields[key]
      ) {
        updated[reportReqData.responseFields[key]] = res[key];
        delete updated[key];
      }
    });
    return updated;
  });

  // Return full list for Super Admin or allCustomerList
  if (user.isSuperAdmin || user.type == "Supplier" || internalCall) {
    return response;
  }

  // Else, filter by customer names present in user's list
  const allowedNames = new Set(user.customerList.map((c) => c.name));

  const filteredResponse = response.filter((cust) =>
    allowedNames.has(cust.name)
  );

  return filteredResponse;
};

const getSupplierList = async (user, internalCall = false) => {
  try {
    const reportReqData = reportConfig.supplierList;

    const tempQuery = `${reportReqData.name}{${Object.keys(
      reportReqData.responseFields
    ).toString()}}`;

    let response = await graphqlService.getGraphqlData({
      name: reportReqData.name,
      payload: tempQuery,
    });

    // Rename fields based on responseFields mapping
    response = response.map((res) => {
      const updated = { ...res };
      Object.keys(res).forEach((key) => {
        const mappedKey = reportReqData.responseFields[key];
        if (mappedKey && key !== mappedKey) {
          updated[mappedKey] = res[key];
          delete updated[key];
        }
      });
      return updated;
    });

    // If user is not superAdmin, filter by user's supplierList
    if (user.isSuperAdmin || user.type == "Customer" || internalCall) {
      return response;
    }

    const allowedNames = user.supplierList.map((s) => s.name);
    response = response.filter((item) => allowedNames.includes(item.name));
    return response;
  } catch (error) {
    logger.info(`Error in get supplierList ${error.message}`);
    throw new Error("Error while get suppliers");
  }
};

const getDestinationDetails = async (user, filter) => {
  const reportReqData = reportConfig.destnationDetails;

  let whereClause = "";
  if (filter?.search) {
    whereClause = `(where: "${filter.search}")`;
  }

  const tempQuery = `${reportReqData.name}${whereClause}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;

  const response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  // console.log("response->", response)

  const destination = [];

  response.forEach((obj) => {
    if (filter.type === "country") {
      destination.push(obj.country_name);
    } else if (filter.type === "operator") {
      destination.push(obj.operator_name);
    } else {
      // Field remapping (but you're not using it anywhere afterward)
      const remappedObj = {};
      Object.keys(obj).forEach((key) => {
        const newKey = reportReqData.responseFields[key] || key;
        remappedObj[newKey] = obj[key];
      });
      destination.push(remappedObj); // Optional: push remapped object
    }
  });

  return destination;
};

const getLcrDetails = async (user, filter) => {
  const reportReqData = reportConfig.LcDetails;
  const tempQuery = `${reportReqData.name}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;
  const response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  response.map((res) => {
    Object.keys(res).map((key) => {
      if (key != reportReqData.responseFields[key]) {
        if (reportReqData.responseFields[key]) {
          res[reportReqData.responseFields[key]] = res[key];
          delete res[key];
        }
      }
    });
  });

  return response;
};

const getcdrStatus = async (user, filter) => {
  const reportReqData = reportConfig.cdrStatusDetails;
  const tempQuery = `${reportReqData.name}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;
  const response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  response.map((res) => {
    Object.keys(res).map((key) => {
      if (key != reportReqData.responseFields[key]) {
        if (reportReqData.responseFields[key]) {
          res[reportReqData.responseFields[key]] = res[key];
          delete res[key];
        }
      }
    });
  });

  return response;
};

const getDestinationPrimeDetails = async (user, filter) => {
  const reportReqData = reportConfig.destinationPrimeDetails;
  const tempQuery = `${reportReqData.name}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;
  const response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  response.map((res) => {
    Object.keys(res).map((key) => {
      if (key != reportReqData.responseFields[key]) {
        if (reportReqData.responseFields[key]) {
          res[reportReqData.responseFields[key]] = res[key];
          delete res[key];
        }
      }
    });
  });

  return response;
};

const getSourcePrimeDetails = async (user, filter) => {
  const reportReqData = reportConfig.sourcePrimeDetails;
  const tempQuery = `${reportReqData.name}{${Object.keys(
    reportReqData.responseFields
  ).toString()}}`;
  const response = await graphqlService.getGraphqlData({
    name: reportReqData.name,
    payload: tempQuery,
  });

  response.map((res) => {
    Object.keys(res).map((key) => {
      if (key != reportReqData.responseFields[key]) {
        if (reportReqData.responseFields[key]) {
          res[reportReqData.responseFields[key]] = res[key];
          delete res[key];
        }
      }
    });
  });

  return response;
};

const getrawcdrs = async (user, payload, options, res) => {
  // console.log("user is ", user);

  let query;
  (limit = 10), (page = 1);
  if (options) {
    limit = options.limit || 10;
    page = options.page || 1;
  }

  let whereClause = "",
    { startDate } = payload,
    { endDate } = payload,
    { filters } = payload,
    filterClause = "",
    fields = [];

  filterClause = `(customer_bind!='0' and customer_bind!='-1' and supplier_bind!='0'  and supplier_bind != '-1')`;
  for (let j = 0; j < filters.length; j++) {
    if (reportConfig.columnMapping[filters[j].field]) {
      if (filters[j].field == "Transaction Type") {
        let transactionClause = "";
       for (let k = 0; k < filters[j].value.length; k++) {
          if (k > 0) transactionClause = `${transactionClause} or `;
          transactionClause = `${transactionClause} (${reportConfig.columnMapping[filters[j].field]
            } ='${filters[j].value[k]}') `;
        }
        if (filterClause && filterClause.length > 0)
          filterClause = `${filterClause} and (${transactionClause})`;
        else filterClause = `(${transactionClause})`;
      } else
        filterClause = `${filterClause} and (${
          reportConfig.columnMapping[filters[j].field]
        } ='${filters[j].value}') `;
    }
  }
  if (payload.timezone) user.timezone = payload.timezone;

  try {
    // Check if this is a download request
    if (payload.download && payload.download == 1) {
      const { totalCount } = payload || {};

      if (!totalCount) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Total count is required");
      }

      if (Number(totalCount) > cdr_downloads.initiate_offline_download_rows) {
        const response = await handleOfflineDownload(
          user,
          payload,
          filterClause,
          res
        );

        return response;
      }

      // Process the download
      let buf = await downloadRawCdrs(user, payload, filterClause, res);
      // console.log("buf is ", buf);

      return buf?.buf;
    }
  } catch (err) {
    // console.error("Unexpected error:", err);
    throw err;
    // No additional response should be sent here, as the response is already sent
  }

  let pagemetaData =
      "{ \
        current_page, \
        last_page, \
        page_size, \
        total_items \
      }",
    tempQuery;
 if (filterClause.length > 0) {
    tempQuery = `${reportConfig.rawCdr.name
      }(pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}", timeZone:"${user.timezone
      }",  where: "${filterClause}"){page_meta${pagemetaData} items{${reportConfig.rawCdr.responseFields.toString()}}}`;
  } else {
    tempQuery = `${reportConfig.rawCdr.name
      }(pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}", timeZone:"${user.timezone
      }"){page_meta${pagemetaData} items{${reportConfig.rawCdr.responseFields.toString()}}}`;
  }

  let response = await graphqlService.getGraphqlData({
    name: reportConfig.rawCdr.name,
    payload: tempQuery,
  });

  data = response.items;
  data.map((obj, i) => {
    Object.keys(obj).map(async (key) => {
      // using for convert date format

      const regexPattern =
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

      if (regexPattern.test(obj[key])) {
        obj[key] = obj[key].split("+")[0];
        obj[key] = dayjs(obj[key]).format("YYYY-MMM-DD HH:mm:ss");
      }

      let columnkey = Object.keys(reportConfig.columnMapping).find(
        (ckey) => reportConfig.columnMapping[ckey] === key
      );
      obj[columnkey] = obj[key];
      delete obj[key];
    });
  });

  if (!payload.download) {
    let responseTobeSent = {
      totalCount: response.page_meta.total_items,
      pageNo: response.page_meta.current_page,
      pageSize: response.page_meta.page_size,
      data,
    };
    return responseTobeSent;
  }
};

const saveRawCdrs = async (buf, cdrData) => {
  try {
    // console.log("buffer->", buf);
    // console.log("buffer filename->", buf.filename);
    // console.log("cdrDownloadId is ", cdrData.id);
    // console.log("cdr_downloads.download_directory is ", cdr_downloads.download_directory);

    // Ensure the directory exists
    if (!fs.existsSync(cdr_downloads.download_directory)) {
      fs.mkdirSync(cdr_downloads.download_directory, { recursive: true });
    }

    // Read the CSV data from the buffer
    const csvData = buf.buf.toString();
    const rows = csvData.split("\n");

    // Extract the first two rows as the header
    const header = rows.slice(0, 2).join("\n"); // First two rows
    const dataRows = rows.slice(2); // All rows except the first two

    // Split data rows into chunks of 10000 rows (or as defined)
    const rowsPerFile = cdr_downloads.initiate_offline_download_rows;
    const chunks = [];
    for (let i = 0; i < dataRows.length; i += rowsPerFile) {
      chunks.push(dataRows.slice(i, i + rowsPerFile));
    }

    console.log(`Total chunks created: ${chunks.length}`);

    // Define the zip file path
    const tarGzFilePath = path.join(
      cdr_downloads.download_directory,
      `${buf.filename}.zip`
    );

    // Create a writable stream for the zip file
    const output = fs.createWriteStream(tarGzFilePath);
    const gzip = zlib.createGzip();
    const tar = require("tar-stream").pack();

    // Attach event listeners for the compression process
    const tarGzPromise = new Promise((resolve, reject) => {
      output.on("close", () => {
        resolve();
      });
      output.on("error", (err) => {
        logger.error("Error writing to the output file:", err);
        reject(err);
      });
      gzip.on("error", (err) => {
        logger.error("Error during compression process:", err);
        reject(err);
      });
    });

    // Pipe TAR stream to Gzip and then to the output
    tar.pipe(gzip).pipe(output);

    // Add each chunk as a separate file in the TAR archive
    for (let i = 0; i < chunks.length; i++) {
      const isSingleChunk = chunks.length === 1;
      const chunkFileName = isSingleChunk
        ? `${buf.filename}.csv` // No "part1" if there's only one chunk
        : `${buf.filename}_part${i + 1}.csv`;

      // Add the header (first two rows) to the chunk data
      const chunkDataWithHeader = [header, ...chunks[i]].join("\n");

      // Add the chunk to the TAR archive
      tar.entry({ name: chunkFileName }, chunkDataWithHeader);
    }

    // Finalize the TAR archive
    tar.finalize();

    // Wait for the compression process to complete
    await tarGzPromise;

    // Update the CDR download status and file path
    const cdrPayload = {
      status: cdr_downloads.status.COMPLETED,
      // fileName: `${buf.filename}.tar.gz`,
      filePath: tarGzFilePath, // Save the TAR.GZ file path
    };
    await cdrDownloadService.updateCdrDownload(cdrData.id, cdrPayload);

    logger.info(`Raw CDRs saved as a single TAR.GZ: ${tarGzFilePath}`);
  } catch (error) {
    logger.error("Error saving raw CDRs as a single TAR.GZ:", error);
    throw error;
  }
};

const handleOfflineDownload = async (user, payload, filterClause, res) => {
  if (payload.type != "CSV") {
    return res.status(httpStatus.BAD_REQUEST).send({
      message: "Only CSV download is supported for raw cdrs",
    });
  }
  let date = new Date();
  if (payload.timezone) user.timezone = payload.timezone;

  if (payload.startDate) {
    startDate = payload.startDate;
  }

  if (payload.endDate) {
    endDate = payload.endDate;
  }

  const fileName = payload.fileName || `Report_${startDate}_${endDate}`;

  let cdrDataRes;

  logger.info(`Creating download entry for ${fileName}`);

  const filePath = path.resolve(
    cdr_downloads.download_directory,
    `${fileName}.csv`
  );

  // Create an entry for the download
  const crdFileData = {
    userId: user.id,
    initiatedBy: user.name,
    fileName,
    filePath: filePath,
    status: cdr_downloads.status.INITIATED,
  };

  cdrDataRes = await cdrDownloadService.createCdrDownload(crdFileData);
  // Send response immediately
  res.send({
    message: cdr_downloads.message,
  });
  logger.info("response sended", cdr_downloads.message);

  let whereClause = filterClause,
    data = {
      method: "post",
      url: `${reportConfig.restapiUri}/api/v1/fetch_raw_cdr`,
      data: {
        start_time: startDate.trim(),
        end_time: endDate.trim(),
        //  "order_by": "timestamp",
        fields: reportConfig.rawCdr.responseFields,
        time_zone: user.timezone,
      },
    };

  if (whereClause.length > 0) {
    data.data.where = whereClause;
  }
  logger.info(`Request to download CDR : ${JSON.stringify(data)}`);
  let result = null;
  try {
    const response = await axios(data);
    console.log("response received");
    let responseData = response.data;
    result = responseData;
  } catch (error) {
    // console.log(error.response);
    logger.error(`error logging: ${error.message}`);
    const cdrPayload = {
      status: cdr_downloads.status.FAILED,
    };
    await cdrDownloadService.updateCdrDownload(cdrDataRes.id, cdrPayload);
    return null;
  }

  let downloadableData = result.split("\n");
  if (downloadableData.length < 2) {
    // throw new ApiError(httpStatus.NOT_FOUND, "No record found"); // handle if there are no or invalid rows
    const cdrPayload = {
      status: cdr_downloads.status.FAILED,
    };
    await cdrDownloadService.updateCdrDownload(cdrDataRes.id, cdrPayload);
  }

  // split the header row to get column names
  let headingResponse = downloadableData[0].split("\t");
  let headers = [];
  for (let i = 0; i < headingResponse.length; i++) {
    let columnkey = Object.keys(reportConfig.columnMapping).find(
      (ckey) => reportConfig.columnMapping[ckey] === headingResponse[i]
    );
    headers.push(columnkey);
  }

  // find index of columns with datetime values (assuming createdAt and updatedAt here)
  let arrivaltime = headingResponse.indexOf("time_of_arrival");
  let deliverytime = headingResponse.indexOf("time_of_delivery");
  let eventdate = headingResponse.indexOf("event_date");
  rows = [];
  // iterate over each row (starting from the second row, as the first row is headers)
  for (let i = 1; i < downloadableData.length; i++) {
    downloadableData[i] = downloadableData[i].replace("\\N", "-");
    let columns = downloadableData[i].split("\t");

    if (columns.length > 0) {
      rows.push(columns);
    }
  }

  if (rows.length == 0) {
    // throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
    const cdrPayload = {
      status: cdr_downloads.status.FAILED,
    };
    await cdrDownloadService.updateCdrDownload(cdrDataRes.id, cdrPayload);
  }
  /*
  startDate = await convertToClientTime(startDate, user.timezone);
  endDate = await convertToClientTime(endDate, user.timezone);
  */

  // Create a new CSV buffer
  if (payload.type == "CSV") {
    try {
      let csv = rows.join("\n");
      csv = `Reports for ${startDate} to ${endDate}  \n${headers.toString()}\n${csv}`;
      const buf = {
        buf: Buffer.from(csv),
        startDate,
        endDate,
        extension: ".csv",
        filename: fileName,
      };
      console.log("csv created");
      // If more than 10k rows are downloaded, save the file
      // Save the downloaded file
      await saveRawCdrs(buf, cdrDataRes);
      return null;
    } catch (innerErr) {
      logger.error(`error logging: ${innerErr.message}`);
      const cdrPayload = {
        status: cdr_downloads.status.FAILED,
      };
      await cdrDownloadService.updateCdrDownload(cdrDataRes.id, cdrPayload);
      return null;
    }
  }
};

const downloadRawCdrs = async (user, payload, filterClause, res) => {
    let date = new Date();
    if (payload.timezone) user.timezone = payload.timezone;

    if (payload.startDate) {
      startDate = payload.startDate;
    }

    if (payload.endDate) {
      endDate = payload.endDate;
    }

    const fileName = payload.fileName || `Report_${startDate}_${endDate}`;

    const { totalCount } = payload || {};

    if (!totalCount) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Total count is required");
    }

    let cdrDataRes;

    let whereClause = filterClause,
      data = {
        method: "post",
        url: `${reportConfig.restapiUri}/api/v1/fetch_raw_cdr`,
        data: {
          start_time: startDate.trim(),
          end_time: endDate.trim(),
          //  "order_by": "timestamp",
          fields: reportConfig.rawCdr.responseFields,
          time_zone: user.timezone,
        },
      };

    if (whereClause.length > 0) {
      data.data.where = whereClause;
    }
    logger.info(`Request to download CDR : ${JSON.stringify(data)}`);
    let result = null;
    await axios(data)
      .then((response) => {
        console.log("recieved resp");
        let responseData = response.data;
        result = responseData;
        // console.log("raw cdr data->", result);
      })
      .catch((error) => {
        // console.log(error.response);
        logger.error(`error logging: ${error.message}`);
        if (error.response && error.response.data) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            JSON.stringify(error.response.data.detail)
          );
        } else {
          // let auditLogPayload = {
          //   username: user.email,
          //   userId: user.id,
          //   roleName: user.role.name,
          //   event: auditLogEvents.DOWNLOAD_FAILED,
          //   action: `Failed downloading raw cdr${error.message}`,
          // };
          // auditLogService.addAuditLog(auditLogPayload);
          throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            "Something went wrong.Pls try again after sometime"
          );
        }
      });

    let downloadableData = result.split("\n");
    if (downloadableData.length < 2) {
      throw new ApiError(httpStatus.NOT_FOUND, "No record found"); // handle if there are no or invalid rows
    }

    // split the header row to get column names
    let headingResponse = downloadableData[0].split("\t");
    let headers = [];
    for (let i = 0; i < headingResponse.length; i++) {
      let columnkey = Object.keys(reportConfig.columnMapping).find(
        (ckey) => reportConfig.columnMapping[ckey] === headingResponse[i]
      );
      headers.push(columnkey);
    }

    // find index of columns with datetime values (assuming createdAt and updatedAt here)
    let arrivaltime = headingResponse.indexOf("time_of_arrival");
    let deliverytime = headingResponse.indexOf("time_of_delivery");
    let eventdate = headingResponse.indexOf("event_date");
    rows = [];
    // iterate over each row (starting from the second row, as the first row is headers)
    for (let i = 1; i < downloadableData.length; i++) {
      downloadableData[i] = downloadableData[i].replace("\\N", "-");
      let columns = downloadableData[i].split("\t");

      if (columns.length > 0) {
        rows.push(columns);
      }
    }

    if (rows.length == 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
    }
    /*
startDate = await convertToClientTime(startDate, user.timezone);
endDate = await convertToClientTime(endDate, user.timezone);
*/

    try {
      switch (payload.type) {
        case "EXCEL":
          return constructExcel(headers, rows, payload);
        case "PDF":
          return await constructPDF(headers, rows, payload);
        default:
          return await constructCSV(headers, rows, payload);
      }
    } catch (error) {
      console.error("Error in download cds:", error.message);
      throw new ApiError(
        error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
        error.message || "Error processing download"
      );
    }

    // Create a new PDF buffer
    if (payload.type == "PDF") {
      const pdfBuffer = new Promise((resolve) => {
        let doc = new PDFDocument({
          bufferPages: true,
          margin: 30,
          size: "A4",
        });

        // rows = rows.slice(0, 5000);
        const table = {
          title: payload.reportName,
          subtitle: `Reports for ${startDate} to ${endDate}`,
          headers,
          rows,
        };

        let buffers = [];
        doc.on("data", buffers.push.bind(buffers));
        doc.on("end", () => {
          let pdfData = Buffer.concat(buffers);
          console.log("pdf created");
          resolve(pdfData);
        });

        doc.table(table);
        // done!
        doc.end();
      });
      return {
        buf: pdfBuffer,
        startDate,
        endDate,
        extension: ".pdf",
      };
    }

    // Create a new CSV buffer
    else if (payload.type == "CSV") {
      let csv = rows.join("\n");
      csv = `Reports for ${startDate} to ${endDate}  \n${headers.toString()}\n${csv}`;
      const buf = {
        buf: Buffer.from(csv),
        startDate,
        endDate,
        extension: ".csv",
        filename: fileName + ".csv",
      };
      console.log("csv created");

      return buf;
    }

    // Create a new XLSX buffer
    wb = XLSX.utils.book_new();

    /* create empty sheet
   const ws = XLSX.utils.aoa_to_sheet([["Reports for "+startDate + " to "+endDate]]) */
    const ws = XLSX.utils.aoa_to_sheet([
      [`Reports for ${startDate} to ${endDate} `],
    ]);
    // add row to sheet

    XLSX.utils.sheet_add_aoa(ws, [headers], { origin: -1 });
    try {
      //if(rows.length < 1000000)
      // rows = rows.slice(0, 30000);
      XLSX.utils.sheet_add_aoa(ws, rows, { origin: -1 });
    } catch (err) {
      logger.error("Failed downloading ", err);
      let auditLogPayload = {
        username: email,
        userId: user.id,
        roleName: user.role.name,
        event: auditLogEvents.DOWNLOAD_FAILED,
        action: `Failed downloading ${err}`,
      };
      auditLogService.addAuditLog(auditLogPayload);
    }

    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    buf = XLSX.write(wb, {
      type: "buffer",
      bookType: "xlsx",
    });

    return {
      buf,
      startDate,
      endDate,
      extension: ".xlsx",
    };
  },
  getCdrDownloadData = async (payload) => {
    try {
      let response = await cdr_download.findAll();
      return response;
    } catch (err) {
      logger.error("Failed downloading ", err);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, err);
    }
  };
const addSequenceNumbers = (csvData) => {
  // console.log("csvData->", csvData);
  const calculatedFields =
    reportConfig.reportsMapping["Network Reports"].calculatedFields;

  let sNo = 1;
  let firstSeqNum = 1; // Start with first sequence number as 1

  return csvData.map((row, index) => {
    // Extract the date from the row for comparison
    const rowDate = row.Date.split(" ")[0]; // Just the date part (e.g., '2025-01-05')
    const prevRowDate =
      index > 0 ? csvData[index - 1].Date.split(" ")[0] : null;

    let lastSeqNum;

    // If it's the first row or the first row of the day, reset sequence numbers
    if (index === 0 || rowDate !== prevRowDate) {
      firstSeqNum = 1;
      lastSeqNum = row["Total SMS"];
    } else {
      lastSeqNum = firstSeqNum + row["Total SMS"];
    }

    // Creating new row with sequence numbers
    const newRow = {
      [calculatedFields.s_no]: sNo,

      ...row,
      [calculatedFields.first_seq_no]: firstSeqNum,
      [calculatedFields.last_seq_no]: lastSeqNum,
    };

    // Update first sequence number for next row
    firstSeqNum = lastSeqNum;
    sNo += 1;

    // console.log("newRow->", newRow)

    return newRow;
  });
};

const createSelectedColumns = async (user, payload) => {
  try {
    const {
      reportType,
      reportName,
      reportId,
      selectAll = false,
      selectedColumns = [],
    } = payload || {};

    // Validate reportId for dynamic reports
    if (reportType === "dynamic" && !reportId) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Report ID is required for dynamic reports"
      );
    }

    // Choose correct report config
    const selectedReportName =
      reportType === "dynamic" ? "Dynamic Report" : reportName;

    if (
      config.REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS.includes(
        selectedReportName
      )
    ) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Report ${selectedReportName} does not support expandable columns`
      );
    }
    const reportConfigData = reportConfig.reportsMapping[selectedReportName];

    if (!reportConfigData) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Invalid report name for ${reportType} report`
      );
    }

    // If response fields have Date then dont allow to unselect
    if (
      !selectedColumns.includes("Date") &&
      !selectedColumns.includes("Datetime") &&
      Object.keys(reportConfigData?.responseFields || {}).includes("timestamp")
    ) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Date column cannot be unselect"
      );
    }

    const responseFieldLabels = Object.values(reportConfigData.responseFields || {});

    // 🔄 Reorder selectedColumns to match the order of responseFieldLabels
    const orderedSelectedColumns = responseFieldLabels.filter(label =>
      selectedColumns.includes(label)
    );

    console.log("selectedColumns->", selectedColumns);
    console.log("orderedSelectedColumns->", orderedSelectedColumns);

    // Validate at least one derived field is selected
    const reportDerivedFields =
      reportConfigData.aggregationFieldMapping.derivedFieldName || {};
    const derivedLabels = Object.values(reportDerivedFields);

    const hasAtLeastOneDerived = derivedLabels.some((label) =>
      selectedColumns.includes(label)
    );

    const reportFieldNames =
      reportConfigData.aggregationFieldMapping.fieldName || {};
    const fieldLabels = Object.values(reportFieldNames);

    const hasAtLeastOneField = fieldLabels.some((label) =>
      selectedColumns.includes(label)
    );

    if (!hasAtLeastOneDerived && !hasAtLeastOneField) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "At least one non-aggregate and one aggregate field should be selected."
      );
    }

    // Build the payload
    const userPreferencePayload = {
      userId: user.id,
      reportType,
      reportName,
      reportId: reportId || null,
      selectedColumns: orderedSelectedColumns,
      selectAll,
    };

    const [userPreference] = await UserReportPreferences.upsert(
      userPreferencePayload
    );

    return userPreference;
  } catch (error) {
    logger.error("Error creating user preference:", error);
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Failed to create user preference"
    );
  }
};

const getSelectedColumns = async (userId, filters) => {
  try {
    const { reportType, reportName, reportId } = filters;

    if (reportType === "dynamic" && !reportId) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Report ID is required for dynamic reports"
      );
    }

    let dataColumns = {};
    let reportConfigData = null;

    if (reportType === "static") {
      // For static reports, fetch the columns from the report configuration
      reportConfigData = reportConfig.reportsMapping[reportName];
      if (!reportConfigData) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Invalid report name for static report"
        );
      }

      const { tableFields, derivedFields } =
        separateTableAndDerivedFields(reportConfigData);

      dataColumns = {
        tableFields,
        derivedFields,
      };
    } else if (reportType === "dynamic") {
      // For dynamic reports, fetch the columns from the panel service
      const panel = await panelService.getPanel(reportId);
      if (!panel) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          "Panel not found for dynamic report"
        );
      }

      reportConfigData = reportConfig.reportsMapping["Dynamic Report"];
      dataColumns = panel.dataColumns || {};

      const derivedFieldLabels = Object.values(
        reportConfigData.aggregationFieldMapping.derivedFieldName
      );

      // Filter matching fields
      const aggDeriveFields =
        dataColumns.derivedFields?.filter((field) =>
          derivedFieldLabels.includes(field)
        ) || [];

      // Add matched fields to tableFields (avoid duplicates)
      dataColumns.tableFields = Array.from(
        new Set([
          ...(dataColumns.tableFields || []),
          // ...aggDeriveFields  // Uncomment if you want to move matched derived fields to tableFields
        ])
      );

      // Ensure "Date" is the first column
      if (
        !dataColumns.tableFields.includes("Date") &&
        !dataColumns.tableFields.includes("Datetime")
      ) {
        dataColumns.tableFields.unshift("Date");
      }
    }

    const userPreferences = await UserReportPreferences.findOne({
      where: {
        userId,
        reportType,
        reportName,
      },
    });

    if (!userPreferences) {
      // Return default columns and config flags if no user preferences exist
      return {
        dataColumns,
        isCustomReport: reportConfigData?.isCustomReport || false,
        noStartEndDate: reportConfigData?.noStartEndDate || false,
        noViewBy: reportConfigData?.noViewBy || false,
      };
    }

    const userPrefJSON = userPreferences.toJSON();
    userPrefJSON.dataColumns = dataColumns;

    // Add config flags to response
    if (reportConfigData) {
      userPrefJSON.isCustomReport = reportConfigData.isCustomReport || false;
      userPrefJSON.noStartEndDate = reportConfigData.noStartEndDate || false;
      userPrefJSON.noViewBy = reportConfigData.noViewBy || false;
    }

    return userPrefJSON;
  } catch (error) {
    logger.error("Error fetching user preferences:", error);
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Failed to retrieve user preferences"
    );
  }
};

module.exports = {
  getStaticReports,
  getFilterFields,
  getReportTypes,
  getCustomerList,
  getSupplierList,
  getDestinationDetails,
  getLcrDetails,
  getcdrStatus,
  getDestinationPrimeDetails,
  getSourcePrimeDetails,
  getrawcdrs,
  getCdrDownloadData,
  createSelectedColumns,
  getSelectedColumns,
};
