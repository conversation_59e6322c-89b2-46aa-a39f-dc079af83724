const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const graphqlService = require("./graphql.service");
const emailService = require("./email.service");
const groupService = require("./alerts.service");

const config = require("../config/config");
const globalConfig = require("../config/globalConfig");
const logger = require("../config/logger");
const { auditLogEvents } = require("../config/roles");
const auditLogService = require("./audit-log.service");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");

dayjs.extend(utc);
dayjs.extend(timezone);

const constructCSV = require("../utils/generateCsv");
const constructPDF = require("../utils/generatePdf");
const constructExcel = require("../utils/generateExcelFile");
const offlineDownloadService = require("./offlineDownload.service");
const path = require("path");
const { saveCsvOffline, createTarGzBuffer, createZipBuffer } = require("../utils/saveCsvOffline");
const constructGrapghqlQuery = require("../utils/constructGrapghqlQuery.js");
const { determineViewBy, getWhereClause } = require("../utils/misc.js");
const { get } = require("http");
const { handleOtherFilters } = require("../utils/build-advanced-filter.js");
const { maskDownloadedRows, maskNumber } = require("../utils/maskNumber.js");
const { PaddingDownloadedRows, padding } = require("../utils/padding.js");
const streamCsvToZipBuffer = require("../utils/streamCsvToZipBuffer.js");
const streamPdfToZipBuffer = require("../utils/streamPdfToZipBuffer.js");
const streamExcelToZipBuffer = require("../utils/streamExcelToZipBuffer.js");

// Helper: Parse and format downloaded data
function parseDownloadedData(rawData) {
  console.log("rowdata before")
  const lines = rawData.split("\n");
  console.log("rowdata after")

  if (lines.length < 2) {
    throw new Error("No data available in downloadable response.");
  }

  const headers = lines[0].split("\t");
  const rows = [];

  const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

  for (let i = 1; i < lines.length; i++) {
    if (!lines[i]) {
      lines[i] = "-";
    } else {
      lines[i] = lines[i].replace(/\\N/g, "-");
    }
    let columns = lines[i].split("\t");
    if (columns.length > 0) {
      for (let j = 0; j < columns.length; j++) {
        if (regexPattern.test(columns[j])) {
          columns[j] = dayjs(columns[j]).format("YYYY-MMM-DD HH:mm:ss");
        } else if (columns[j] && !isNaN(columns[j])) {
          columns[j] = parseFloat(columns[j]);
        }
      }
      rows.push(columns);
    }
  }

  if (rows.length === 0) {
    throw new Error("Parsed rows are empty.");
  }

  return { headers, rows };
}


const handleCusRepOfflineDownload = async ({ payload, customRepQuery, user, apiUrl, totalCount, aggregateResponse, res }) => {
  let offlineDataRes;

  try {
    if (payload.type != "CSV") {
      return res.status(httpStatus.BAD_REQUEST).send({
        message: "Only CSV download is supported for offline download",
      });
    }

    const fileName =
      payload.fileName || `${payload.reportName}_${payload.startDate}_${payload.endDate}`;

    const filePath = path.resolve(
      config.offline_downloads.download_directory,
      `${fileName}.csv`
    );

    // Create an entry for the download
    const offlineFileData = {
      userId: user.id,
      initiatedBy: user.name,
      fileName: fileName,
      reportName: payload.reportName,
      category: config.offline_downloads.category.static,
      filePath: filePath,
      status: config.offline_downloads.status.INITIATED,
    };

    // Create offline download entry
    offlineDataRes = await offlineDownloadService.createOfflineDownload(
      offlineFileData
    );

    // Respond to user immediately to indicate offline download initiation
    res.send({
      isOfflineDownload: true,
      message: config.offline_downloads.message,
    });

    const graphanaPayloads = [];

    const pageSize = config.NO_OF_DATA_PER_QUERY; // Define page size
    const totalPages = Math.ceil(totalCount / pageSize); // Calculate total pages

    // If the total count exceeds the maximum rows for fetching reports, split the query into multiple pages
    if (totalCount > pageSize) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        customRepQuery = {
          ...customRepQuery,
          page: pageNumber,
          limit: pageSize,
        };
        graphanaPayloads.push(customRepQuery);
      }
    } else {
      // If the total count is within the limit, add the query to the payloads
      customRepQuery = {
        ...customRepQuery,
        page: 1,
        limit: Number(totalCount),
      };
      graphanaPayloads.push(customRepQuery);
    }

    // Loop through the payloads and fetch data from FastAPI
    for (let i = 0; i < graphanaPayloads.length; i++) {
      const graphanaPayload = graphanaPayloads[i];
      const isFinal = i === graphanaPayloads.length - 1;

      // Fetch data from FastAPI
      let responseData = await graphqlService.getGraphqlCustomData({
        apiUrl,
        payload: graphanaPayload,
      });

      let { headers, rows } = parseDownloadedData(responseData.data)

      rows = PaddingDownloadedRows(headers, rows)

      // Mask row values
      if (!user.isSuperAdmin && user.reportMasking) {
        rows = maskDownloadedRows(headers, rows);
      }

      // Construct the CSV data
      const csvData = await constructCSV(
        headers,
        rows,
        payload,
        aggregateResponse
      );
      // Save the CSV file offline as muli-parted zip file
      await saveCsvOffline(csvData, offlineDataRes, isFinal);
    }

    return;
  } catch (error) {
    logger.error(`error logging: ${error.message}`);
    // let auditLogPayload = {
    //   username: user.email,
    //   userId: user.id,
    //   roleName: user.role.name,
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading ${error.message}`,
    // };
    // // Add audit log
    // auditLogService.addAuditLog(auditLogPayload);
    // logger.error(`error logging: ${error.message}`);

    // Update the offline download status to failed
    const offlinePayload = {
      status: config.offline_downloads.status.FAILED,
    };
    await offlineDownloadService.updateOfflineDownload(
      offlineDataRes.id,
      offlinePayload
    );
    return null;
  }
};

const handleCustomRepDownload = async ({
  user,
  payload,
  customRepQuery,
  apiUrl,
  totalCount,
  aggregateResponse,
}) => {
  try {
    const response = await graphqlService.getGraphqlCustomData({
      apiUrl,
      payload: { ...customRepQuery, page: 1, limit: Number(totalCount) },
    });

    let { headers, rows } = parseDownloadedData(response.data)

    rows = PaddingDownloadedRows(headers, rows)

    // Mask row values
    if (!user.isSuperAdmin && user.reportMasking) {
      rows = maskDownloadedRows(headers, rows);
    }

    switch (payload.type) {
      case "EXCEL":
        // return await constructExcel(headers, rows, payload, aggregateResponse);
        return streamExcelToZipBuffer(headers, rows, payload, aggregateResponse);
      case "PDF":
        // return await constructPDF(headers, rows, payload, aggregateResponse);
        return await streamPdfToZipBuffer(headers, rows, payload, aggregateResponse);
      default:
        // return await constructCSV(headers, rows, payload, aggregateResponse);
        return await streamCsvToZipBuffer(headers, rows, payload, aggregateResponse);
    }
  } catch (error) {
    console.error("Error in handleCustomRepDownload:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Error processing download"
    );
  }
};

const processCustomRepDownload = async ({
  user,
  payload,
  customRepQuery,
  apiUrl,
  totalCount,
  aggregateResponse,
  res,
}) => {
  try {
    // Offline download flow
    // Check if the total count exceeds the limit for offline downloads
    // If payload.sendFile is true, it means the user is requesting a file download (used for Billing report FTP download)
    if (Number(totalCount) > config.offline_downloads.initiate_offline_download_rows) {
      const response = await handleCusRepOfflineDownload({
        payload,
        customRepQuery,
        user,
        apiUrl,
        totalCount,
        aggregateResponse,
        res,
      });

      return response;
    }
    // Download file flow
    const file = await handleCustomRepDownload({
      user,
      payload,
      customRepQuery,
      apiUrl,
      totalCount,
      aggregateResponse,
    });

    res.setHeader("Content-Type", "application/zip");
    res.setHeader("Content-Disposition", `attachment; filename=${file.filename}`);
    res.send(file.buf);

    // Create a zip buffer from the file
    // const tarGzBuffer = await createTarGzBuffer(file.buf, file.filename);

    // const compressedFile = await createZipBuffer([file]);

    // // Send the zip buffer as a response
    // console.log("compressed buffer received")
    // res.setHeader("Content-Type", "application/zip");
    // res.setHeader("Content-Disposition", `attachment; filename=${file.filename}.zip`);
    // res.send(compressedFile);

    return;
  } catch (error) {
    console.error("Error in processCustomRepDownload:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

const processCustomRepSendEmail = async ({
  user,
  payload,
  customRepQuery,
  apiUrl,
  totalCount,
  aggregateResponse,
}) => {
  try {
    if (totalCount > config.NO_OF_ROWS_PER_FILE) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Maximum response size reached `
      );
    }
    const file = await handleCustomRepDownload({
      user,
      payload,
      customRepQuery,
      apiUrl,
      totalCount,
      aggregateResponse,
    });

    // const tarGzBuffer = await createTarGzBuffer(file.buf, file.filename);
    // const compressedFile = await createZipBuffer([file]);


    const fileSizeInMB = (
      Buffer.byteLength(file.buf) /
      (1024 * 1024)
    ).toFixed(2);

    console.log("fileSizeInMB-->", fileSizeInMB)

    if (fileSizeInMB > globalConfig.MAX_EMAIL_ATTACHMENT_SIZE) {
      throw new ApiError(413, "File size too large");
    }
    let groupMembers = [];

    if (payload.groupList) {
      groupMembers = await groupService.getGroupMembers(payload.groupList);
    }

    let emailList = [...new Set([...payload.mailList, ...groupMembers])];

    if (!emailList || emailList.length === 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Minimum 1 email ID required");
    }

    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: emailList.toString(),
      subject: payload.reportName,
      template: "email_report.template",
      context: {
        REPORT_NAME: payload.reportName,
        STARTDATE: payload.startDate,
        ENDDATE: payload.endDate,
      },
      attachments: {
        filename: file.filename,
        content: file.buf,
        encoding: "base64",
      },
    };

    return emailService.MailService(emailObj);
  } catch (error) {
    console.error("Error in processCustomRepSendEmail:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

const getOtherFilterQuery = async (payload, reportReqData, user) => {
  const payloadFilters = payload.filters || {};
  const { otherFilters = {} } = reportReqData || {};
  const otherFilterFields = Object.keys(otherFilters);

  const cleanedOtherPayload = {};
  const andGroups = [];

  // Separate other filters from the payload
  for (const [key, value] of Object.entries(payloadFilters)) {
    if (
      (Array.isArray(value) && value.length === 0) ||
      value === null || value === undefined || value === ''
    ) {
      continue; // skip empty
    }

    // Add to cleanedOtherPayload if it's an other filter
    if (otherFilterFields.includes(key)) {
      cleanedOtherPayload[key] = value;
      // Remove from payload.filters
      delete payload.filters[key];
    }

  }

  // If there are no other filters, return empty string
  if (Object.keys(cleanedOtherPayload).length === 0) {
    return '';
  }
  // Construct Raw query for Other Filters
  andGroups.push(...await handleOtherFilters(cleanedOtherPayload, reportReqData, payload, user));

  return andGroups.length > 0 ? andGroups.join(" AND ") : '';

}

const customReports = async (payload, user, reportReqData, options, res) => {
  try {

    if(!user.timezone) user.timezone = config.DEFAULT_TIMEZONE
  
    if (payload.timezone) user.timezone = payload.timezone;
  
    payload.startDate = payload.startDate ? payload.startDate.trim() : dayjs().tz(user.timezone).startOf("day").format("YYYY-MM-DD HH:mm:ss");
    payload.endDate = payload.endDate ? payload.endDate.trim() : dayjs().tz(user.timezone).endOf("day").format("YYYY-MM-DD HH:mm:ss");
    
    // Find the FastApi URL from the config
    const apiUrl = `${config.dataServiceUrl}${reportReqData?.apiEndpoint}`;

    // Build where clause and advanced filters
    const { whereClause, viewBy } =
      await getWhereClause({
        user,
        payload,
        reportReqData,
        options,
      });

    // Build the other filter query because it is not supported in the where clause
    // This also removes the other filters from the payload.filters
    const otherFilterQuery = await getOtherFilterQuery(payload, reportReqData, user)

    // Construct the Fast Api query for custom reports
    const customRepQuery = await constructGrapghqlQuery.getCustomRepQuery({
      payload,
      otherFilterQuery,
      viewBy,
      user,
      reportReqData,
    });

    const { aggregationFieldMapping } = reportReqData || {};
    let aggregateResponse = []

    if (aggregationFieldMapping) {
      // Construct the aggregation query
      const aggregateQuery = constructGrapghqlQuery.getAggregationQuery({
        payload,
        whereClause,
        viewBy,
        user,
        reportReqData,
      });

      // Fetch the aggregation data from the FastApi
      aggregateResponse = await graphqlService.getAggregationFields(
        aggregateQuery,
        reportReqData
      );
    }

    // Handle Download and Send Email
    if (Number(payload.download) || Number(payload.sendEmail)) {

      // To find total count of the Report
      const response = await graphqlService.getGraphqlCustomData({
        apiUrl,
        payload: { ...customRepQuery, page: 1, limit: 1, download: "0" },
      });

      const totalCount = response.data.page_meta.total_items || 0;

      logger.info(`totalCount: ${totalCount}`)

      // if (!totalCount) {
      //   throw new ApiError(httpStatus.NOT_FOUND, "No data found");
      // }

      // Handle online download and offline download
      if (Number(payload.download)) {
        return await processCustomRepDownload({
          user,
          payload,
          customRepQuery,
          apiUrl,
          totalCount,
          aggregateResponse,
          res,
        });
      }

      // Handle Send Email
      if (Number(payload.sendEmail)) {
        return await processCustomRepSendEmail({
          user,
          payload,
          customRepQuery,
          apiUrl,
          totalCount,
          aggregateResponse,
        });
      }
    }

    // Handle View
    const response = await graphqlService.getGraphqlCustomData({
      apiUrl,
      payload: customRepQuery,
    });

    let { items, page_meta, ...rest } = response.data;

    items = padding(items)

    //Masking the number
    if (!user.isSuperAdmin && user.reportMasking && items) {
      items = maskNumber(items);
    }
    return {
      ...rest,
      aggregateResponse,
      data: items,
      pageNo: page_meta.current_page,
      pageSize: page_meta.page_size,
      totalCount: page_meta.total_items,
    };
  } catch (error) {
    console.error("Error in custom reports service:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};


module.exports = {
  customReports,
}