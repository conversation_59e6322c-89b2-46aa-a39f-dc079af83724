const httpStatus = require("http-status");
const { Op, ValidationError } = require("sequelize");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const { convertToClientTime } = require('../utils/misc')
const {
  rolePermissions,
  resources,
  defaultUserRole,
  preCreatedUserRoles,
} = require("../config/roles");
const dashboardService = require("./dashboard.service");
const { reportClassification } = require("../config/reportConfig");
const panelService = require("./panel.service");
const { v4: uuidv4 } = require("uuid");
const { User, Role } = models;


function encodePermissions(items) {
  const encodedPermissions = {};

  for (const item of items) {
    let permission = 0;

    for (const permissionKey of Object.keys(item.permissions)) {
      if (item.permissions[permissionKey]) {
        permission |= rolePermissions[permissionKey] || 0;
      }
    }

    // Ensure 'view' is included if other permissions are present
    if (
      permission > (rolePermissions.view || 0) &&
      (!item.permissions.view || item.permissions.view === 0)
    ) {
      permission |= rolePermissions.view;
    }

    encodedPermissions[item.name] = permission;
  }

  return encodedPermissions;
}


function decodeResourcePermissions(resourcesData) {
  const raw_resources = [];
  const tmpResources = [];
  const resourceConf = Object.values(resources)

  for (let key in resourcesData) {
    const name = key;
    const permissions = {
      create: 0,
      view: 0,
      update: 0,
      delete: 0,
      download: 0,
    };

    Object.keys(rolePermissions).forEach((permission) => {
      const permissionFlag = resourcesData[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });

    tmpResources.push(name);
    raw_resources.push({ name, permissions });
  }

  for (let k = 0; k < resourceConf.length; k++) {
    if (
      !tmpResources.includes(resourceConf[k]) &&
      resourceConf[k] !== resources.USER_MANAGEMENT &&
      resourceConf[k] !== resources.ROLE_MANAGEMENT
    ) {
      raw_resources.push({
        name: resourceConf[k],
        permissions: {
          create: 0,
          view: 0,
          update: 0,
          delete: 0,
        },
      });
    }
  }

  return raw_resources;
}


function decodeStaticRepPermissions(staticReportsData) {
  const staticReports = [];
  const tmpReports = [];
  const allReports = [];

  Object.keys(reportClassification).forEach((key) => {
    reportClassification[key].forEach((rep) => {
      allReports.push(rep.name);
    });
  });

  for (let key in staticReportsData) {
    const name = key;
    const permissions = {
      download: 0,
      view: 0,
    };

    Object.keys(rolePermissions).forEach((permission) => {
      const permissionFlag = staticReportsData[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });

    tmpReports.push(name);
    staticReports.push({ name, permissions });
  }

  for (let k = 0; k < allReports.length; k++) {
    if (!tmpReports.includes(allReports[k])) {
      staticReports.push({
        name: allReports[k],
        permissions: {
          download: 0,
          view: 0,
        },
      });
    }
  }

  return staticReports;
}


function decodeCardPermissions(cardsData) {
  const cards = [];

  for (let key in cardsData) {
    const name = key;
    const permissions = {
      download: 0,
      view: 0,
      update: 0,
      delete: 0,
    };

    Object.keys(rolePermissions).forEach((permission) => {
      const permissionFlag = cardsData[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });

    cards.push({ name, permissions });
  }

  return cards;
}


async function decodeDynamicDashboard(dashboardData, createdBy, user) {
  const dynamicDashboard = [];
  const tmpDashboard = [];
  let allDashboards = await dashboardService.getDashboards(user, {}, {});
  allDashboards = allDashboards.data;

  for (let key in dashboardData) {
    const name = key;
    const dashboardDetails = await dashboardService.getDashboardByName(createdBy, name);

    if (dashboardDetails) {
      const permissions = {
        download: 0,
        view: 0,
      };

      Object.keys(rolePermissions).forEach((permission) => {
        const permissionFlag = dashboardData[key] & rolePermissions[permission];
        if (permissionFlag === rolePermissions[permission]) {
          permissions[permission] = 1;
        }
      });

      dynamicDashboard.push({ name, permissions });
      tmpDashboard.push(name);
    }
  }

  for (let k = 0; k < allDashboards.length; k++) {
    if (!tmpDashboard.includes(allDashboards[k].name)) {
      dynamicDashboard.push({
        name: allDashboards[k].name,
        permissions: {
          download: 0,
          view: 0,
        },
      });
    }
  }

  return dynamicDashboard;
}

const decodeDynamicReports = async (dynamicReportsData, user) => {
  const dynamicReports = [];
  const panels = await panelService.getDynamicPanels(user);
  const adminCreatedPanels = await panelService.getAdminDynamicPanels(user);

  // const allPanels = [...panels, ...adminCreatedPanels];
  const allPanels = adminCreatedPanels
  const availableReports = allPanels.map(panel => panel.name);

  // Sanity check
  if (!dynamicReportsData || typeof dynamicReportsData !== 'object' || Array.isArray(dynamicReportsData)) {
    // console.warn('decodeDynamicReports: Invalid data format:', dynamicReportsData);
    return availableReports.map(name => ({
      name,
      permissions: { view: 0, download: 0 }
    }));
  }

  const tmpReportNames = [];

  for (const name in dynamicReportsData) {
    if (!availableReports.includes(name)) continue;

    const bitmask = dynamicReportsData[name];
    const permissions = {};

    for (const perm in rolePermissions) {
      permissions[perm] = (bitmask & rolePermissions[perm]) === rolePermissions[perm] ? 1 : 0;
    }

    dynamicReports.push({ name, permissions });
    tmpReportNames.push(name);
  }

  for (const name of availableReports) {
    if (!tmpReportNames.includes(name)) {
      dynamicReports.push({
        name,
        permissions: { view: 0, download: 0 }
      });
    }
  }
  return dynamicReports;
};


async function getAllRoles(user, filter, options) {
  let count, limit, offset, page, roles_details;
  if (options) {
    limit = options.limit || null;
    page = options.page || null;
    offset = (page - 1) * limit;
  }
  if (Object.keys(filter).length == 0) {
    count = await Role.count({ where: { isSuperAdminRole: false } });
    if (limit) {
      roles_details = await Role.findAll({
        limit,
        offset,
        where: { isSuperAdminRole: false },
        include: [
          {
            model: User,
            as: "users",
          },
        ],
        order: [
          [
            "createdAt",
            "DESC"
          ]
        ],
      });
    }
    else {
      roles_details = await Role.findAll({
        where: { isSuperAdminRole: false },
        include: [
          {
            model: User,
            as: "users",
          },
        ],
        order: [
          [
            "createdAt",
            "DESC"
          ]
        ],
      });
    }
  }
  else {
    count = await Role.count({
      where: {
        isSuperAdminRole: false,
        name: { [Op.like]: `%${filter.search}%` },
      },
    });
    roles_details = await Role.findAll({
      limit,
      offset,
      where: {
        isSuperAdminRole: false,
        name: { [Op.like]: `%${filter.search}%` },
      },
      include: [
        {
          model: User,
          as: "users",
        },
      ],
      order: [
        [
          "createdAt",
          "DESC"
        ]
      ],
    });
  }

  let roles = roles_details;
  const newRoles = [];

  for (let i = 0; i < roles.length; i++) {
    const role = roles[i];

    const raw_resources = decodeResourcePermissions(
      role.resources,
    );

    const staticReports = decodeStaticRepPermissions(
      role.staticReports,
    );

    const cards = decodeCardPermissions(
      role.cards,
    );

    const dynamicDashboard = await decodeDynamicDashboard(
      role.dynamicDashboard,
      role.createdBy,
      user
    );

    const dynamicReports = await decodeDynamicReports(
      role.dynamicReports,
      user
    )

    // Convert timestamps to client time
    role.dataValues.createdAt = await convertToClientTime(role.createdAt, user.timezone);
    role.dataValues.updatedAt = await convertToClientTime(role.updatedAt, user.timezone);

    // Add user count
    role.dataValues.userCount = role.users.length;

    // Clean up unnecessary data
    delete role.dataValues.users;

    // Attach permissions
    role.resources = raw_resources;
    role.staticReports = staticReports;
    role.dynamicReports = dynamicReports;
    role.cards = cards;
    role.dynamicDashboard = dynamicDashboard;

    // Predefined role check
    const roleName = role.dataValues.name;
    role.dataValues.isPredefined = preCreatedUserRoles.includes(roleName);

    if (roleName === "Customer_role") role.dataValues.customerRole = true;
    if (roleName === "Supplier_role") role.dataValues.supplierRole = true;
    if (roleName === "Customer_Supplier_role") role.dataValues.customerSupplierRole = true;

    newRoles.push(role);
  }

  return {
    totalCount: count,
    pageNo: page,
    pageSize: limit,
    data: newRoles,
  };
}

async function createRole(payload) {
  const roleId = uuidv4();
  const roleName = payload.name;

  const cResources = encodePermissions(payload.resources || []);
  const staticR = encodePermissions(payload.staticReports || []);

  const cards = encodePermissions(payload.cards || []);
  const dynamicD = encodePermissions(payload.dynamicDashboard || []);
  const dynamicR = encodePermissions(payload.dynamicReports || []);

  const record = {
    roleId,
    name: roleName,
    description: payload.description,
    resources: cResources,
    staticReports: staticR,
    dynamicReports: dynamicR,
    cards: cards,
    dynamicDashboard: dynamicD,
    panelCount: payload.panelCount,
    dashboardCount: payload.dashboardCount,
    createdBy: payload.createdBy,
  };

  try {
    const roleRecord = await Role.create(record);
    return roleRecord;
  } catch (e) {
    if (e instanceof ValidationError) {
      for (const error of e.errors) {
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(httpStatus.BAD_REQUEST, "Role name used before");
          case "notEmpty":
            throw new ApiError(httpStatus.BAD_REQUEST, "Role name cannot be empty");
          default:
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
      }
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, e.message || e);
    }
  }
}

async function deleteRole(roleId) {
  let roleData = await Role.findByPk(roleId);
  if (!roleData) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Role does not exists");
  }
  if (roleData.name == defaultUserRole.name) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Default role cannot be deleted"
    );
  }
  let assignedRoleCheck = await User.findAll({ where: { roleId } });
  if (assignedRoleCheck && assignedRoleCheck.length > 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Role is assigned to a user, so role can't be deleted"
    );
  }

  let deletedFiles = await Role.destroy({ where: { id: roleId } }),
    response = {
      status: "OK",
      result: roleData,
    };

  return response;
}

async function getRole(user, roleId) {
  let role = await Role.findByPk(roleId);
  if (!role) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Role does not exists");
  }

  const raw_resources = decodeResourcePermissions(
    role.resources,
  );

  const staticReports = decodeStaticRepPermissions(
    role.staticReports,
  );

  const cards = decodeCardPermissions(
    role.cards
  );

  const dynamicDashboard = role.dynamicDashboard ? await decodeDynamicDashboard(
    role.dynamicDashboard,
    role.createdBy,
    user
  ) : [];

  const dynamicReports = await decodeDynamicReports(
    role.dynamicReports,
    user
  )

  role.resources = raw_resources;
  role.staticReports = staticReports;
  role.dynamicReports = dynamicReports;
  role.cards = cards;
  role.dynamicDashboard = dynamicDashboard;

  // finding predefind roles

  if (preCreatedUserRoles.includes(role.dataValues.name)) {
    role.dataValues.isPredefined = true;
  }
  else {
    role.dataValues.isPredefined = false;
  }
  return role;
}

async function getRoleByName(role) {
  let result = await Role.findOne({ where: { name: role } }),
    raw_resources = [],
    staticReports = [],
    dynamicReports = [],
    dynamicDashboard = [];
  for (let key in result.resources) {
    var name = key,
      permissions = {
        create: 0,
        view: 0,
        update: 0,
        delete: 0,
      };
    Object.keys(rolePermissions).forEach((permission) => {
      let val = rolePermissions[permission],
        permissionFlag = result.resources[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });

    raw_resources.push({
      name,
      permissions,
    });
  }
  for (let key in result.staticReports) {
    var name = key,
      permissions = {
        view: 0,
        download: 0,
      };
    Object.keys(rolePermissions).forEach((permission) => {
      let permissionFlag =
        result.staticReports[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });
    staticReports.push({
      name,
      permissions,
    });
  }
  for (let key in result.cards) {
    var name = key,
      permissions = {
        view: 0,
        download: 0,
        update: 0,
        delete: 0,
      };
    Object.keys(rolePermissions).forEach((permission) => {
      let permissionFlag = result.cards[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });
    dynamicReports.push({
      name,
      permissions,
    });
  }
  for (let key in result.dynamicDashboard) {
    var name = key,
      permissions = {
        view: 0,
        download: 0,
        update: 0,
        delete: 0,
      };
    Object.keys(rolePermissions).forEach((permission) => {
      let permissionFlag =
        result.dynamicDashboard[key] & rolePermissions[permission];
      if (permissionFlag === rolePermissions[permission]) {
        permissions[permission] = 1;
      }
    });
    dynamicDashboard.push({
      name,
      permissions,
    });
  }
  result.resources = raw_resources;
  result.staticReports = staticReports;
  result.cards = dynamicReports;
  result.dynamicDashboard = dynamicDashboard;
  return result;
}

async function updateRole(payload, roleId) {
  const oldRole = await Role.findByPk(roleId);
  if (!oldRole) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Role does not exist");
  }

  try {
    if (payload.resources) {
      payload.resources = encodePermissions(payload.resources);
    }

    if (payload.staticReports) {
      payload.staticReports = encodePermissions(payload.staticReports);
    }

    if (payload.cards) {
      payload.cards = encodePermissions(payload.cards);
    }


    if (payload.dynamicDashboard) {
      payload.dynamicDashboard = encodePermissions(payload.dynamicDashboard);
    }

    if (payload.dynamicReports) {
      payload.dynamicReports = encodePermissions(payload.dynamicReports);
    }

    Object.assign(oldRole, payload);
    const updatedRole = await oldRole.save();
    return updatedRole;

  } catch (e) {
    if (e instanceof ValidationError) {
      for (const error of e.errors) {
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(httpStatus.BAD_REQUEST, "Role name used before");
          case "notEmpty":
            throw new ApiError(httpStatus.BAD_REQUEST, "Role name cannot be empty");
          default:
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
      }
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, e.message || e);
    }
  }
}

const getAllRoleNames = async (user) => {
  let roleList = [];

  user = user.dataValues;
  if (user.isSuperAdmin) {
    let roles = await Role.findAll({
      attributes: ["name"],
      raw: true,
    });
    for (let i = 0; i < roles.length; i++) {
      roleList.push(roles[i].name);
    }
  }
  else {
    //  let role = await Role.findOne({where : {id : user.role}})
    roleList = [user.role.dataValues.name];
  }
  return roleList;
};

module.exports = {
  createRole,
  getAllRoles,
  getRole,
  getRoleByName,
  updateRole,
  deleteRole,
  getAllRoleNames,
};