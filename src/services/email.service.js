const nodemailer = require("nodemailer");
const hbs = require("nodemailer-express-handlebars");
const config = require("../config/config");
const logger = require("../config/logger");
const auditLogService = require("./audit-log.service");
const { auditLogEvents } = require("../config/roles");

const axios = require("axios");
const path = require("path");
const handlebars = require("handlebars");
const juice = require("juice");
const { off } = require("process");
const fs = require("fs").promises;

handlebars.registerHelper("length", function (arr) {
  return Array.isArray(arr) ? arr.length : 0;
});

handlebars.registerHelper("keys", function (obj) {
  return Object.keys(obj);
});

handlebars.registerHelper("values", function (obj) {
  return Object.values(obj);
});

handlebars.registerHelper("eq", function (a, b) {
  return a === b;
});

handlebars.registerHelper("gt", function (a, b) {
  return a > b;
});

handlebars.registerHelper("includes", (key = '', match = '') =>
  key.toLowerCase().includes(match.toLowerCase())
);



transport = nodemailer.createTransport(config.email.smtp);

async function ViewOption(transport, hbs) {
  transport.use(
    "compile",
    hbs({
      viewEngine: {
        extname: ".hbs", // handlebars extension
        layoutsDir: "src/templates", // location of handlebars templates
        defaultLayout: "", // name of main template
        partialsDir: "views/partials/", // location of your subtemplates aka. header, footer etc
      },
      viewPath: "src/templates",
      extName: ".hbs",
    })
  );
}

/**
 * send an email
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @returns {Promise}
 */

async function SendMailSMTP(mailOptions) {
  return new Promise((resolve, reject) => {
    const transporter = this.transport;

    ViewOption(transporter, hbs);

    mailOptions.template = `${mailOptions.template}`;
    logger.info(mailOptions);

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        logger.error(`error is ${error}`);
        // let auditLogPayload = {
        //   username: mailOptions.to,
        //   userId: "",
        //   roleName: "",
        //   event: auditLogEvents.EMAIL_SEND_FAIL,
        //   action: `Failed sending mail to ${mailOptions.to}err : ${error}`,
        // };
        // auditLogService.addAuditLog(auditLogPayload);
        resolve(error); // or use rejcet(false) but then you will have to handle errors
      } else {
        logger.info("Email sent:", JSON.stringify(info.response));
        resolve(info);
      }
    });
  });
}

  async function compileTemplate(templateName, context) {
    try {
      const templatePath = path.join(
        __dirname,
        "../templates",
        `${templateName}.hbs`
      );
      const templateContent = await fs.readFile(templatePath, "utf8");
      const template = handlebars.compile(templateContent);
      const htmlContent = template(context);
      return juice(htmlContent);
    } catch (error) {
      logger.error(`Error loading template ${templateName}:` + error);
      throw error;
    }
  }
async function getAccessToken() {
  try {
    const response = await axios.post(
      "https://oauth2.googleapis.com/token",
      null,
      {
        params: {
          client_id: config.email.oauth2.clientId,
          client_secret: config.email.oauth2.clientSecret,
          refresh_token: config.email.oauth2.refreshToken,
          grant_type: "refresh_token",
        },
      }
    );

    return response.data.access_token;
  } catch (error) {
    console.error("Failed to get access token:", error.response?.data || error);
    return null;
  }
}

function createRawEmail({ to, from, subject, html, attachments = [] }) {
  const hasAttachments = attachments.length > 0;
  const boundary = "__boundary__";
  const parts = [];

  parts.push(`To: ${to}`);
  parts.push(`From: ${from}`);
  parts.push(`Subject: ${subject}`);
  parts.push(`MIME-Version: 1.0`);

  if (hasAttachments) {
    parts.push(`Content-Type: multipart/mixed; boundary="${boundary}"`);
    parts.push(""); // End of headers

    // HTML body part
    parts.push(`--${boundary}`);
    parts.push(`Content-Type: text/html; charset="UTF-8"`);
    parts.push(`Content-Transfer-Encoding: 7bit`);
    parts.push("");
    parts.push(html);

    // Attachment parts
    for (const attachment of attachments) {
      let mimeType;
      if (attachment.filename.endsWith(".xlsx")) {
        mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      } else if (attachment.filename.endsWith(".csv")) {
        mimeType = "text/csv";
      } else if (attachment.filename.endsWith(".pdf")) {
        mimeType = "application/pdf";
      } else if (attachment.filename.endsWith(".tar.gz")) {
        mimeType = "application/gzip";
      } else if (attachment.filename.endsWith(".zip")) {
        mimeType = "application/zip";
      } else {
        mimeType = "application/octet-stream";
      }

      parts.push(`--${boundary}`);
      parts.push(`Content-Type: ${mimeType}; name="${attachment.filename}"`);
      parts.push(`Content-Disposition: attachment; filename="${attachment.filename}"`);
      parts.push(`Content-Transfer-Encoding: base64`);
      parts.push("");
      parts.push(
        Buffer.isBuffer(attachment.content)
          ? attachment.content.toString("base64")
          : attachment.content
      );
    }

    parts.push(`--${boundary}--`);
  } else {
    // No attachments: plain HTML email
    parts.push(`Content-Type: text/html; charset="UTF-8"`);
    parts.push(`Content-Transfer-Encoding: 7bit`);
    parts.push("");
    parts.push(html);
  }

  return Buffer.from(parts.join("\n"))
    .toString("base64")
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");
}


async function SendMailGmail(emailContent) {
  const accessToken = await getAccessToken();
  if (!accessToken) {
    logger.error("OAuth2 token retrieval failed");
    return { isSuccess: false, data: "OAuth2 token retrieval failed" };
  }

  const {
    from: senderEmail,
    to: recipientEmail,
    subject,
    template,
    context,
    htmlBody,
  } = emailContent;

  try {
    const html = htmlBody || (await compileTemplate(template, context || {}));

    const rawEmail = createRawEmail({
      to: recipientEmail,
      from: senderEmail,
      subject,
      html,
      attachments: emailContent.attachments ? [emailContent.attachments] : emailContent.attachments // Wrap single attachment in array
    });

    const url = `https://gmail.googleapis.com/gmail/v1/users/${config.email.oauth2.emailId}/messages/send`;
    console.log("url is " + url);

    const response = await axios.post(
      url,
      { raw: rawEmail },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log(`Gmail API response: ${response.status} - ${response.data}`);

    return response.status === 200
      ? { isSuccess: true, data: response.data }
      : {
          isSuccess: false,
          data: `${response.status} - ${response.statusText}`,
        };
  } catch (error) {
    console.log("error in gamil->", error.response?.data?.error?.message || error.message)
    const errMsg = error.response?.data?.error?.message || error.message;

    logger.error(`Gmail API error: ${errMsg}`);

    // const auditLogPayload = {
    //   username: recipientEmail,
    //   userId: "",
    //   roleName: "",
    //   event: auditLogEvents.EMAIL_SEND_FAIL,
    //   action: `Failed sending mail to ${recipientEmail}. Error: ${errMsg}`,
    // };
    // auditLogService.addAuditLog(auditLogPayload);

    return { isSuccess: false, data: errMsg };
  }
}

const MailService = async (emailContent) => {
  try {
    let mailInfo;

    if (config.GAMIL_OAUTH) {
      // ✅ Gmail OAuth2-specific service function
      mailInfo = await SendMailGmail(emailContent);
      logger.info("mailInfo is send via gmail" + JSON.stringify(mailInfo));
      return mailInfo?.isSuccess
        ? { isSuccess: true }
        : { isSuccess: false, data: mailInfo?.data || "Unknown error" };
    } else {
      // ✅ Call SMTP-specific service function
      mailInfo = await SendMailSMTP(emailContent);
      logger.info("mailInfo is send via smtp" + JSON.stringify(mailInfo));

      if (mailInfo.response?.includes("250")) {
        return { isSuccess: true };
      }

      return {
        isSuccess: false,
        data: mailInfo.response || "SMTP send failed",
      };
    }
  } catch (error) {
    logger.error("Error while sending email:" + error.message)
    return {
      isSuccess: false,
      code: "SERVER_ERROR12",
      data: error,
    };
  }
};

module.exports = {
  MailService,
};
