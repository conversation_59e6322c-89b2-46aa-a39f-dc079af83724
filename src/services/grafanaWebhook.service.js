const lodash = require("lodash");
const moment = require("moment");
const { MailService } = require("./email.service");
const logger = require("../config/logger");
const { alertConfig } = require("../config/alertConfig");
const { models } = require("../models");
const { Op } = require("sequelize");

const fs = require("fs");
const path = require("path");


// function extractValuesFromValueString(str) {
//   console.log("strig is ", str);
//   if (!str) {
//     return {};
//   }
//   // const regex = /var='([^']*)'\s+labels=\{([^\}]*)\}\s+value=([\d.]+)/;
//   const regex = /var='([^']*)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/g;




//   const arr = str.split(", ");

//   const finalObj = {};
//   arr.forEach((str) => {
//     // Match the string with the regex
//     const match = str.match(regex);

//     if (match) {
//       const variable = match[1]; // Extracted var
//       const labels = match[2]; // Extracted labels
//       const value = parseFloat(match[3]); // Extracted value, converted to a number
//       finalObj[variable] = { labels, value };
//     } else {
//       console.error("String format does not match the expected pattern.", str);

//       //   `[ var='percentage_difference' labels={customer_name=Wirelsc_C_P2P} value=-49.533554017454115 ]`;
//       const regex2 = /var='([^']+)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/;
//       const match2 = str.match(regex2);

//       if (match2) {
//         const variable = match2[1]; // Extracted var
//         const labels = match2[2]; // Extracted labels
//         const value = parseFloat(match2[3]); // Extracted value, converted to a number
//         finalObj[variable] = { labels, value };
//       } else {
//         console.error("String format does not match the expected 2nd pattern.");
//       }
//     }
//   });
//   return finalObj;
// }

function extractValuesFromValueString(str) {
  console.log("Input string:", str);

  if (!str) {
    return {};
  }

  const regex = /var='([^']*)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/g;

  const finalObj = {};
  let match;

  while ((match = regex.exec(str)) !== null) {
    const variable = match[1]; // Extracted var
    const labelsString = match[2]; // Extracted labels
    const value = parseFloat(match[3]); // Extracted value

    // Convert labels into an object
    const labels = {};
    if (labelsString) {
      labelsString.split(", ").forEach((label) => {
        const [key, val] = label.split("=");
        if (key && val !== undefined) {
          labels[key.trim()] = val.trim();
        }
      });
    }

    finalObj[variable] = { labels, value };
  }

  if (Object.keys(finalObj).length === 0) {
    console.error("String format does not match the expected pattern.");
  }

  return finalObj;
}



let adminEmailCache;
async function getAdminGroupEmails() {
  if (adminEmailCache) {
    logger.info(`Admin emails cache is found: ${adminEmailCache}`);
    return adminEmailCache;
  }
  const STR_ADMIN_GROUP = "admin group";
  try {
    const whereClause = {
      name: {
        [Op.like]: `%${STR_ADMIN_GROUP}%`,
      },
    };
    const adminGroup = await models.Email_group.findOne({
      where: whereClause,
      raw: true,
      attributes: ["members"],
    });
    if (!adminGroup) {
      throw new Error(`Admin group not found. Name search: ${STR_ADMIN_GROUP}`);
    }
    adminEmailCache = adminGroup.members.join(",");
    logger.info(`Admin emails: ${adminEmailCache}`);
    return adminEmailCache;
  } catch (error) {
    console.log("error from getAdminGroupEmails", error);
    logger.error(error);
    return null;
  }
}

const savePayloadToFile = async (payload) => {
  const date = moment().format("DD-MM-YYYY"); // Format: 13-02-2025
  const directory = "grafana-payload"; // Directory name
  const fileName = `grafana_payload_${date}.json`;
  const filePath = path.join(directory, fileName); // Full path

  // Ensure the directory exists
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }

  // Convert the new payload to a JSON string with proper formatting
  const newEntry = JSON.stringify(payload, null, 2);

  // Append the new entry to the file with a separator for clarity
  fs.appendFile(filePath, `\n${newEntry},\n`, (err) => {
    if (err) {
      console.error("Error appending payload:", err);
    } else {
      console.log(`Payload appended successfully to ${filePath}`);
    }
  });
}


/**
 * Groups alerts by customer > supplier > destination
 * @param {Array} alerts - Array of alert objects
 * @returns {Array} - Array of { groupKey, groupValue, alerts }
 */
const groupAlerts = (alerts) => {
  const groupMap = {};
  alerts.forEach((alert) => {
    const labels = alert.labels || {};
    let groupKey = "Customer", groupValue = labels.customer_name && labels.customer_name.trim();
    if (!groupValue) {
      groupKey = "Supplier";
      groupValue = labels.supplier && labels.supplier.trim();
    }
    if (!groupValue) {
      groupKey = "Destination";
      groupValue = labels.destination && labels.destination.trim();
    }
    if (!groupValue) {
      groupKey = "Unknown";
      groupValue = "Unknown Group";
    }
    const mapKey = `${groupKey}:${groupValue}`;
    if (!groupMap[mapKey]) {
      groupMap[mapKey] = { groupKey, groupValue, alerts: [] };
    }
    groupMap[mapKey].alerts.push(alert);
  });
  return Object.values(groupMap);
};

/**
 * Process alert data to create formatted data objects for email
 * @param {Array} alerts - Array of alert objects
 * @returns {Array} - Array of formatted data objects, now including customerName and supplier
 */
const processAlertData = (alerts, meta_alert_type) => {
  return alerts.map((alert) => {
    console.log("Processing alert:", alert.labels.customer_name, alert.labels.destination);
    const extractedValues = extractValuesFromValueString(alert.valueString);

    console.log("extractedValues:", extractedValues);

    const customerName = alert.labels.customer_name || "";
    const destinationName = alert.labels.destination || "";
    const supplierName = alert.labels.supplier || "";

    const TS = lodash.get(extractedValues, "today_total_sub.value", "");
    const TSP = lodash.get(extractedValues, "total_submissions_previous.value", "");
    const variation = lodash.get(extractedValues, "percentage_difference.value", "");

    let tDLR = lodash.get(extractedValues, "today_dlr_percentage.value", "");
    tDLR = Number(tDLR).toFixed(4);
    let prevDLR = lodash.get(extractedValues, "pre_dlr_percentage.value", "");
    prevDLR = Number(prevDLR).toFixed(4);
    let dlrVariation = tDLR && prevDLR ? (tDLR - prevDLR).toFixed(4) : 0;

    const delivery_failure_count_new_current = lodash.get(extractedValues, "delivery_failure_count_new_current.value", "");
    const delivery_failure_percentage_today = lodash.get(extractedValues, "delivery_failure_percentage_today.value", "");
    const total_submissions_current = lodash.get(extractedValues, "total_submissions_current.value", "");

    const delivery_failure_count_new_previous = lodash.get(extractedValues, "delivery_failure_count_new_previous.value", "");
    const delivery_success_previous = lodash.get(extractedValues, "delivery_success_previous.value", "");
    const delivery_success_today = lodash.get(extractedValues, "delivery_success_today.value", "");
    const total_submissions_previous = lodash.get(extractedValues, "total_submissions_previous.value", "");

    const avg_delivery_success_percentage_previous = lodash.get(extractedValues, "avg_delivery_success_percentage_previous.value", "");
    const delivery_success_percentage_difference = lodash.get(extractedValues, "delivery_success_percentage_difference.value", "");
    const delivery_success_percentage_today = lodash.get(extractedValues, "delivery_success_percentage_today.value", "");
    let obj;

    console.log("meta_alert_type:", meta_alert_type);
    console.log("total_submissions_current:", total_submissions_current);

    switch (meta_alert_type) {

      case alertConfig.types.VOLUME_DROP_OR_SPIKE:
        obj = {
          "Today Submission": TS || total_submissions_current,
          "Yesterday Submission": TSP,
          "Volume Variation": Number(variation).toFixed(4),
          "Today DLR": tDLR,
          "Yesterday DLR": prevDLR,
          "DLR Variation": dlrVariation,
        };
        break;
      case alertConfig.types.ERROR:
        obj = {
          "Today Submission": total_submissions_current,
          "Today Delivery Failure Count": delivery_failure_count_new_current,
          "Today Delivery Failure Percentage": delivery_failure_percentage_today,
        };
        break;
      case alertConfig.types.DELIVERY_DROP:
        obj = {
          "Today Submission": total_submissions_current,
          "Today Delivery Failure Count": delivery_failure_count_new_current,
          "Previous Delivery Success Percentage": avg_delivery_success_percentage_previous,
          "Today Delivery Success Percentage": delivery_success_percentage_today,
          "Delievery Success Precentage Difference": delivery_success_percentage_difference
        };
        break;
      case alertConfig.types.DELIVERY_REPORT_DROP:
        obj = {
          "Today Submission": total_submissions_current,
          "Today Delivery Failure Count": delivery_failure_count_new_current,
          "Yesterday Delivery Failure Count": delivery_failure_count_new_previous,
          "Yesterday Delivery Success Count": delivery_success_previous,
          "Today Delivery Success Count": delivery_success_today,
        };
        break;
      default:
        obj = {
          "Unknown Metric": "N/A",
        };
    }

    // if (meta_alert_type === alertConfig.types.ERROR) {
    //   const delivery_failure_count_new_current = lodash.get(extractedValues, "delivery_failure_count_new_current.value", "");
    //   const delivery_failure_percentage_today = lodash.get(extractedValues, "delivery_failure_percentage_today.value", "");
    //   const total_submissions_current = lodash.get(extractedValues, "total_submissions_current.value", "");
    //   obj = {
    //     "Today Submission": total_submissions_current,
    //     "Today Delivery Failure Count": delivery_failure_count_new_current,
    //     "Today Delivery Failure Percentage": delivery_failure_percentage_today,
    //   };
    // } else {
    //   obj = {
    //     "Today Submission": TS,
    //     "Yesterday Submission": TSP,
    //     "Volume Variation": Number(variation).toFixed(4),
    //     "Today DLR": tDLR,
    //     "Yesterday DLR": prevDLR,
    //     "DLR Variation": dlrVariation,
    //   };
    // }

    // Note: No need to conditionally add customerName here as we are grouping by it
    // if (customerName) {
    //   obj = { Customer: customerName, ...obj };
    // }

    if (destinationName) {
      obj = { Destination: destinationName, ...obj }
    }

    if (supplierName) {
      obj = { Supplier: supplierName, ...obj }
    }

    return obj;
  });
};

/**
 * Determine notification type based on alert metadata and variation
 * @param {string} meta_alert_type - Alert type from metadata
 * @param {number|string} variation - Percentage variation value
 * @returns {string} - Notification type description
 */
const getNotificationType = (meta_alert_type, variation) => {
  const isSpike = variation && variation.toString().includes("-");

  switch (meta_alert_type) {
    case alertConfig.types.VOLUME_DROP_OR_SPIKE:
      return isSpike ? "Volume Spike" : "Volume Dip";
    case alertConfig.types.ERROR:
      return "Error";
    case alertConfig.types.DELIVERY_DROP:
      return "Delivery Drop";
    case alertConfig.types.DELIVERY_REPORT_DROP:
      return "Delivery Report Drop";
    default:
      return "Unknown Alert Type";
  }
};

const grafanaWebhook = async (req) => {
  const payload = req.body;

  // ✅ Save payload to file only in production
  if (process.env.NODE_ENV === 'production') {
    await savePayloadToFile(payload);
  }
  const { commonLabels = {}, alerts = [], title, message } = payload;
  const { meta_alert_type } = commonLabels;
  let contactPoint = lodash.get(commonLabels, "meta_contact_point", "");
  if (!contactPoint) {
    const adminGroupEmails = await getAdminGroupEmails();
    contactPoint = adminGroupEmails;
  }

  const startTime = lodash.get(alerts, '[0].startsAt', '');
  const endTime = lodash.get(alerts, '[0].endsAt', '');

  logger.info(`Webhook logs:\n`);
  logger.info(message);



  // Group alerts by customer > supplier > destination
  const alertGroups = groupAlerts(alerts);
  logger.info(`Found ${alertGroups.length} alert groups`);

  // Send an email for each group
  const emailResults = [];

  for (const group of alertGroups) {
    const { groupKey, groupValue, alerts: groupAlertsArr } = group;
    logger.info(`Sending email for group: ${groupKey} - ${groupValue}`);

    // Process all alerts for this group
    const data = processAlertData(groupAlertsArr, meta_alert_type);

    // Get the variation value from the first alert of this group
    // Assuming the meta_alert_type and variation are consistent across alerts for the same group
    const firstAlert = groupAlertsArr[0] || {};
    const firstAlertValues = firstAlert.values || {};
    const variation = firstAlertValues.percentage_difference;

    // Determine notification type
    const notificationType = getNotificationType(meta_alert_type, variation);

    const alertname = (firstAlert.labels && firstAlert.labels.alertname) || "";

    // Create email object for this group
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: contactPoint,
      subject: `${alertname} - ${groupValue}`,
      template: "volume_dip_notification",
      context: {
        groupKey,
        groupValue,
        startTime: moment(startTime).format("HH:MM:SS"),
        endTime: moment(endTime).format("HH:MM:SS"),
        teamName: "SMSHUB Team",
        data, // 'data' now contains objects for all alerts in this group
        notificationType,
      },
      helpers: {
        keys: (obj) => Object.keys(obj),
        values: (obj) => Object.values(obj),
        length: (array) => (Array.isArray(array) ? array.length : 0),
        eq: (a, b) => a === b, // Checks if a is equal to b
        gt: (a, b) => a > b, // Checks if a is greater than b
      },
    };

    logger.info(`Email for ${groupValue}:`, emailObj);

    try {
      const emailRes = await MailService(emailObj);
      emailResults.push({
        groupValue,
        result: emailRes
      });
    } catch (error) {
      logger.error(`Error sending email for ${groupValue}:`, error);
      emailResults.push({
        groupValue,
        error: error.message || 'Unknown error'
      });
    }
  }

  return {
    success: true,
    emailsSent: emailResults.length,
    results: emailResults
  };
};


const grafanaWebhookPayload = {
  // body: {
  //   "receiver": "webhook",
  //   "status": "firing",
  //   "alerts": [
  //     {
  //       "status": "firing",
  //       "labels": {
  //         "alertname": "DRP1",
  //         "contact_point": "webhook",
  //         "grafana_folder": "SMSHUB Alerts",
  //         "max_allowed_percentage_change": "10%",
  //         "meta_alert_type": "Delivery Report Pending",
  //         "meta_contact_point": "<EMAIL>",
  //         "name": "DRP1",
  //         "supplier": "NEXMO INC"
  //       },
  //       "annotations": {},
  //       "startsAt": "2025-07-21T10:45:00+05:30",
  //       "endsAt": "0001-01-01T00:00:00Z",
  //       "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/f24c6cff-fb87-4126-9130-3c6e489ad9fc/view?orgId=1",
  //       "fingerprint": "acb8aa3855ea19cb",
  //       "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DDRP1&matcher=contact_point%3Dwebhook&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_alert_type%3DDelivery+Report+Pending&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Ccaroline.synthia%40openturf.in&matcher=name%3DDRP1&matcher=supplier%3DNEXMO+INC&orgId=1",
  //       "dashboardURL": "",
  //       "panelURL": "",
  //       "values": {
  //         "delivery_failure_count_new_current": 8,
  //         "delivery_failure_count_new_previous": 0,
  //         "delivery_success_previous": 1,
  //         "delivery_success_today": 0,
  //         "percentage_difference": 100,
  //         "total_submissions_current": 93,
  //         "total_submissions_previous": 6,
  //         "trigger_alert": 1,
  //         "valid_submissions_today": 1
  //       },
  //       "valueString": "[ var='delivery_failure_count_new_current' labels={supplier=NEXMO INC} value=8 ], [ var='delivery_failure_count_new_previous' labels={supplier=NEXMO INC} value=0 ], [ var='delivery_success_previous' labels={supplier=NEXMO INC} value=1 ], [ var='delivery_success_today' labels={supplier=NEXMO INC} value=0 ], [ var='percentage_difference' labels={supplier=NEXMO INC} value=100 ], [ var='total_submissions_current' labels={supplier=NEXMO INC} value=93 ], [ var='total_submissions_previous' labels={supplier=NEXMO INC} value=6 ], [ var='trigger_alert' labels={supplier=NEXMO INC} value=1 ], [ var='valid_submissions_today' labels={supplier=NEXMO INC} value=1 ]"
  //     }
  //   ],
  //   "groupLabels": {
  //     "alertname": "DRP1",
  //     "grafana_folder": "SMSHUB Alerts"
  //   },
  //   "commonLabels": {
  //     "alertname": "DRP1",
  //     "contact_point": "webhook",
  //     "grafana_folder": "SMSHUB Alerts",
  //     "max_allowed_percentage_change": "10%",
  //     "meta_alert_type": "Delivery Report Pending",
  //     "meta_contact_point": "<EMAIL>",
  //     "name": "DRP1",
  //     "supplier": "NEXMO INC"
  //   },
  //   "commonAnnotations": {},
  //   "externalURL": "http://hub-release.openturf.dev:3000/grafana/",
  //   "version": "1",
  //   "groupValue": "{}:{alertname=\"DRP1\", grafana_folder=\"SMSHUB Alerts\"}",
  //   "truncatedAlerts": 0,
  //   "orgId": 1,
  //   "title": "[FIRING:1] DRP1 SMSHUB Alerts (webhook 10% Delivery <NAME_EMAIL> DRP1 NEXMO INC)",
  //   "state": "alerting",
  //   "message": "**Firing**\n\nValue: delivery_failure_count_new_current=8, delivery_failure_count_new_previous=0, delivery_success_previous=1, delivery_success_today=0, percentage_difference=100, total_submissions_current=93, total_submissions_previous=6, trigger_alert=1, valid_submissions_today=1\nLabels:\n - alertname = DRP1\n - contact_point = webhook\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_alert_type = Delivery Report Pending\n - meta_contact_point = <EMAIL>\n - name = DRP1\n - supplier = NEXMO INC\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/f24c6cff-fb87-4126-9130-3c6e489ad9fc/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DDRP1&matcher=contact_point%3Dwebhook&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_alert_type%3DDelivery+Report+Pending&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Ccaroline.synthia%40openturf.in&matcher=name%3DDRP1&matcher=supplier%3DNEXMO+INC&orgId=1\n"
  // },
  // body: {
  //   "receiver": "webhook",
  //   "status": "firing",
  //   "alerts": [
  //     {
  //       "status": "firing",
  //       "labels": {
  //         "alertname": "VolSpiDrop",
  //         "contact_point": "webhook",
  //         "destination": "Hutchison Macau",
  //         "grafana_folder": "SMSHUB Alerts",
  //         "max_allowed_percentage_change": "10%",
  //         "meta_additional_type": "spike",
  //         "meta_alert_type": "Volume Drop or Spike",
  //         "meta_contact_point": "<EMAIL>",
  //         "name": "VolSpiDrop"
  //       },
  //       "annotations": {},
  //       "startsAt": "2025-07-21T10:01:00+05:30",
  //       "endsAt": "0001-01-01T00:00:00Z",
  //       "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1",
  //       "fingerprint": "7f8ca0eec39827e1",
  //       "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1",
  //       "dashboardURL": "",
  //       "panelURL": "",
  //       "values": {
  //         "avg_previous_total_sub": 136,
  //         "delivery_report_success_current": 108,
  //         "delivery_report_success_previous": 11,
  //         "min_sub_check": 1,
  //         "no_data_check": 0,
  //         "percentage_difference": 4223.529411764705,
  //         "pre_dlr_percentage": 8.088235294117647,
  //         "today_dlr_percentage": 1.8367346938775513,
  //         "today_total_sub": 5880,
  //         "total_submissions_previous": 680,
  //         "trigger_alert": 1
  //       },
  //       "valueString": "[ var='avg_previous_total_sub' labels={destination=Hutchison Macau} value=136 ], [ var='delivery_report_success_current' labels={destination=Hutchison Macau} value=108 ], [ var='delivery_report_success_previous' labels={destination=Hutchison Macau} value=11 ], [ var='min_sub_check' labels={destination=Hutchison Macau} value=1 ], [ var='no_data_check' labels={destination=Hutchison Macau} value=0 ], [ var='percentage_difference' labels={destination=Hutchison Macau} value=4223.529411764705 ], [ var='pre_dlr_percentage' labels={destination=Hutchison Macau} value=8.088235294117647 ], [ var='today_dlr_percentage' labels={destination=Hutchison Macau} value=1.8367346938775513 ], [ var='today_total_sub' labels={destination=Hutchison Macau} value=5880 ], [ var='total_submissions_previous' labels={destination=Hutchison Macau} value=680 ], [ var='trigger_alert' labels={destination=Hutchison Macau} value=1 ]"
  //     },
  //     {
  //       "status": "firing",
  //       "labels": {
  //         "alertname": "VolSpiDrop",
  //         "contact_point": "webhook",
  //         "destination": "Maxis Communication BHD Malaysia",
  //         "grafana_folder": "SMSHUB Alerts",
  //         "max_allowed_percentage_change": "10%",
  //         "meta_additional_type": "spike",
  //         "meta_alert_type": "Volume Drop or Spike",
  //         "meta_contact_point": "<EMAIL>",
  //         "name": "VolSpiDrop"
  //       },
  //       "annotations": {},
  //       "startsAt": "2025-07-21T10:01:00+05:30",
  //       "endsAt": "0001-01-01T00:00:00Z",
  //       "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1",
  //       "fingerprint": "76d20d5d6506b8d8",
  //       "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DMaxis+Communication+BHD+Malaysia&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1",
  //       "dashboardURL": "",
  //       "panelURL": "",
  //       "values": {
  //         "avg_previous_total_sub": 73.6,
  //         "delivery_report_success_current": 50,
  //         "delivery_report_success_previous": 10,
  //         "min_sub_check": 1,
  //         "no_data_check": 0,
  //         "percentage_difference": 4022.2826086956525,
  //         "pre_dlr_percentage": 13.586956521739133,
  //         "today_dlr_percentage": 1.6479894528675016,
  //         "today_total_sub": 3034,
  //         "total_submissions_previous": 368,
  //         "trigger_alert": 1
  //       },
  //       "valueString": "[ var='avg_previous_total_sub' labels={destination=Maxis Communication BHD Malaysia} value=73.6 ], [ var='delivery_report_success_current' labels={destination=Maxis Communication BHD Malaysia} value=50 ], [ var='delivery_report_success_previous' labels={destination=Maxis Communication BHD Malaysia} value=10 ], [ var='min_sub_check' labels={destination=Maxis Communication BHD Malaysia} value=1 ], [ var='no_data_check' labels={destination=Maxis Communication BHD Malaysia} value=0 ], [ var='percentage_difference' labels={destination=Maxis Communication BHD Malaysia} value=4022.2826086956525 ], [ var='pre_dlr_percentage' labels={destination=Maxis Communication BHD Malaysia} value=13.586956521739133 ], [ var='today_dlr_percentage' labels={destination=Maxis Communication BHD Malaysia} value=1.6479894528675016 ], [ var='today_total_sub' labels={destination=Maxis Communication BHD Malaysia} value=3034 ], [ var='total_submissions_previous' labels={destination=Maxis Communication BHD Malaysia} value=368 ], [ var='trigger_alert' labels={destination=Maxis Communication BHD Malaysia} value=1 ]"
  //     },
  //     {
  //       "status": "firing",
  //       "labels": {
  //         "alertname": "VolSpiDrop",
  //         "contact_point": "webhook",
  //         "destination": "Vodafone Italy",
  //         "grafana_folder": "SMSHUB Alerts",
  //         "max_allowed_percentage_change": "10%",
  //         "meta_additional_type": "spike",
  //         "meta_alert_type": "Volume Drop or Spike",
  //         "meta_contact_point": "<EMAIL>",
  //         "name": "VolSpiDrop"
  //       },
  //       "annotations": {},
  //       "startsAt": "2025-07-21T10:01:00+05:30",
  //       "endsAt": "0001-01-01T00:00:00Z",
  //       "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1",
  //       "fingerprint": "52c8bd25265d02f2",
  //       "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DVodafone+Italy&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1",
  //       "dashboardURL": "",
  //       "panelURL": "",
  //       "values": {
  //         "avg_previous_total_sub": 33.6,
  //         "delivery_report_success_current": 33,
  //         "delivery_report_success_previous": 3,
  //         "min_sub_check": 1,
  //         "no_data_check": 0,
  //         "percentage_difference": 4245.238095238095,
  //         "pre_dlr_percentage": 8.928571428571429,
  //         "today_dlr_percentage": 2.26027397260274,
  //         "today_total_sub": 1460,
  //         "total_submissions_previous": 168,
  //         "trigger_alert": 1
  //       },
  //       "valueString": "[ var='avg_previous_total_sub' labels={destination=Vodafone Italy} value=33.6 ], [ var='delivery_report_success_current' labels={destination=Vodafone Italy} value=33 ], [ var='delivery_report_success_previous' labels={destination=Vodafone Italy} value=3 ], [ var='min_sub_check' labels={destination=Vodafone Italy} value=1 ], [ var='no_data_check' labels={destination=Vodafone Italy} value=0 ], [ var='percentage_difference' labels={destination=Vodafone Italy} value=4245.238095238095 ], [ var='pre_dlr_percentage' labels={destination=Vodafone Italy} value=8.928571428571429 ], [ var='today_dlr_percentage' labels={destination=Vodafone Italy} value=2.26027397260274 ], [ var='today_total_sub' labels={destination=Vodafone Italy} value=1460 ], [ var='total_submissions_previous' labels={destination=Vodafone Italy} value=168 ], [ var='trigger_alert' labels={destination=Vodafone Italy} value=1 ]"
  //     }
  //   ],
  //   "groupLabels": {
  //     "alertname": "VolSpiDrop",
  //     "grafana_folder": "SMSHUB Alerts"
  //   },
  //   "commonLabels": {
  //     "alertname": "VolSpiDrop",
  //     "contact_point": "webhook",
  //     "grafana_folder": "SMSHUB Alerts",
  //     "max_allowed_percentage_change": "10%",
  //     "meta_additional_type": "spike",
  //     "meta_alert_type": "Volume Drop or Spike",
  //     "meta_contact_point": "<EMAIL>",
  //     "name": "VolSpiDrop"
  //   },
  //   "commonAnnotations": {},
  //   "externalURL": "http://hub-release.openturf.dev:3000/grafana/",
  //   "version": "1",
  //   "groupKey": "{}:{alertname=\"VolSpiDrop\", grafana_folder=\"SMSHUB Alerts\"}",
  //   "truncatedAlerts": 0,
  //   "orgId": 1,
  //   "title": "[FIRING:3] VolSpiDrop SMSHUB Alerts (webhook 10% spike Volume Drop <NAME_EMAIL> VolSpiDrop)",
  //   "state": "alerting",
  //   "message": "**Firing**\n\nValue: avg_previous_total_sub=136, delivery_report_success_current=108, delivery_report_success_previous=11, min_sub_check=1, no_data_check=0, percentage_difference=4223.529411764705, pre_dlr_percentage=8.088235294117647, today_dlr_percentage=1.8367346938775513, today_total_sub=5880, total_submissions_previous=680, trigger_alert=1\nLabels:\n - alertname = VolSpiDrop\n - contact_point = webhook\n - destination = Hutchison Macau\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VolSpiDrop\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1\n\nValue: avg_previous_total_sub=73.6, delivery_report_success_current=50, delivery_report_success_previous=10, min_sub_check=1, no_data_check=0, percentage_difference=4022.2826086956525, pre_dlr_percentage=13.586956521739133, today_dlr_percentage=1.6479894528675016, today_total_sub=3034, total_submissions_previous=368, trigger_alert=1\nLabels:\n - alertname = VolSpiDrop\n - contact_point = webhook\n - destination = Maxis Communication BHD Malaysia\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VolSpiDrop\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DMaxis+Communication+BHD+Malaysia&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1\n\nValue: avg_previous_total_sub=33.6, delivery_report_success_current=33, delivery_report_success_previous=3, min_sub_check=1, no_data_check=0, percentage_difference=4245.238095238095, pre_dlr_percentage=8.928571428571429, today_dlr_percentage=2.26027397260274, today_total_sub=1460, total_submissions_previous=168, trigger_alert=1\nLabels:\n - alertname = VolSpiDrop\n - contact_point = webhook\n - destination = Vodafone Italy\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VolSpiDrop\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fe2126fe-382a-4a83-9d4e-cf572baa9b6a/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVolSpiDrop&matcher=contact_point%3Dwebhook&matcher=destination%3DVodafone+Italy&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Ddhivya.elango%40openturf.in&matcher=name%3DVolSpiDrop&orgId=1\n"
  // },
//   body: {
//   "receiver": "webhook",
//   "status": "firing",
//   "alerts": [
//     {
//       "status": "firing",
//       "labels": {
//         "alertname": "VOL-Spike",
//         "contact_point": "webhook",
//         "destination": "Hutchison Macau",
//         "grafana_folder": "SMSHUB Alerts",
//         "max_allowed_percentage_change": "5%",
//         "meta_additional_type": "spike",
//         "meta_alert_type": "Volume Drop or Spike",
//         "meta_contact_point": "<EMAIL>",
//         "name": "VOL-Spike"
//       },
//       "annotations": {},
//       "startsAt": "2025-07-21T10:02:00+05:30",
//       "endsAt": "0001-01-01T00:00:00Z",
//       "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fde39719-97b7-4204-8a05-1090f2c73525/view?orgId=1",
//       "fingerprint": "bf6fea209c580709",
//       "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVOL-Spike&matcher=contact_point%3Dwebhook&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D5%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVOL-Spike&orgId=1",
//       "dashboardURL": "",
//       "panelURL": "",
//       "values": {
//         "avg_previous_total_sub": 113.33333333333333,
//         "delivery_report_success_current": 98,
//         "delivery_report_success_previous": 7,
//         "min_sub_check": 1,
//         "no_data_check": 0,
//         "percentage_difference": 4727.352941176471,
//         "pre_dlr_percentage": 6.176470588235294,
//         "today_dlr_percentage": 1.7912630232133064,
//         "today_total_sub": 5471,
//         "total_submissions_previous": 340,
//         "trigger_alert": 1
//       },
//       "valueString": "[ var='avg_previous_total_sub' labels={destination=Hutchison Macau} value=113.33333333333333 ], [ var='delivery_report_success_current' labels={destination=Hutchison Macau} value=98 ], [ var='delivery_report_success_previous' labels={destination=Hutchison Macau} value=7 ], [ var='min_sub_check' labels={destination=Hutchison Macau} value=1 ], [ var='no_data_check' labels={destination=Hutchison Macau} value=0 ], [ var='percentage_difference' labels={destination=Hutchison Macau} value=4727.352941176471 ], [ var='pre_dlr_percentage' labels={destination=Hutchison Macau} value=6.176470588235294 ], [ var='today_dlr_percentage' labels={destination=Hutchison Macau} value=1.7912630232133064 ], [ var='today_total_sub' labels={destination=Hutchison Macau} value=5471 ], [ var='total_submissions_previous' labels={destination=Hutchison Macau} value=340 ], [ var='trigger_alert' labels={destination=Hutchison Macau} value=1 ]"
//     }
//   ],
//   "groupLabels": {
//     "alertname": "VOL-Spike",
//     "grafana_folder": "SMSHUB Alerts"
//   },
//   "commonLabels": {
//     "alertname": "VOL-Spike",
//     "contact_point": "webhook",
//     "destination": "Hutchison Macau",
//     "grafana_folder": "SMSHUB Alerts",
//     "max_allowed_percentage_change": "5%",
//     "meta_additional_type": "spike",
//     "meta_alert_type": "Volume Drop or Spike",
//     "meta_contact_point": "<EMAIL>",
//     "name": "VOL-Spike"
//   },
//   "commonAnnotations": {},
//   "externalURL": "http://hub-release.openturf.dev:3000/grafana/",
//   "version": "1",
//   "groupKey": "{}:{alertname=\"VOL-Spike\", grafana_folder=\"SMSHUB Alerts\"}",
//   "truncatedAlerts": 0,
//   "orgId": 1,
//   "title": "[FIRING:1] VOL-Spike SMSHUB Alerts (webhook Hutchison Macau 5% spike Volume Drop <NAME_EMAIL> VOL-Spike)",
//   "state": "alerting",
//   "message": "**Firing**\n\nValue: avg_previous_total_sub=113.33333333333333, delivery_report_success_current=98, delivery_report_success_previous=7, min_sub_check=1, no_data_check=0, percentage_difference=4727.352941176471, pre_dlr_percentage=6.176470588235294, today_dlr_percentage=1.7912630232133064, today_total_sub=5471, total_submissions_previous=340, trigger_alert=1\nLabels:\n - alertname = VOL-Spike\n - contact_point = webhook\n - destination = Hutchison Macau\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 5%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VOL-Spike\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/fde39719-97b7-4204-8a05-1090f2c73525/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVOL-Spike&matcher=contact_point%3Dwebhook&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D5%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVOL-Spike&orgId=1\n"
// },

body: {
  "receiver": "webhook",
  "status": "firing",
  "alerts": [
    {
      "status": "firing",
      "labels": {
        "alertname": "VLS_0_15mins",
        "contact_point": "webhook",
        "customer_name": "NEXMO INC",
        "destination": "Hutchison Macau",
        "grafana_folder": "SMSHUB Alerts",
        "max_allowed_percentage_change": "1%",
        "meta_additional_type": "spike",
        "meta_alert_type": "Volume Drop or Spike",
        "meta_contact_point": "<EMAIL>",
        "name": "VLS_0_15mins"
      },
      "annotations": {},
      "startsAt": "2025-07-21T10:01:00+05:30",
      "endsAt": "0001-01-01T00:00:00Z",
      "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/da8aa438-a59d-48ec-bf5e-0d27948c9119/view?orgId=1",
      "fingerprint": "886f504561ffb062",
      "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVLS_0_15mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D1%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVLS_0_15mins&orgId=1",
      "dashboardURL": "",
      "panelURL": "",
      "values": {
        "avg_previous_total_sub": 18,
        "delivery_report_success_current": 7,
        "delivery_report_success_previous": 2,
        "min_sub_check": 1,
        "no_data_check": 0,
        "percentage_difference": 3827.777777777778,
        "pre_dlr_percentage": 11.11111111111111,
        "today_dlr_percentage": 0.9900990099009901,
        "total_submissions_current": 707,
        "total_submissions_previous": 90,
        "trigger_alert": 1
      },
      "valueString": "[ var='avg_previous_total_sub' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=18 ], [ var='delivery_report_success_current' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=7 ], [ var='delivery_report_success_previous' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=2 ], [ var='min_sub_check' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=1 ], [ var='no_data_check' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=0 ], [ var='percentage_difference' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=3827.777777777778 ], [ var='pre_dlr_percentage' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=11.11111111111111 ], [ var='today_dlr_percentage' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=0.9900990099009901 ], [ var='total_submissions_current' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=707 ], [ var='total_submissions_previous' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=90 ], [ var='trigger_alert' labels={customer_name=NEXMO INC, destination=Hutchison Macau} value=1 ]"
    },
    {
      "status": "firing",
      "labels": {
        "alertname": "VLS_0_15mins",
        "contact_point": "webhook",
        "customer_name": "NEXMO INC",
        "destination": "Maxis Communication BHD Malaysia",
        "grafana_folder": "SMSHUB Alerts",
        "max_allowed_percentage_change": "1%",
        "meta_additional_type": "spike",
        "meta_alert_type": "Volume Drop or Spike",
        "meta_contact_point": "<EMAIL>",
        "name": "VLS_0_15mins"
      },
      "annotations": {},
      "startsAt": "2025-07-21T10:01:00+05:30",
      "endsAt": "0001-01-01T00:00:00Z",
      "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/da8aa438-a59d-48ec-bf5e-0d27948c9119/view?orgId=1",
      "fingerprint": "ce3ab38445d10e4d",
      "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVLS_0_15mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=destination%3DMaxis+Communication+BHD+Malaysia&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D1%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVLS_0_15mins&orgId=1",
      "dashboardURL": "",
      "panelURL": "",
      "values": {
        "avg_previous_total_sub": 9.8,
        "delivery_report_success_current": 4,
        "delivery_report_success_previous": 0,
        "min_sub_check": 1,
        "no_data_check": 0,
        "percentage_difference": 3206.1224489795914,
        "pre_dlr_percentage": 0,
        "today_dlr_percentage": 1.2345679012345678,
        "total_submissions_current": 324,
        "total_submissions_previous": 49,
        "trigger_alert": 1
      },
      "valueString": "[ var='avg_previous_total_sub' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=9.8 ], [ var='delivery_report_success_current' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=4 ], [ var='delivery_report_success_previous' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=0 ], [ var='min_sub_check' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=1 ], [ var='no_data_check' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=0 ], [ var='percentage_difference' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=3206.1224489795914 ], [ var='pre_dlr_percentage' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=0 ], [ var='today_dlr_percentage' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=1.2345679012345678 ], [ var='total_submissions_current' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=324 ], [ var='total_submissions_previous' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=49 ], [ var='trigger_alert' labels={customer_name=NEXMO INC, destination=Maxis Communication BHD Malaysia} value=1 ]"
    }
  ],
  "groupLabels": {
    "alertname": "VLS_0_15mins",
    "grafana_folder": "SMSHUB Alerts"
  },
  "commonLabels": {
    "alertname": "VLS_0_15mins",
    "contact_point": "webhook",
    "customer_name": "NEXMO INC",
    "grafana_folder": "SMSHUB Alerts",
    "max_allowed_percentage_change": "1%",
    "meta_additional_type": "spike",
    "meta_alert_type": "Volume Drop or Spike",
    "meta_contact_point": "<EMAIL>",
    "name": "VLS_0_15mins"
  },
  "commonAnnotations": {},
  "externalURL": "http://hub-release.openturf.dev:3000/grafana/",
  "version": "1",
  "groupKey": "{}:{alertname=\"VLS_0_15mins\", grafana_folder=\"SMSHUB Alerts\"}",
  "truncatedAlerts": 0,
  "orgId": 1,
  "title": "[FIRING:2] VLS_0_15mins SMSHUB Alerts (webhook NEXMO INC 1% spike Volume Drop <NAME_EMAIL> VLS_0_15mins)",
  "state": "alerting",
  "message": "**Firing**\n\nValue: avg_previous_total_sub=18, delivery_report_success_current=7, delivery_report_success_previous=2, min_sub_check=1, no_data_check=0, percentage_difference=3827.777777777778, pre_dlr_percentage=11.11111111111111, today_dlr_percentage=0.9900990099009901, total_submissions_current=707, total_submissions_previous=90, trigger_alert=1\nLabels:\n - alertname = VLS_0_15mins\n - contact_point = webhook\n - customer_name = NEXMO INC\n - destination = Hutchison Macau\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 1%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VLS_0_15mins\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/da8aa438-a59d-48ec-bf5e-0d27948c9119/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVLS_0_15mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=destination%3DHutchison+Macau&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D1%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVLS_0_15mins&orgId=1\n\nValue: avg_previous_total_sub=9.8, delivery_report_success_current=4, delivery_report_success_previous=0, min_sub_check=1, no_data_check=0, percentage_difference=3206.1224489795914, pre_dlr_percentage=0, today_dlr_percentage=1.2345679012345678, total_submissions_current=324, total_submissions_previous=49, trigger_alert=1\nLabels:\n - alertname = VLS_0_15mins\n - contact_point = webhook\n - customer_name = NEXMO INC\n - destination = Maxis Communication BHD Malaysia\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 1%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = VLS_0_15mins\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/da8aa438-a59d-48ec-bf5e-0d27948c9119/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVLS_0_15mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=destination%3DMaxis+Communication+BHD+Malaysia&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D1%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in&matcher=name%3DVLS_0_15mins&orgId=1\n"
}}


// grafanaWebhook(grafanaWebhookPayload)


module.exports = {
  grafanaWebhook,
  extractValuesFromValueString,
  getAdminGroupEmails,
  processAlertData,
  getNotificationType
};
