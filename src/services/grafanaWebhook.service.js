const lodash = require("lodash");
const moment = require("moment");
const { MailService } = require("./email.service");
const logger = require("../config/logger");
const { alertConfig } = require("../config/alertConfig");
const { models } = require("../models");
const { Op } = require("sequelize");
const { createAlertNotificationHistorys } = require("./alertsNotificationHistory.service");

const fs = require("fs");
const path = require("path");


// function extractValuesFromValueString(str) {
//   console.log("strig is ", str);
//   if (!str) {
//     return {};
//   }
//   // const regex = /var='([^']*)'\s+labels=\{([^\}]*)\}\s+value=([\d.]+)/;
//   const regex = /var='([^']*)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/g;




//   const arr = str.split(", ");

//   const finalObj = {};
//   arr.forEach((str) => {
//     // Match the string with the regex
//     const match = str.match(regex);

//     if (match) {
//       const variable = match[1]; // Extracted var
//       const labels = match[2]; // Extracted labels
//       const value = parseFloat(match[3]); // Extracted value, converted to a number
//       finalObj[variable] = { labels, value };
//     } else {
//       console.error("String format does not match the expected pattern.", str);

//       //   `[ var='percentage_difference' labels={customer_name=Wirelsc_C_P2P} value=-49.533554017454115 ]`;
//       const regex2 = /var='([^']+)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/;
//       const match2 = str.match(regex2);

//       if (match2) {
//         const variable = match2[1]; // Extracted var
//         const labels = match2[2]; // Extracted labels
//         const value = parseFloat(match2[3]); // Extracted value, converted to a number
//         finalObj[variable] = { labels, value };
//       } else {
//         console.error("String format does not match the expected 2nd pattern.");
//       }
//     }
//   });
//   return finalObj;
// }

function extractValuesFromValueString(str) {
  console.log("Input string:", str);

  if (!str) {
    return {};
  }

  const regex = /var='([^']*)'\s+labels=\{([^}]*)\}\s+value=([-\d.]+)/g;

  const finalObj = {};
  let match;

  while ((match = regex.exec(str)) !== null) {
    const variable = match[1]; // Extracted var
    const labelsString = match[2]; // Extracted labels
    const value = parseFloat(match[3]); // Extracted value

    // Convert labels into an object
    const labels = {};
    if (labelsString) {
      labelsString.split(", ").forEach((label) => {
        const [key, val] = label.split("=");
        if (key && val !== undefined) {
          labels[key.trim()] = val.trim();
        }
      });
    }

    finalObj[variable] = { labels, value };
  }

  if (Object.keys(finalObj).length === 0) {
    console.error("String format does not match the expected pattern.");
  }

  return finalObj;
}



let adminEmailCache;
async function getAdminGroupEmails() {
  if (adminEmailCache) {
    logger.info(`Admin emails cache is found: ${adminEmailCache}`);
    return adminEmailCache;
  }
  const STR_ADMIN_GROUP = "admin group";
  try {
    const whereClause = {
      name: {
        [Op.like]: `%${STR_ADMIN_GROUP}%`,
      },
    };
    const adminGroup = await models.Email_group.findOne({
      where: whereClause,
      raw: true,
      attributes: ["members"],
    });
    if (!adminGroup) {
      throw new Error(`Admin group not found. Name search: ${STR_ADMIN_GROUP}`);
    }
    adminEmailCache = adminGroup.members.join(",");
    logger.info(`Admin emails: ${adminEmailCache}`);
    return adminEmailCache;
  } catch (error) {
    console.log("error from getAdminGroupEmails", error);
    logger.error(error);
    return null;
  }
}

const savePayloadToFile = async (payload) => {
  const date = moment().format("DD-MM-YYYY"); // Format: 13-02-2025
  const directory = "grafana-payload"; // Directory name
  const fileName = `grafana_payload_${date}.json`;
  const filePath = path.join(directory, fileName); // Full path

  // Ensure the directory exists
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }

  // Convert the new payload to a JSON string with proper formatting
  const newEntry = JSON.stringify(payload, null, 2);

  // Append the new entry to the file with a separator for clarity
  fs.appendFile(filePath, `\n${newEntry},\n`, (err) => {
    if (err) {
      console.error("Error appending payload:", err);
    } else {
      console.log(`Payload appended successfully to ${filePath}`);
    }
  });
}


/**
 * Groups alerts by customer > supplier > destination
 * @param {Array} alerts - Array of alert objects
 * @returns {Array} - Array of { groupKey, groupValue, alerts }
 */
const groupAlerts = (alerts) => {
  const groupMap = {};
  alerts.forEach((alert) => {
    const labels = alert.labels || {};
    let groupKey = "Customer", groupValue = labels.customer_name && labels.customer_name.trim();
    if (!groupValue) {
      groupKey = "Supplier";
      groupValue = labels.supplier && labels.supplier.trim();
    }
    if (!groupValue) {
      groupKey = "Destination";
      groupValue = labels.destination && labels.destination.trim();
    }
    if (!groupValue) {
      groupKey = "Unknown";
      groupValue = "Unknown Group";
    }
    const mapKey = `${groupKey}:${groupValue}`;
    if (!groupMap[mapKey]) {
      groupMap[mapKey] = { groupKey, groupValue, alerts: [] };
    }
    groupMap[mapKey].alerts.push(alert);
  });
  return Object.values(groupMap);
};


const buildObjectFromAlert = (labels, extractedValues, meta_alert_type) => {
  // console.log("Input labels:", labels); 
  const customerName = labels.customer_name || "";
  const destinationName = labels.destination || "";
  const supplierName = labels.supplier || "";
  const node = labels.node || "";
  const result_code = labels.result_code || "";
  const errorDescription = labels.error_description || "";
  const volumeType = labels.volume_type || "";

  const TS = lodash.get(extractedValues, "today_total_sub.value", "");
  const TSP = lodash.get(extractedValues, "total_submissions_previous.value", "");
  const variation = lodash.get(extractedValues, "percentage_difference.value", "");

  // Helper function to format values based on type
  const formatValue = (value, { type = "number", decimals = 0, showPercent = false } = {}) => {
    if (value === null || value === undefined || value === '') {
      return showPercent ? "00.00%" : "00";
    }
    let num = Number(value);
    if (type === "percent") {
      const positiveNum = Math.abs(num); // ensure positive
      return (positiveNum < 10 ? `0${positiveNum.toFixed(2)}` : positiveNum.toFixed(2)) + "%";
    }

    if (type === "float") {
      return num.toFixed(decimals);
    }
    // Default: integer, with leading zero for single digit
    num = Math.round(num);
    return num < 10 && num >= 0 ? `0${num}` : `${num}`;
  };


  const tDLR = lodash.get(extractedValues, "today_dlr_percentage.value", "");
  const prevDLR = lodash.get(extractedValues, "pre_dlr_percentage.value", "");

  let dlrVariation = tDLR && prevDLR ? (tDLR - prevDLR) : "00";

  const delivery_failure_count_new_current = lodash.get(extractedValues, "delivery_failure_count_new_current.value", "");
  const delivery_failure_percentage_today = lodash.get(extractedValues, "delivery_failure_percentage_today.value", "");
  const total_submissions_current = lodash.get(extractedValues, "total_submissions_current.value", "");

  const delivery_failure_count_new_previous = lodash.get(extractedValues, "delivery_failure_count_new_previous.value", "");
  const delivery_success_previous = lodash.get(extractedValues, "delivery_success_previous.value", "");
  const delivery_success_today = lodash.get(extractedValues, "delivery_success_today.value", "");
  const total_submissions_previous = lodash.get(extractedValues, "total_submissions_previous.value", "");

  const avg_delivery_success_percentage_previous = lodash.get(extractedValues, "avg_delivery_success_percentage_previous.value", "");
  const delivery_success_percentage_difference = lodash.get(extractedValues, "delivery_success_percentage_difference.value", "");
  const delivery_success_percentage_today = lodash.get(extractedValues, "delivery_success_percentage_today.value", "");

  let obj;

  console.log("meta_alert_type:", meta_alert_type);
  console.log("total_submissions_current:", total_submissions_current);

  switch (meta_alert_type) {

    case alertConfig.types.VOLUME_DROP_OR_SPIKE:
      obj = {
        "Today's Submission": formatValue(total_submissions_current),
        "Yesterday's Submission": formatValue(TSP),
        "Volume Variation": formatValue(variation, { type: "percent" }),
        "Today's DLR": formatValue(tDLR, { type: "percent" }),
        "Yesterday's DLR": formatValue(prevDLR, { type: "percent" }),
        "DLR Variation": formatValue(dlrVariation, { type: "percent" }),
      };
      break;
    case alertConfig.types.ERROR:
      obj = {
        "Node": node,
        "Result Code": result_code,
        "Error Code": errorDescription,
        "Today's Submission": formatValue(total_submissions_current),
        "Error Percentage": formatValue(delivery_failure_percentage_today, { type: "percent" }),
        "Error Occured": formatValue(delivery_failure_count_new_current),
      };
      break;
    case alertConfig.types.DELIVERY_DROP:
      obj = {
        "Today's Submission": formatValue(total_submissions_current),
        "YesterDay's Submission": formatValue(delivery_success_previous),
        "Today Delivery Failure Count": formatValue(delivery_failure_count_new_current),
        "Today's DLR": formatValue(delivery_success_percentage_today, { type: "percent" }),
        "Yesterday's DLR": formatValue(avg_delivery_success_percentage_previous, { type: "percent" }),
        "DLR Variation": formatValue(delivery_success_percentage_difference, { type: "percent" })
      };
      break;
    case alertConfig.types.DELIVERY_REPORT_DROP:
      obj = {
        "Today's Submission": formatValue(total_submissions_current),
        "Yesterday's Submission": formatValue(delivery_success_previous),
        "Volume Variation": formatValue(total_submissions_current - delivery_success_previous),
        "Today's DLR": formatValue(tDLR, { type: "percent" }),
        "Yesterday's DLR": formatValue(prevDLR, { type: "percent" }),
        "DLR Variation": formatValue(dlrVariation, { type: "percent" }),

        // "Today Delivery Failure Count": Number(delivery_failure_count_new_current).toFixed(4),
        // "Yesterday Delivery Failure Count": Number(delivery_failure_count_new_previous).toFixed(4),
        // "Yesterday Delivery Success Count": Number(delivery_success_previous).toFixed(4),
        // "Today Delivery Success Count": delivery_success_today,
      };
      break;
    default:
      obj = {
        "Unknown Metric": "N/A",
      };
  }

  if (destinationName) {
    obj = { Destination: destinationName, ...obj }
  }

  if (supplierName) {
    obj = { Supplier: supplierName, ...obj }
  }

  return obj;
}

/**
 * Process alert data to create formatted data objects for email
 * @param {Array} alerts - Array of alert objects
 * @returns {Array} - Array of formatted data objects, now including customerName and supplier
 */
const processAlertData = (alerts, meta_alert_type) => {
  return alerts.map((alert) => {
    console.log("Processing alert:", alert.labels.customer_name, alert.labels.destination);
    const extractedValues = extractValuesFromValueString(alert.valueString);

    console.log("extractedValues:", extractedValues);

    const obj = buildObjectFromAlert(alert.labels, extractedValues, meta_alert_type);

    return obj;

  });
};


/**
 * Processes alerts for a Datasource Error to create a simple list of failing rules.
 * @param {Array} alerts - Array of alert objects from the group.
 * @returns {Array} - Array of objects with the failing rule name.
 */
const processDatasourceErrorData = (alerts) => {
  const uniqueRuleNames = new Set();
  const result = [];

  alerts.forEach(alert => {
    const ruleName = alert.labels.rulename || alert.labels.name || "Unknown Rule Name";
    if (!uniqueRuleNames.has(ruleName)) {
      uniqueRuleNames.add(ruleName);
      result.push({
        "Failing Alert Rule": ruleName
      });
    }
  });

  return result;
};

const processDatasourceNoData = (alerts) => {
  const uniqueRuleNames = new Set();
  const result = [];

  alerts.forEach(alert => {
    const ruleName = alert.labels.rulename || alert.labels.name || "Unknown Rule Name";
    if (!uniqueRuleNames.has(ruleName)) {
      uniqueRuleNames.add(ruleName);
      result.push({
        "No data Alert Rule": ruleName
      });
    }
  });

  return result;
};

/**
 * Processes alerts for an Ingestion Alert to extract summary and description.
 * @param {Array} alerts - Array of alert objects from the group.
 * @returns {Array} - Array of objects with the summary and description.
 */
const processIngestionAlert = (alerts) => {
  // For ingestion alerts, all alerts in the group share the same annotations.
  // We can just take the first one.
  const firstAlert = alerts[0] || {};
  const annotations = firstAlert.annotations || {};

  const summary = annotations.summary || "No summary provided.";
  // The description has newlines, which need to be preserved in HTML.
  // We will pass the raw description and handle formatting in the template if needed.
  const description = annotations.description || "No description provided.";

  return [{ // Return an array with a single object for the template to loop through
    "Summary": summary,
    "Description": description
  }];
};


/**
 * Determine notification type based on alert metadata and variation
 * @param {string} meta_alert_type - Alert type from metadata
 * @param {number|string} variation - Percentage variation value
 * @returns {string} - Notification type description
 */
const getNotificationType = (meta_alert_type, variation) => {
  const isSpike = variation > 0; // positive means spike

  switch (meta_alert_type) {
    case alertConfig.types.VOLUME_DROP_OR_SPIKE:
      return isSpike ? "Volume Spike" : "Volume Dip";
    case alertConfig.types.ERROR:
      return "Error";
    case alertConfig.types.DELIVERY_DROP:
      return "Delivery Drop";
    case alertConfig.types.DELIVERY_REPORT_DROP:
      return "Delivery Report Drop";
    default:
      return "Unknown Alert Type";
  }
};

const grafanaWebhook = async (req) => {
  const payload = req.body;

  // ✅ Save payload to file only in production
  if (process.env.NODE_ENV === 'production') {
    await savePayloadToFile(payload);
  }
  const { commonLabels = {}, alerts = [], title, message } = payload;

  const isDatasourceError = commonLabels.alertname === 'DatasourceError';
  const isDatasourceNoData = commonLabels.alertname === 'DatasourceNoData';
  const isIngestionAlert = commonLabels.alertname === 'SMSHUB Ingestion Alert';

  const { meta_alert_type } = commonLabels;

  let contactPoint = lodash.get(commonLabels, "meta_contact_point", "");

  if (!contactPoint) {
    const adminGroupEmails = await getAdminGroupEmails();
    contactPoint = adminGroupEmails;
  }

  // const startTime = lodash.get(alerts, '[0].startsAt', '');
  // const endTime = lodash.get(alerts, '[0].endsAt', '');

  logger.info(`Webhook logs:\n`);
  logger.info(message);

  let alertGroups;

  if (isDatasourceError) {
    // If it's a datasource error, create ONE single group for all alerts.
    // This bypasses the customer/supplier/destination logic completely.
    logger.info("Datasource error detected. Creating a single group.");
    alertGroups = [{
      groupKey: "System",
      groupValue: "Datasource Error",
      alerts: alerts // Put all alerts into this single group
    }];
  } else if (isDatasourceNoData) {
    // If it's a datasource no data, create ONE single group for all alerts.
    // This bypasses the customer/supplier/destination logic completely.
    logger.info("Datasource no data detected. Creating a single group.");
    alertGroups = [{
      groupKey: "System",
      groupValue: "Datasource No Data",
      alerts: alerts // Put all alerts into this single group
    }];
  } else if (isIngestionAlert) {
    // If it's an ingestion alert, create ONE single group for all alerts.
    // This bypasses the customer/supplier/destination logic completely.
    logger.info("Ingestion alert detected. Creating a single group.");
    alertGroups = [{
      groupKey: "Default",
      groupValue: "Ingestion Alert",
      alerts: alerts // Put all alerts into this single group
    }];
  } else {
    // Use your existing grouping logic for all other alert types.
    logger.info("Standard alert detected. Grouping by customer/supplier/destination.");
    alertGroups = groupAlerts(alerts);
  }

  logger.info(`Found ${alertGroups.length} alert groups`);

  // Send an email for each group
  const emailResults = [];

  for (const group of alertGroups) {
    const { groupKey, groupValue, alerts: groupAlertsArr } = group;
    logger.info(`Sending email for group: ${groupKey} - ${groupValue}`);

    let notificationType;
    let data;
    const volumeType = (groupAlertsArr[0]?.labels?.volumeType) || "";

    // ✅ DETERMINE IF THE EXTRA TEXT SHOULD BE SHOWN
    const showDestinationText = !isDatasourceError && isDatasourceNoData && isIngestionAlert && group.groupKey !== 'Destination';

    if (isDatasourceError) {
      notificationType = 'Datasource Error';
      // Use a dedicated, simple function for this case
      data = processDatasourceErrorData(groupAlertsArr);
    } else if (isDatasourceNoData) {
      notificationType = 'Datasource No Data';
      // Use a dedicated, simple function for this case
      data = processDatasourceNoData(groupAlertsArr);
    } else if (isIngestionAlert) {
      notificationType = 'Ingestion Alert';
      // Use a dedicated, simple function for this case
      data = processIngestionAlert(groupAlertsArr);
    } else {
      // Original logic for regular alerts
      const firstAlert = groupAlertsArr[0] || {};
      const firstAlertValues = firstAlert.values || {};
      const variation = firstAlertValues.percentage_difference;

      notificationType = getNotificationType(meta_alert_type, variation);
      console.log("notificationType->", notificationType);
      data = processAlertData(groupAlertsArr, meta_alert_type);
    }

    const alertname = (groupAlertsArr[0]?.labels?.alertname) || "Alert";
    const firstAlertInGroup = groupAlertsArr[0] || {};
    // const alertTriggerTime = firstAlertInGroup.startsAt; // This is the END of the window
    const alertTriggerTime = moment().format(); // This will give you current time in ISO format with timezone
    const groupEndTime = firstAlertInGroup.endsAt;
    const everyRunMinutes = parseInt(firstAlertInGroup.labels?.every_run_minutes || '0', 10);

    let formattedStartTime;
    let formattedEndTime;

    if (everyRunMinutes > 0) {
      // The time the alert triggers is the END of the period being evaluated.
      formattedEndTime = moment(alertTriggerTime).format("HH:mm");
      // The start time is that end time MINUS the duration.
      formattedStartTime = moment(alertTriggerTime).subtract(everyRunMinutes, 'minutes').format("HH:mm");
    } else {
      // Fallback to original logic if every_run_minutes_minutes is not used
      formattedStartTime = moment(alertTriggerTime).format("HH:mm");
      if (groupEndTime === "0001-01-01T00:00:00Z") {
        formattedEndTime = "In Progress";
      } else {
        formattedEndTime = moment(groupEndTime).format("DD-MMM HH:mm");
      }
    }

    let template;
    if (isDatasourceError) {
      template = "datasource_error.template";
    } else if (isDatasourceNoData) {
      template = "datasource_nodata.template";
    } else if (isIngestionAlert) {
      template = "ingestion_alert.template";
    } else {
      template = "volume_dip_notification";
    }

    let subject = `${alertname} - ${groupValue}`;
    if (isIngestionAlert && firstAlertInGroup.annotations?.summary) {
      subject = firstAlertInGroup.annotations.summary;
    }
    // Create email object for this group
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: contactPoint,
      subject,
      template,
      context: {
        groupKey,
        groupValue,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
        teamName: "SMSHUB Team",
        data, // 'data' now contains objects for all alerts in this group
        notificationType,
        showDestinationText
      },
      helpers: {
        keys: (obj) => Object.keys(obj),
        values: (obj) => Object.values(obj),
        length: (array) => (Array.isArray(array) ? array.length : 0),
        eq: (a, b) => a === b, // Checks if a is equal to b
        gt: (a, b) => a > b, // Checks if a is greater than b
      },
    };

    logger.info(`Email for ${groupValue}:`, emailObj);
    console.log("email  object is ", JSON.stringify(emailObj, null, 2));

    // Create AlertNotificationHistory entries in parallel for each alert in the group
    const notificationLogPromises = groupAlertsArr.map(async (alert) => {
      try {
        const notificationLogPayload = {
          alertId: alert.labels?.alert_id || 0, // Use alert_id from labels or default to 0
          name: alertname,
          description: alert.annotations?.description || alert.annotations?.summary || '',
          state: alert.status === 'firing' ? 'Alreting' : 'Resolved' || 'firing',
          startsAt: alert.startsAt,
          everyRunMinute: everyRunMinutes,
          intervalStart: formattedStartTime,
          intervalEnd: formattedEndTime,
          status: alertConfig.alertsHistoryStatus.OPEN, // Initial status is 'open'
          category: alert.labels?.category || 'minor', // Default to 'minor' if not specified
          alertType: meta_alert_type,
          notificationPayload: alert // Store the entire alert object as JSON
        };

        return await createAlertNotificationHistorys(notificationLogPayload);
      } catch (error) {
        logger.error(`Error creating notification log for alert:`, error);
        return null; // Continue with other alerts even if one fails
      }
    });

    // Execute notification log creation in parallel (don't wait for completion)
    Promise.allSettled(notificationLogPromises).then((results) => {
      const successful = results.filter(result => result.status === 'fulfilled' && result.value !== null).length;
      const failed = results.length - successful;
      logger.info(`Notification logs created: ${successful} successful, ${failed} failed for group ${groupValue}`);
    }).catch((error) => {
      logger.error(`Error in parallel notification log creation for group ${groupValue}:`, error);
    });

    try {
      const emailRes = await MailService(emailObj);
      emailResults.push({
        groupValue,
        result: emailRes
      });
    } catch (error) {
      logger.error(`Error sending email for ${groupValue}:`, error);
      emailResults.push({
        groupValue,
        error: error.message || 'Unknown error'
      });
    }
  }

  return {
    success: true,
    emailsSent: emailResults.length,
    results: emailResults
  };
};


const grafanaWebhookPayload = {

  body:{
  "receiver": "webhook",
  "status": "firing",
  "alerts": [
    {
      "status": "firing",
      "labels": {
        "alertname": "Vlm_Drop_30mins",
        "contact_point": "webhook",
        "customer_name": "NEXMO INC",
        "error_code": "undefined",
        "error_description": "undefined undefined",
        "every_run_minutes": "30",
        "grafana_folder": "SMSHUB Alerts",
        "max_allowed_percentage_change": "10%",
        "meta_additional_type": "spike",
        "meta_alert_type": "Volume Drop or Spike",
        "meta_contact_point": "<EMAIL>",
        "name": "Vlm_Drop_30mins",
        "node": "undefined",
        "result_code": "undefined",
        "ruleGroup": "every_30_min",
        "volumeType": "spike"
      },
      "annotations": {},
      "startsAt": "2025-07-31T16:00:00+05:30",
      "endsAt": "0001-01-01T00:00:00Z",
      "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/c694dddd-1b38-4735-bdd0-3f504e132267/view?orgId=1",
      "fingerprint": "1a7c3df958ed64ad",
      "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVlm_Drop_30mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=error_code%3Dundefined&matcher=error_description%3Dundefined+undefined&matcher=every_run_minutes%3D30&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Cmanovignesh.subramani%40openturf.in&matcher=name%3DVlm_Drop_30mins&matcher=node%3Dundefined&matcher=result_code%3Dundefined&matcher=ruleGroup%3Devery_30_min&matcher=volumeType%3Dspike&orgId=1",
      "dashboardURL": "",
      "panelURL": "",
      "values": {
        "avg_previous_total_sub": 4475.666666666667,
        "count_successful_delivery_final_current": 0,
        "count_successful_delivery_final_previous": 11271,
        "min_sub_check": 1,
        "no_data_check": 0,
        "percentage_difference": 177.2547851344306,
        "pre_dlr_percentage": 251.8284054517018,
        "today_dlr_percentage": 0,
        "today_total_sub": 12409,
        "total_submissions_previous": 13427,
        "trigger_alert": 1
      },
      "valueString": "[ var='avg_previous_total_sub' labels={customer_name=NEXMO INC} value=4475.666666666667 ], [ var='count_successful_delivery_final_current' labels={customer_name=NEXMO INC} value=0 ], [ var='count_successful_delivery_final_previous' labels={customer_name=NEXMO INC} value=11271 ], [ var='min_sub_check' labels={customer_name=NEXMO INC} value=1 ], [ var='no_data_check' labels={customer_name=NEXMO INC} value=0 ], [ var='percentage_difference' labels={customer_name=NEXMO INC} value=177.2547851344306 ], [ var='pre_dlr_percentage' labels={customer_name=NEXMO INC} value=251.8284054517018 ], [ var='today_dlr_percentage' labels={customer_name=NEXMO INC} value=0 ], [ var='today_total_sub' labels={customer_name=NEXMO INC} value=12409 ], [ var='total_submissions_previous' labels={customer_name=NEXMO INC} value=13427 ], [ var='trigger_alert' labels={customer_name=NEXMO INC} value=1 ]"
    },
    {
      "status": "firing",
      "labels": {
        "alertname": "Vlm_Drop_30mins",
        "contact_point": "webhook",
        "customer_name": "SAP France S.A.",
        "error_code": "undefined",
        "error_description": "undefined undefined",
        "every_run_minutes": "30",
        "grafana_folder": "SMSHUB Alerts",
        "max_allowed_percentage_change": "10%",
        "meta_additional_type": "spike",
        "meta_alert_type": "Volume Drop or Spike",
        "meta_contact_point": "<EMAIL>",
        "name": "Vlm_Drop_30mins",
        "node": "undefined",
        "result_code": "undefined",
        "ruleGroup": "every_30_min",
        "volumeType": "spike"
      },
      "annotations": {},
      "startsAt": "2025-07-31T16:00:00+05:30",
      "endsAt": "0001-01-01T00:00:00Z",
      "generatorURL": "http://hub-release.openturf.dev:3000/grafana/alerting/grafana/c694dddd-1b38-4735-bdd0-3f504e132267/view?orgId=1",
      "fingerprint": "be1a0b6a8de31b31",
      "silenceURL": "http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVlm_Drop_30mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DSAP+France+S.A.&matcher=error_code%3Dundefined&matcher=error_description%3Dundefined+undefined&matcher=every_run_minutes%3D30&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Cmanovignesh.subramani%40openturf.in&matcher=name%3DVlm_Drop_30mins&matcher=node%3Dundefined&matcher=result_code%3Dundefined&matcher=ruleGroup%3Devery_30_min&matcher=volumeType%3Dspike&orgId=1",
      "dashboardURL": "",
      "panelURL": "",
      "values": {
        "avg_previous_total_sub": 4414.666666666667,
        "count_successful_delivery_final_current": 0,
        "count_successful_delivery_final_previous": 11141,
        "min_sub_check": 1,
        "no_data_check": 0,
        "percentage_difference": 185.20839625490785,
        "pre_dlr_percentage": 252.3633343400785,
        "today_dlr_percentage": 0,
        "today_total_sub": 12591,
        "total_submissions_previous": 13244,
        "trigger_alert": 1
      },
      "valueString": "[ var='avg_previous_total_sub' labels={customer_name=SAP France S.A.} value=4414.666666666667 ], [ var='count_successful_delivery_final_current' labels={customer_name=SAP France S.A.} value=0 ], [ var='count_successful_delivery_final_previous' labels={customer_name=SAP France S.A.} value=11141 ], [ var='min_sub_check' labels={customer_name=SAP France S.A.} value=1 ], [ var='no_data_check' labels={customer_name=SAP France S.A.} value=0 ], [ var='percentage_difference' labels={customer_name=SAP France S.A.} value=185.20839625490785 ], [ var='pre_dlr_percentage' labels={customer_name=SAP France S.A.} value=252.3633343400785 ], [ var='today_dlr_percentage' labels={customer_name=SAP France S.A.} value=0 ], [ var='today_total_sub' labels={customer_name=SAP France S.A.} value=12591 ], [ var='total_submissions_previous' labels={customer_name=SAP France S.A.} value=13244 ], [ var='trigger_alert' labels={customer_name=SAP France S.A.} value=1 ]"
    }
  ],
  "groupLabels": {
    "alertname": "Vlm_Drop_30mins"
  },
  "commonLabels": {
    "alertname": "Vlm_Drop_30mins",
    "contact_point": "webhook",
    "error_code": "undefined",
    "error_description": "undefined undefined",
    "every_run_minutes": "30",
    "grafana_folder": "SMSHUB Alerts",
    "max_allowed_percentage_change": "10%",
    "meta_additional_type": "spike",
    "meta_alert_type": "Volume Drop or Spike",
    "meta_contact_point": "<EMAIL>",
    "name": "Vlm_Drop_30mins",
    "node": "undefined",
    "result_code": "undefined",
    "ruleGroup": "every_30_min",
    "volumeType": "spike"
  },
  "commonAnnotations": {},
  "externalURL": "http://hub-release.openturf.dev:3000/grafana/",
  "version": "1",
  "groupKey": "{alertstate!=\"Error\",alertstate!=\"NoData\",grafana_folder=\"SMSHUB Alerts\",state!=\"error\"}/{ruleGroup=\"every_30_min\"}:{alertname=\"Vlm_Drop_30mins\"}",
  "truncatedAlerts": 0,
  "orgId": 1,
  "title": "[FIRING:2] Vlm_Drop_30mins (webhook undefined undefined undefined 30 SMSHUB Alerts 10% spike Volume Drop <NAME_EMAIL> Vlm_Drop_30mins undefined undefined every_30_min spike)",
  "state": "alerting",
  "message": "**Firing**\n\nValue: avg_previous_total_sub=4475.666666666667, count_successful_delivery_final_current=0, count_successful_delivery_final_previous=11271, min_sub_check=1, no_data_check=0, percentage_difference=177.2547851344306, pre_dlr_percentage=251.8284054517018, today_dlr_percentage=0, today_total_sub=12409, total_submissions_previous=13427, trigger_alert=1\nLabels:\n - alertname = Vlm_Drop_30mins\n - contact_point = webhook\n - customer_name = NEXMO INC\n - error_code = undefined\n - error_description = undefined undefined\n - every_run_minutes = 30\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = Vlm_Drop_30mins\n - node = undefined\n - result_code = undefined\n - ruleGroup = every_30_min\n - volumeType = spike\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/c694dddd-1b38-4735-bdd0-3f504e132267/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVlm_Drop_30mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DNEXMO+INC&matcher=error_code%3Dundefined&matcher=error_description%3Dundefined+undefined&matcher=every_run_minutes%3D30&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Cmanovignesh.subramani%40openturf.in&matcher=name%3DVlm_Drop_30mins&matcher=node%3Dundefined&matcher=result_code%3Dundefined&matcher=ruleGroup%3Devery_30_min&matcher=volumeType%3Dspike&orgId=1\n\nValue: avg_previous_total_sub=4414.666666666667, count_successful_delivery_final_current=0, count_successful_delivery_final_previous=11141, min_sub_check=1, no_data_check=0, percentage_difference=185.20839625490785, pre_dlr_percentage=252.3633343400785, today_dlr_percentage=0, today_total_sub=12591, total_submissions_previous=13244, trigger_alert=1\nLabels:\n - alertname = Vlm_Drop_30mins\n - contact_point = webhook\n - customer_name = SAP France S.A.\n - error_code = undefined\n - error_description = undefined undefined\n - every_run_minutes = 30\n - grafana_folder = SMSHUB Alerts\n - max_allowed_percentage_change = 10%\n - meta_additional_type = spike\n - meta_alert_type = Volume Drop or Spike\n - meta_contact_point = <EMAIL>\n - name = Vlm_Drop_30mins\n - node = undefined\n - result_code = undefined\n - ruleGroup = every_30_min\n - volumeType = spike\nAnnotations:\nSource: http://hub-release.openturf.dev:3000/grafana/alerting/grafana/c694dddd-1b38-4735-bdd0-3f504e132267/view?orgId=1\nSilence: http://hub-release.openturf.dev:3000/grafana/alerting/silence/new?alertmanager=grafana&matcher=alertname%3DVlm_Drop_30mins&matcher=contact_point%3Dwebhook&matcher=customer_name%3DSAP+France+S.A.&matcher=error_code%3Dundefined&matcher=error_description%3Dundefined+undefined&matcher=every_run_minutes%3D30&matcher=grafana_folder%3DSMSHUB+Alerts&matcher=max_allowed_percentage_change%3D10%25&matcher=meta_additional_type%3Dspike&matcher=meta_alert_type%3DVolume+Drop+or+Spike&matcher=meta_contact_point%3Dnarasimmharaaj.parthasarathy%40openturf.in%2Ccaroline.synthia%40openturf.in%2Cbanupriya.muthaiya%40openturf.in%2Cmanovignesh.subramani%40openturf.in&matcher=name%3DVlm_Drop_30mins&matcher=node%3Dundefined&matcher=result_code%3Dundefined&matcher=ruleGroup%3Devery_30_min&matcher=volumeType%3Dspike&orgId=1\n"
},
}


// grafanaWebhook(grafanaWebhookPayload)


module.exports = {
  grafanaWebhook,
  extractValuesFromValueString,
  getAdminGroupEmails,
  processAlertData,
  buildObjectFromAlert,
  getNotificationType
};
