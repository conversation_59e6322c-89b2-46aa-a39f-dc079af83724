const { request, gql } = require("graphql-request");
const httpStatus = require("http-status");
const { graphqlServerUri, columnMapping } = require("../config/reportConfig");
const ApiError = require("../utils/ApiError");
const auditLogService = require("./audit-log.service");
const { auditLogEvents, defaultUserRole } = require("../config/roles");
const logger = require("../config/logger");
const { default: axios } = require("axios");
const config = require("../config/config");

const gqlOp = {
  query: "query",
  mutation: "mutation",
};

const getGraphqlData = async ({ name, payload, user = null }) => {
  try {
    const query = gql`
      query ${name} {${payload}}
    `;

   logger.info("GraphQL query:" + JSON.stringify(query, null, 2));

    // Call the GraphQL API
    const data = await request(graphqlServerUri, query);

    if (!data) {
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "No response received from GraphQL server"
      );
    }

    if (!data[name]) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Invalid response structure: missing expected data"
      );
    }

    logger.info("GraphQL response received successfully");


    let result = data[name];

    return result;
  } catch (error) {
    logger.error("Error in GraphQL request:", error);

    // Handle GraphQL specific errors
    if (error.response?.errors?.length > 0) {
      logger.error("GraphQL error:", error.response.errors[0].message);
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        error.response.errors[0].message
      );
    }

    // // Log audit for failed requests
    // const auditLogPayload = {
    //   username: user?.email || "-",
    //   userId: user?.id || "-",
    //   roleName: user?.role?.name || "-",
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading data: ${error.message}`,
    // };

    // try {
    //   await auditLogService.addAuditLog(auditLogPayload);
    // } catch (auditError) {
    //   logger.error("Failed to log audit:", auditError);
    // }

    // If it's already an ApiError, throw it directly
    if (error instanceof ApiError) {
      throw error;
    }

    // Throw generic error for unexpected cases
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Something went wrong. Please try again later"
    );
  }
};

const getGraphqlCustomData = async ({ apiUrl, payload, user = null }) => {
  try {
    logger.info("Custom report query:" + apiUrl + JSON.stringify(payload));

    // Call the custom report API
    const response = await axios.post(apiUrl, payload, {
      headers: { "Content-Type": "application/json" },
    });

    if (!response) {
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "No response received from server"
      );
    }

    if (response.status !== 200) {
      logger.error("Invalid response status:", response.status);
      throw new ApiError(
        response.status,
        response.statusText || "API call failed"
      );
    }

    logger.info("Response received from custom report");

    return response;
  } catch (error) {
    logger.error("Error in custom reports:", error);

    // Handle Axios errors with response
    if (error.response?.data) {
      logger.error("API error:", error.response.data);
      throw new ApiError(
        error.response.status || httpStatus.BAD_REQUEST,
        error.response.data.message || "API request failed"
      );
    }

    // Handle GraphQL specific errors
    if (error.response?.errors?.length > 0) {
      logger.error("GraphQL error:", error.response.errors[0].message);
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        error.response.errors[0].message
      );
    }

    // // Log audit for unexpected errors
    // const auditLogPayload = {
    //   username: user?.email || "-",
    //   userId: user?.id || "-",
    //   roleName: user?.role?.name || "-",
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading data: ${error.message}`,
    // };

    // await auditLogService.addAuditLog(auditLogPayload);

    // If it's already an ApiError, throw it directly
    if (error instanceof ApiError) {
      throw error;
    }

    // Throw generic error for unexpected cases
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Something went wrong. Please try again later"
    );
  }
};

const getAggregationFields = async (query, reportReqData, user = null) => {
  try {

    const { aggregationFieldMapping, responseFields } = reportReqData || {};


    const aggregationUri = `${config.dataServiceUrl}/api/v1/aggregationReport`;
    logger.info(
      "aggregation query is " + "\n" + JSON.stringify(query, null, 2)
    );

    // Call the aggregation API
    const response = await axios.post(aggregationUri, query, {
      headers: { "Content-Type": "application/json" },
    });

    if (!response) {
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "No response received from server"
      );
    }

    if (response.status !== 200) {
      logger.error("Aggregation API call failed");
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Aggregation API call failed"
      );
    }

    logger.info("Response received from aggregation service");

    if (
      !Array.isArray(response.data.items) ||
      response.data.items.length === 0
    ) {
      return []
    }


    const item = response.data?.items[0];
    
    if (item["sum_final_margin_euro"]) {
      item.final_margin_euro = item["sum_final_margin_euro"];
      delete item["sum_final_margin_euro"];
    }

    const renamedItem = {};
    Object.entries(item).forEach(([key, value]) => {
      const newKey =
        (aggregationFieldMapping?.fieldName && aggregationFieldMapping.fieldName[key]) ||
        (aggregationFieldMapping?.derivedFieldName &&
          aggregationFieldMapping.derivedFieldName[key]) ||
        key;
      renamedItem[newKey] = value;
    });

    let orderedItem = {};

    if (responseFields) {
      // Create an array of display names from responseFields in the defined order
      const responseFieldOrder = Object.values(responseFields);

      // Initialize orderedItem
      // Normalize and order the renamedItem based on the display names from responseFields
      responseFieldOrder.forEach(displayName => {
        const trimmedDisplayName = displayName.trim(); // Trim to avoid mismatches
        if (trimmedDisplayName in renamedItem) {
          orderedItem[trimmedDisplayName] = renamedItem[trimmedDisplayName];
          delete renamedItem[trimmedDisplayName];
        }
      });

      // Add remaining keys (not found in responseFieldOrder)
      Object.entries(renamedItem).forEach(([key, value]) => {
        orderedItem[key] = value;
      });

    } else {
      orderedItem = renamedItem;
    }

    return orderedItem;

  } catch (error) {
    logger.error("Error in getAggregationFields:", error);

    // Handle Axios errors
    if (error.response?.data) {
      logger.error("API error:", error.response.data);
      throw new ApiError(
        error.response.status || httpStatus.BAD_REQUEST,
        error.response.data.message || error.response.data.details || "API request failed"
      );
    }

    // // Log audit for unexpected errors
    // const auditLogPayload = {
    //   username: user?.email || "-",
    //   userId: user?.id || "-",
    //   roleName: user?.role?.name || "-",
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed processing aggregation data: ${error.message}`,
    // };

    // await auditLogService.addAuditLog(auditLogPayload);

    // If it's already an ApiError, throw it directly
    if (error instanceof ApiError) {
      throw error;
    }

    // Throw generic error for unexpected cases
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to process aggregation data"
    );
  }
};

const getWeekMonthyReport = async (query, user = null, isDownload = false) => {
  try {
    // Construct the query for weekly/monthly reports
    const weekMonthlyUri = `${config.dataServiceUrl}/api/v1/billingWeekMonthReport`;
    logger.info(
      "weekly monthly query is " + "\n" + JSON.stringify(query, null, 2)
    );

    // Call the weekly/monthly report API
    const response = await axios.post(weekMonthlyUri, query, {
      headers: { "Content-Type": "application/json" },
    });

    if (!response) {
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "No response received from server"
      );
    }

    if (response.status !== 200) {
      logger.error("Invalid response status:", response.status);
      throw new ApiError(
        response.status,
        response.statusText || "API call failed"
      );
    }
    logger.info("Response received from getWeekMonthyReport service");

    // For download, we return the response directly
    if (isDownload) {
      return response;
    }

    if (!response?.data) {
      return [];
    }

    return response.data;
  } catch (error) {
    logger.error("Error in getWeekMonthyReport:", error);

    // Handle Axios errors
    if (error.response?.data) {
      logger.error("API error:", error.response.data);
      throw new ApiError(
        error.response.status || httpStatus.BAD_REQUEST,
        error.response.data.message || "API request failed"
      );
    }

    // // Log audit for unexpected errors
    // const auditLogPayload = {
    //   username: user?.email || "-",
    //   userId: user?.id || "-",
    //   roleName: user?.role?.name || "-",
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed processing getWeekMonthyReport data: ${error.message}`,
    // };

    // await auditLogService.addAuditLog(auditLogPayload);

    // If it's already an ApiError, throw it directly
    if (error instanceof ApiError) {
      throw error;
    }

    // Throw generic error for unexpected cases
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to process getWeekMonthyReport data"
    );
  }
};

const graphqlRequestHandler = async (name, payload, operationType = gqlOp.query, user = null) => {
  const gqlOperation = gql`
    ${operationType} ${name} {
      ${payload}
    }
  `;

  logger.info("GraphQL operation is: " + gqlOperation);
  let response = null;

  await request(graphqlServerUri, gqlOperation)
    .then((data) => {
      response = data[name];
    })
    .catch((error) => {
      // console.log(error)
      const errMsg = error?.response?.errors?.[0]?.message || error.message;
      if(!errMsg) console.log(error)
      logger.error('Error from server: ' + errMsg);
      if (error.response && error.response.errors && error.response.errors.length > 0) {
        throw new ApiError(httpStatus.BAD_REQUEST, error.response.errors[0].message);
      } else {
        /*
        const auditLogPayload = {
          username: user ? user.email : '-',
          userId: user ? user.id : '-',
          roleName: user ? user.role.name : ' ',
          event: auditLogEvents.DOWNLOAD_FAILED,
          action: 'Failed downloading data',
        };
        auditLogService.addAuditLog(auditLogPayload);
        */
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          'Something went wrong. Please try again later.'
        );
      }
    });
  return response;
};

module.exports = {
  gqlOp,
  getGraphqlData,
  getGraphqlCustomData,
  getAggregationFields,
  getWeekMonthyReport,
  getGraphqlCustomData,
  getAggregationFields,
  getWeekMonthyReport,
  graphqlRequestHandler
};
