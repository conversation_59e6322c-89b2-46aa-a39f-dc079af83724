const { Sequelize, Op } = require("sequelize");
const dayjs = require("dayjs");
const { alertConfig } = require("../config/alertConfig");
const logger = require("../config/logger");
const { models } = require("../models");
const { getAlertHistoryById } = require("./alerts.service");
const { getAdminGroupEmails } = require("./grafanaWebhook.service");
const { MailService } = require("./email.service");
const { AlertsHistoryModel, AlertsModel } = models;

exports.fetchAlertsHistory = async (req) => {
  try {
    // Fetch active alerts
    const currentAlerts = await AlertsModel.findAll({
      where: { status: alertConfig.alertStatus.ACTIVE },
      raw: true,
      attributes: [
        "id",
        "category",
        [Sequelize.json("metadata.meta_alert_type"), "alert_type"],
        "name",
      ],
    });

    if (!currentAlerts) {
      logger.error("Error while fetching currentAlerts");
      return;
    }
    const currentAlertsObj = currentAlerts.reduce((acc, alert) => {
      acc[alert.id] = alert;
      return acc;
    }, {});
    const currentAlertsIds = currentAlerts.map((alert) => alert.id);

    logger.info(`Active alerts IDs: ${currentAlertsIds}`);

    if (currentAlertsIds.length === 0) {
      logger.info("No active alerts found.");
      return;
    }

    // Fetch alert histories in parallel
    const allAlertHistories = await Promise.all(
      currentAlertsIds.map(async (alertId) => {
        try {
          const history = await getAlertHistoryById({ params: { alertId } });
          if (history) {
            return history.map((entry) => ({
              alert_id: alertId,
              description: entry.description,
              state: entry.state,
              timestamp: entry.timestamp,
              category: currentAlertsObj[alertId].category,
              alert_type: currentAlertsObj[alertId].alert_type,
              name: currentAlertsObj[alertId].name,
              status: "open",
            }));
          } else {
            console.log(history);
            return [];
          }
        } catch (error) {
          logger.error(
            `Failed to fetch history for alert ID ${alertId}: ${error.message}`
          );
          return []; // Skip this alert's history if it fails
        }
      })
    );

    // Flatten the results into a single array
    const flattenedHistories = allAlertHistories.flat();

    if (flattenedHistories.length === 0) {
      logger.info("No alert histories to insert.");
      return;
    }

    // Fetch existing records to avoid duplicates
    const existingRecords = await AlertsHistoryModel.findAll({
      where: {
        [Sequelize.Op.or]: flattenedHistories.map(
          ({ alert_id, timestamp }) => ({
            alert_id,
            timestamp,
          })
        ),
      },
      raw: true,
      attributes: ["alert_id", "timestamp"],
    });

    const existingRecordsMap = new Set(
      existingRecords.map((record) => `${record.alert_id}-${record.timestamp}`)
    );

    // Filter out duplicates
    const uniqueHistories = flattenedHistories.filter(
      ({ alert_id, timestamp }) =>
        !existingRecordsMap.has(`${alert_id}-${timestamp}`)
    );

    if (uniqueHistories.length === 0) {
      logger.info("No new alert histories to insert.");
      return;
    }

    // Insert unique alert histories into the database
    await AlertsHistoryModel.bulkCreate(uniqueHistories);

    logger.info(
      `Successfully inserted ${uniqueHistories.length} new alert history records.`
    );
  } catch (error) {
    logger.error(`Error in getAlertsHistory: ${error.message}`);
    throw error; // Rethrow to ensure upstream handling
  }
};

exports.notifyUnsolvedAlert = async () => {
  const statuses = [
    alertConfig.alertsHistoryStatus.ANALYZING,
    alertConfig.alertsHistoryStatus.OPEN,
  ];

  // const [minorCount, majorCount, criticalCount] = await Promise.all([
  //   // Minor alerts: more than 2 days
  //   AlertsHistoryModel.count({
  //     where: {
  //       status: { [Op.in]: statuses },
  //       timestamp: { [Op.lte]: dayjs().subtract(2, "day").toDate() },
  //       category: alertConfig.categoryTypes.MINOR,
  //     },
  //   }),
  //   // Major alerts: more than 6 hours
  //   AlertsHistoryModel.count({
  //     where: {
  //       status: { [Op.in]: statuses },
  //       timestamp: { [Op.lte]: dayjs().subtract(6, "hour").toDate() },
  //       category: alertConfig.categoryTypes.MAJOR,
  //     },
  //   }),
  //   // Critical alerts: more than 8 hours
  //   AlertsHistoryModel.count({
  //     where: {
  //       status: { [Op.in]: statuses },
  //       timestamp: { [Op.lte]: dayjs().subtract(8, "hour").toDate() },
  //       category: alertConfig.categoryTypes.CRITICAL,
  //     },
  //   }),
  // ]);

  const alertCountPromises = Object.values(alertConfig.categoryTypes).map((category) => {
    const { duration, unit } = alertConfig.alertNotifyMailConfig[category];
    return AlertsHistoryModel.count({
      where: {
        status: { [Op.in]: statuses },
        timestamp: {
          [Op.lte]: dayjs().subtract(duration, unit).toDate(),
        },
        category,
      },
    });
  });

  const [minorCount, majorCount, criticalCount] = await Promise.all(alertCountPromises);


  if (!minorCount && !majorCount && !criticalCount) {
    logger.info(`No unsolved alerts found.`);
    return;
  }
  const result = { minorCount, majorCount, criticalCount };
  logger.info(`Unsolved Alerts: ${JSON.stringify(result)}`);

  const adminGroupEmails = await getAdminGroupEmails();

  const emailObj = {
    from: process.env.EMAIL_FROM,
    to: adminGroupEmails,
    subject: `[IMPORTANT] unsolved alerts notification`,
    template: "unsolved_alerts_notification",
    context: {
      minorCount,
      majorCount,
      criticalCount,
      currentYear: new Date().getFullYear(),
      companyName: "ComViva",
    },
  };
  logger.info(emailObj);
  await MailService(emailObj);
};

