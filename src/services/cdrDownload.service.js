const { adminUser } = require("../config/roles");
const { models } = require("../models");
const { convertToClientTime } = require("../utils/misc");
const sortBy = require("../utils/sortBy");
const { cdr_download } = models;
const { Op } = require("sequelize");

const createCdrDownload = async (data) => {
  try {
    const createdRecord = await cdr_download.create(data);
    return createdRecord;
  } catch (error) {
    console.error("Error creating CDR download record:", error);
    throw error;
  }
};

const getCdrDownloadData = async (filter, options, user) => {
  let limit = 10; // Default limit
  let offset = 0; // Default offset (for pagination)
  let page = 1; // Default page
  let sortByArray = [["createdAt", "DESC"]]; // Default sorting by createdAt DESC

  if (options) {
    limit = parseInt(options.limit, 10) || 10;
    page = parseInt(options.page, 10) || 1;
    offset = (page - 1) * limit;
  }

  if (options.sortBy) {
    sortByArray = [...sortByArray, ...sortBy(options.sortBy)];
  }

  // Handle search filtering
  if (filter.search) {
    filter = {
      ...filter,
      [Op.or]: [
        { fileName: { [Op.like]: `%${filter.search}%` } }, // Case-insensitive search
        { status: { [Op.like]: `%${filter.search}%` } },
        { initiatedBy: { [Op.like]: `%${filter.search}%` } },
        { createdAt: { [Op.like]: `%${filter.search}%` } },
        { id: { [Op.like]: `%${filter.search}%` } },
      ],
    };
    delete filter.search;
  }

  // Fetch filtered count for pagination
  const countPromise = cdr_download.count({
    where: filter, // Use the same filter for counting results
  });

  // Fetch filtered data with pagination
  const responsePromise = cdr_download.findAll({
    limit,
    offset,
    where: filter,
    order: sortByArray,
  });

  // Wait for both count and data
  const [totalResults, response] = await Promise.all([
    countPromise,
    responsePromise,
  ]);

  // Convert createdAt and updatedAt to client time
  for (let i = 0; i < response.length; i++) {
    response[i].dataValues.createdAt = await convertToClientTime(
      response[i].createdAt,
      user.timezone
    );
    response[i].dataValues.updatedAt = await convertToClientTime(
      response[i].updatedAt,
      user.timezone
    );
  }

  // Calculate total pages based on filtered count
  const totalPages = Math.ceil(totalResults / limit);

  // Return response along with pagination details
  return {
    data: response,
    pageNo: page,
    pageSize: limit,
    totalPages,
    totalCount: totalResults,
  };
};


const updateCdrDownload = async (id, data) => {
  return await cdr_download.update(data, { where: { id } });
};

const downloadCrdFile = async (id) => {
  const cdrData = await cdr_download.findOne({ where: { id } });
  // console.log("cdrData is ", cdrData);
  return cdrData;
};

const getDownloadById = async (id, user) => {
  const userId = user.id || {}
  const timezone = user.timezone || {}
  // console.log("id is ", id);
  const cdrData = await cdr_download.findOne({ where: { id } });

  if(user.name.toLowerCase() != adminUser.name.toLowerCase()) {
    if(cdrData.userId !== userId) {
      return null
    }
  }

  cdrData.dataValues.createdAt = await convertToClientTime(
    cdrData.createdAt,
    user.timezone
  );

  cdrData.dataValues.updatedAt = await convertToClientTime(
    cdrData.updatedAt,
    user.timezone
  );

  // console.log("cdrData is ", cdrData);
  return {data: cdrData};
};

const deleteCdrDownload = async (id) => {
  const cdrData = await cdr_download.delete({ where: { id } });
  // console.log("cdrData is ", cdrData);
  return cdrData;
};

module.exports = {
  createCdrDownload,
  getCdrDownloadData,
  getDownloadById,
  updateCdrDownload,
  downloadCrdFile,
  deleteCdrDownload,
};
