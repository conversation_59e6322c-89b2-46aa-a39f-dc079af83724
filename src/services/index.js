module.exports.authService = require('./auth.service');
module.exports.emailService = require('./email.service');
module.exports.tokenService = require('./token.service');
module.exports.userService = require('./user.service');
module.exports.roleService = require('./role.service');
module.exports.reportsService = require('./reports.service');
module.exports.staticReportsService = require('./reports.static.service');
module.exports.cdrReportsService = require('./reports.cdr.service');
module.exports.networkReportsService = require('./reports.network.service');
module.exports.auditLogService = require('./audit-log.service');
module.exports.alertsService = require('./alerts.service');
module.exports.cardsService = require('./cards.service');
module.exports.panelService = require('./panel.service');
module.exports.dashboardService = require('./dashboard.service');
module.exports.cdrDownloadService = require('./cdrDownload.service');
module.exports.grafanaWebhookService = require('./grafanaWebhook.service');
module.exports.offlineDownloadService = require('./offlineDownload.service');
module.exports.autoSaveService = require('./reports.autosave.service');