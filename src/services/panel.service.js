const httpStatus = require("http-status");
const { Op, ValidationError, where } = require("sequelize");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const {
  columnMapping,
  maxPageLimit,
  timestampParam,
  percentageFields,
  FieldsInMegaAPI,
  reportsMapping,
} = require("../config/reportConfig");
const graphqlService = require("./graphql.service");
const {
  getViewByValue,
  getCustomerSupplierFilter,
  convertToClientTime,
  formatReportDate,
  addCustomerBindClause,
} = require("../utils/misc");
const auditLogService = require("./audit-log.service");
const { auditLogEvents, adminRole } = require("../config/roles");
const config = require("../config/config");

const fs = require("fs");
const path = require("path");
const { reports_directory } = require("../config/config");

const logger = require("../config/logger");

const XLSX = require("xlsx");
const PDFDocument = require("pdfkit-table");

const dayjs = require("dayjs");
const {
  handleRegularFilters,
  handleOtherFilters,
  buildWhereClauseFromFilters,
} = require("../utils/build-advanced-filter");
const { Panel } = models;
const constructGrapghqlQuery = require("../utils/constructGrapghqlQuery");
const { maskNumber } = require("../utils/maskNumber");
const reportConfig = require("../config/reportConfig");
const globalConfig = require("../config/globalConfig");
const emailService = require("./email.service");
const { createZipBuffer, saveCsvOffline } = require("../utils/saveCsvOffline");
const constructExcel = require("../utils/generateExcelFile");
const constructPDF = require("../utils/generatePdf");
const constructCSV = require("../utils/generateCsv");
const { getTotalCount } = require("../utils/get-total-count");
const offlineDownloadService = require("./offlineDownload.service");
const { default: axios } = require("axios");
const roleConfig = require("../config/roles");
const { PaddingDownloadedRows, padding } = require("../utils/padding");
const streamExcelToZipBuffer = require("../utils/streamExcelToZipBuffer");
const streamPdfToZipBuffer = require("../utils/streamPdfToZipBuffer");
const streamCsvToZipBuffer = require("../utils/streamCsvToZipBuffer");
const findFilterFlag = require("../utils/findFilterFlag");
const getUserDetails = require("../utils/getUserDetails");

const createPanel = async (user, payload) => {
  let panel;
  try {
    /* if(payload.timePeriod && payload.timePeriod.includes('to'))
            {
                let timePeriod = payload.timePeriod.split('to');
                console.log("before ",timePeriod)
                timePeriod[0] = await convertToClientTime(dayjs.utc(timePeriod[0].trim()).toISOString(),user.timezone);
                timePeriod[1] = await convertToClientTime(dayjs.utc(timePeriod[1].trim()).toISOString(),user.timezone);
                console.log(timePeriod[0],timePeriod[1]);
                payload.timePeriod = timePeriod[0] + " to " + timePeriod[1];
     
            }*/

    if (!payload?.filterFlag) {
      const filterFlag = findFilterFlag(payload, user);
      payload.filterFlag = filterFlag;
    }

    panel = await Panel.create(payload);
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Panel name used before"
            );

          case "is_null":
          case "notEmpty":
            if (error.path == "name") {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                "Name cannot be empty"
              );
            }
          default:
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
      });
    } else {
      logger.error(`Error in creating panel : ${e}`);
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "Something went wrong! Pls try again after sometime"
      );
    }
  }
  return panel;
};

const getPanel = async (id) => {
  const panel = await Panel.findOne({ where: { id } });
  if (!panel) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Panel does not exists");
  }
  return panel;
};

const getPanelByName = async (name) => {
  const panel = await Panel.findOne({ where: { name } });
  if (!panel) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Panel does not exists");
  }
  return panel;
};

const getPanelByNames = async (names, user) => {
  const panels = await Panel.findAll({
    where: {
      name: {
        [Op.in]: names,
      },
      dynamicReport: 1,
      createdBy: user.id,
    },
    order: [["createdAt", "DESC"]],
    raw: true,
  });

  return panels;
};

const getDynamicPanels = async (user) => {
  const panels = await Panel.findAll({
    where: { createdBy: user.id, dynamicReport: 1 },
    order: [["createdAt", "DESC"]],
    raw: true,
  });
  return panels;
};

const getAdminDynamicPanels = async (user) => {
  const userService = require("./user.service");

  const adminUser = await userService.getUserByEmail(
    roleConfig.adminUser.email
  );
  const panels = await Panel.findAll({
    where: { dynamicReport: 1, createdBy: adminUser.id },
    raw: true,
  });
  return panels;
};

const getPanels = async (user, filter, options) => {
  let createdBy, logs;
  if (user.isSuperAdmin && filter?.createdBy) {
    createdBy = filter.createdBy;
  } else {
    createdBy = user.id;
  }
  delete filter?.createdBy;

  let limit, offset, page, responseTobeSent;
  if (options) {
    limit = Number(options.limit) || 100;
    page = Number(options.page) || 1;
    offset = (page - 1) * limit;
  }
  /*    if (createdByFilter == "others") {
              let dashboardsShared = user.role.dataValues.dynamicDashboard,
                  panelsShared = [];
              if (dashboardsShared) {
   
                  for (let i = 0; i < dashboardsShared.count; i++) {
                      try {
                          let dd = await dashboardService.getDashboard(dashboardsShared[i])
                          if (dd.panels) {
                              panelsShared.concat(dd.panels)
                          }
                      }
                      catch (err) {
                          console.log("dashboard not found ", err);
                      }
                  }
              }
              responseTobeSent = {
                  totalCount: panelsShared.count,
                  pageNo: page,
                  pageSize: limit,
                  data: panelsShared.slice(offset, offset + limit)
              }
          }
          else {
  */

  if (Object.keys(filter).length == 0) {
    logs = await Panel.findAndCountAll({
      limit,
      offset,
      where: { createdBy },
      order: [["createdAt", "DESC"]],
    });
  } else if (filter.type && filter.search) {
    logs = await Panel.findAndCountAll({
      limit,
      offset,
      where: {
        [Op.and]: {
          name: { [Op.like]: `%${filter.search}%` },
          visualizationType: { [Op.like]: `%${filter.type}%` },
        },
        createdBy,
      },

      order: [["createdAt", "DESC"]],
    });
  } else if (filter.type) {
    logs = await Panel.findAndCountAll({
      limit,
      offset,
      where: {
        visualizationType: { [Op.like]: `%${filter.type}%` },
        createdBy,
      },

      order: [["createdAt", "DESC"]],
    });
  } else if (filter.search) {
    let searchQuery = filter.search;
    // filter = "{[Op.or]: [name: { [Op.like]: '%' " + searchQuery + "'%' },description: { [Op.like]: '%' + searchQuery2 + '%' }]}"
    logs = await Panel.findAndCountAll({
      limit,
      offset,
      where: {
        [Op.or]: {
          name: { [Op.like]: `%${searchQuery}%` },
        },
        createdBy,
      },
      order: [["createdAt", "DESC"]],
    });
  }
  for (let i = 0; i < logs.rows.length; i++) {
    logs.rows[i].dataValues.createdAt = await convertToClientTime(
      logs.rows[i].createdAt,
      user.timezone
    );
    logs.rows[i].dataValues.updatedAt = await convertToClientTime(
      logs.rows[i].updatedAt,
      user.timezone
    );
  }
  responseTobeSent = {
    totalCount: logs.count,
    pageNo: page,
    pageSize: limit,
    data: logs.rows,
  };
  // }
  return responseTobeSent;
};

const updatePanel = async (user, panelId, payload) => {
  let panel = await getPanel(panelId);
  try {
    /*   if(payload.timePeriod && payload.timePeriod.includes('to'))
            {
                let timePeriod = payload.timePeriod.split('to');
                timePeriod[0] = await convertToClientTime(dayjs.utc(timePeriod[0]).toISOString(),user.timezone);
                timePeriod[1] = await convertToClientTime(dayjs.utc(timePeriod[1]).toISOString(),user.timezone);
                payload.timePeriod = timePeriod[0] + " to " + timePeriod[1];
     
            }
    */

    if (!payload?.filterFlag) {
      const filterFlag = findFilterFlag(payload, user);
      payload.filterFlag = filterFlag;
    }

    Object.assign(panel, payload);
    await panel.save();
    return panel;
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Panel name used before"
            );
          default:
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
      });
    } else {
      logger.error(`Error in creating panel : ${e}`);
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "Something went wrong! Pls try again after sometime"
      );
    }
  }
};

const deletePanel = async (id) => {
  let panel = await getPanel(id);
  await Panel.destroy({ where: { id } });
  return panel;
};

const getAggregationData = async (
  payload,
  reportReqData,
  whereClause,
  fields,
  viewBy,
  user,
  dataFromMegaAPI
) => {
  let aggregationFields = {};
  let derivedFields = {};
  let aggregateResponse = "";

  // Get aggregation fields from reportReqData and filter based on payload.dataColumns.derivedFields
  Object.entries(reportReqData.aggregationFieldMapping.fieldName).forEach(
    ([key, label]) => {
      if (fields.includes(key)) {
        aggregationFields[key] = label;
      }
    }
  );

  // Get aggregation derived fields from reportReqData and filter based on payload.dataColumns.derivedFields
  Object.entries(
    reportReqData.aggregationFieldMapping.derivedFieldName
  ).forEach(([key, label]) => {
    if (fields.includes(key)) {
      derivedFields[key] = label;
    }
  });

  delete derivedFields?.negative_report;
  // Build dynamic Report Data
  const dynamicReportData = {
    ...reportReqData,
    aggregationFieldMapping: {
      name: dataFromMegaAPI ? "sms_cdr" : "dashboard_chart_data",
      fieldName: aggregationFields,
      derivedFieldName: derivedFields,
    },
  };

  if (payload?.reportName) {
    const aggregateQuery = constructGrapghqlQuery.getAggregationQuery({
      payload,
      whereClause,
      viewBy,
      user,
      reportReqData: dynamicReportData,
    });

    aggregateResponse = await graphqlService.getAggregationFields(
      aggregateQuery,
      dynamicReportData
    );
  }

  return aggregateResponse;
};

const handleOfflineDownload = async ({
  payload,
  user,
  fields,
  headers,
  viewBy,
  whereClause,
  totalCount,
  dataFromMegaAPI,
  aggregateResponse,
  res,
}) => {
  let offlineDataRes = null;

  try {
    if (payload.type != "CSV") {
      res.status(httpStatus.BAD_REQUEST).send({
        message: "Only CSV download is supported for offline download",
      });
      return;
    }
    const fileName =
      payload.fileName ||
      `${payload.name}_${payload.startDate}_${payload.endDate}`;

    const filePath = path.resolve(
      config.offline_downloads.download_directory,
      `${fileName}.csv`
    );

    // Create an entry for the download
    const offlineFileData = {
      userId: user.id,
      initiatedBy: user.name,
      fileName: fileName,
      reportName: "Dynamic Report",
      category: config.offline_downloads.category.dynamic,
      filePath: filePath,
      status: config.offline_downloads.status.INITIATED,
    };
    offlineDataRes = await offlineDownloadService.createOfflineDownload(
      offlineFileData
    );
    // Send response immediately
    res.send({
      isOfflineDownload: true,
      message: config.offline_downloads.message,
    });
    logger.info("response sent", config.offline_downloads.message);

    const pageSize = config.NO_OF_DATA_PER_QUERY;
    const totalPages = Math.ceil(totalCount / pageSize);

    const payloadArray = [];
    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > pageSize) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        payloadArray.push({
          ...payload,
          page: pageNumber,
          limit: Number(pageSize),
          download: 1,
        });
      }
    } else {
      // If the total count is within the limit, add the query to the payloads
      payloadArray.push({
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      });
    }

    for (let i = 0; i < payloadArray.length; i++) {
      const payload = payloadArray[i];

      let query = panelQueryBuilder({
        payload,
        whereClause,
        fields,
        viewBy,
        user,
        dataFromMegaAPI,
      });

      const name = dataFromMegaAPI ? "getSmsCdr" : "getDashboardChartData";

      let response = await graphqlService.getGraphqlData({
        name,
        payload: query,
      });
      let data = response.items;

      data = await changeDateFormate(data, viewBy);

      data = padding(data);

      //Masking the number
      if (!user.isSuperAdmin && user.reportMasking && data) {
        data = maskNumber(data);
      }

      const rows = await formDataForDownload(data);

      const file = await constructCSV(
        headers,
        rows,
        payload,
        aggregateResponse
      );

      await saveCsvOffline(file, offlineDataRes, i === payloadArray.length - 1);
    }
    return null;
  } catch (error) {
    logger.error(`Error in handleOfflineDownload: ${error.message}`);
    if (offlineDataRes) {
      await offlineDownloadService.updateOfflineDownload(offlineDataRes.id, {
        status: config.offline_downloads.status.FAILED,
      });

      // // Update audit log for failed download
      // await auditLogService.addAuditLog({
      //     username: user.name,
      //     userId: user.id,
      //     roleName: user.role.name,
      //     event: auditLogEvents.DOWNLOAD_FAILED,
      //     action: `Failed downloading ${error.message}`,
      // });
    }
    return null;
  }
};

const handleOnlineDownload = async ({
  payload,
  user,
  fields,
  headers,
  viewBy,
  whereClause,
  totalCount,
  dataFromMegaAPI,
  aggregateResponse,
}) => {
  console.log(
    "Initiating online download as total count is less than threshold"
  );

  payload = {
    ...payload,
    page: 1,
    limit: config.offline_downloads.initiate_offline_download_rows,
  };

  let query = panelQueryBuilder({
    payload,
    whereClause,
    fields,
    viewBy,
    user,
    dataFromMegaAPI,
  });

  const name = dataFromMegaAPI ? "getSmsCdr" : "getDashboardChartData";

  let response = await graphqlService.getGraphqlData({ name, payload: query });
  let data = response.items;

  data = await changeDateFormate(data, viewBy);

  data = padding(data);

  //Masking the number
  if (!user.isSuperAdmin && user.reportMasking && data) {
    data = maskNumber(data);
  }

  const rows = await formDataForDownload(data);

  switch (payload.type) {
    case "EXCEL":
      // return constructExcel(headers, rows, payload, aggregateResponse);
      return streamExcelToZipBuffer(headers, rows, payload, aggregateResponse);
    case "PDF":
      // return await constructPDF(headers, rows, payload, aggregateResponse);
      return await streamPdfToZipBuffer(
        headers,
        rows,
        payload,
        aggregateResponse
      );
    default:
      // return await constructCSV(headers, rows, payload, aggregateResponse);
      return await streamCsvToZipBuffer(
        headers,
        rows,
        payload,
        aggregateResponse
      );
  }
};

const handleDownload = async ({
  payload,
  user,
  fields,
  headers,
  viewBy,
  whereClause,
  totalCount,
  dataFromMegaAPI,
  aggregateResponse,
  res,
}) => {
  if (
    Number(totalCount) > config.offline_downloads.initiate_offline_download_rows
  ) {
    logger.info(
      "Initiating offline download as total count is greater than threshold"
    );
    await handleOfflineDownload({
      payload,
      user,
      fields,
      headers,
      viewBy,
      whereClause,
      totalCount,
      dataFromMegaAPI,
      aggregateResponse,
      res,
    });
    return null;
  }
  const file = await handleOnlineDownload({
    payload,
    user,
    fields,
    headers,
    viewBy,
    whereClause,
    totalCount,
    dataFromMegaAPI,
    aggregateResponse,
  });

  res.setHeader("Content-Type", "application/zip");
  res.setHeader("Content-Disposition", `attachment; filename=${file.filename}`);
  res.send(file.buf);

  // const compressedFile = await createZipBuffer([file]);

  // Send the zip buffer as a response
  // console.log("zip buffer received")
  // res.setHeader("Content-Type", "application/zip");
  // res.setHeader("Content-Disposition", `attachment; filename=${file.filename}.zip`);
  // res.send(compressedFile);
  // return compressedFile;
};

const handleSendEmail = async ({
  payload,
  user,
  fields,
  headers,
  viewBy,
  whereClause,
  totalCount,
  dataFromMegaAPI,
  aggregateResponse,
}) => {

  if (!payload.mailList || payload.mailList.length == 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Minimum 1 email ID required");
  }

  // If the report total count is greater than the configured limit, throw an error
  if (Number(totalCount) > config.NO_OF_ROWS_PER_FILE) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Maximum response size for send email is reached `
    );
  }

  const file = await handleOnlineDownload({
    payload,
    user,
    fields,
    headers,
    viewBy,
    whereClause,
    totalCount,
    dataFromMegaAPI,
    aggregateResponse,
  });

  // const compressedFile = await createZipBuffer([file]);

  const fileSizeInMB = (Buffer.byteLength(file.buf) / (1024 * 1024)).toFixed(2);
  if (fileSizeInMB > globalConfig.MAX_EMAIL_ATTACHMENT_SIZE) {
    throw new ApiError(413, "File size too large");
  }

  const emailObj = {
    from: process.env.EMAIL_FROM,
    to: payload.mailList.toString(),
    subject: payload.name,
    template: "email_report.template",
    context: {
      REPORT_NAME: payload.reportName,
      STARTDATE: file.startDate,
      ENDDATE: file.endDate,
    },
    attachments: {
      // encoded string as an attachment
      filename: file.filename,
      content: file.buf,
      encoding: "base64",
    },
  };
  return await emailService.MailService(emailObj);
};

const handleAutoSave = async ({
  payload,
  user,
  fields,
  headers,
  viewBy,
  whereClause,
  totalCount,
  dataFromMegaAPI,
  aggregateResponse,
}) => {
  if (user.role.name !== adminRole.name) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Only super admin can save table report");
  }

  payload.type = "CSV";

  const totalCountToFetch =
    config.DATA_LIMIT_FOR_GRAPGH > 0 && config.DATA_LIMIT_FOR_GRAPGH < totalCount
      ? config.DATA_LIMIT_FOR_GRAPGH
      : totalCount;

  if (totalCountToFetch !== totalCount) {
    logger.info(`Total count ${totalCount} is greater than the limit. Fetching only ${totalCountToFetch} rows`);
  }

  const pageSize = config.NO_OF_DATA_PER_QUERY;
  const totalPages = Math.ceil(totalCountToFetch / pageSize);

  const payloadArray = [];
  for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
    payloadArray.push({
      ...payload,
      page: pageNumber,
      limit: Number(pageSize),
    });
  }

  console.log("payloadArray->", payloadArray)

  const startDate = dayjs(payload.startDate.trim()).format("YYYY-MM-DD");
  const endDate = dayjs(payload.endDate.trim()).format("YYYY-MM-DD");

  const fileName = `${payload.name}_${startDate}_${endDate}.csv`;
  const filePath = path.join(reports_directory, fileName);

  if (!fs.existsSync(reports_directory)) {
    fs.mkdirSync(reports_directory, { recursive: true });
  }

  const writeStream = fs.createWriteStream(filePath, { flags: "w" });

  try {
    for (let i = 0; i < payloadArray.length; i++) {
      const currPayload = payloadArray[i];

      const query = panelQueryBuilder({
        payload: currPayload,
        whereClause,
        fields,
        viewBy,
        user,
        dataFromMegaAPI,
      });

      const name = dataFromMegaAPI ? "getSmsCdr" : "getDashboardChartData";

      const response = await graphqlService.getGraphqlData({ name, payload: query });
      let data = await changeDateFormate(response.items, viewBy);
      // let data = responseData.data

      console.log("data->", data)

      data = padding(data);

      if (!user.isSuperAdmin && user.reportMasking && data?.data) {
        data = maskNumber(data);
      }

      const rows = await formDataForDownload(data);

      const saveData = await constructCSV(
        headers,
        rows,
        payload,
        aggregateResponse,
        { includeHeaders: i === 0 } // Only include headers for the first page
      );

      writeStream.write(saveData.buf);
    }

    writeStream.end();

    console.log(`File saved to ${filePath}`);
    return "Table report saved successfully";
  } catch (error) {
    writeStream.destroy(); // close the stream on error
    if (error.code === "ENOSPC") {
      console.error("Error: Storage is full. Unable to save the file.");
      throw new ApiError(
        httpStatus.INSUFFICIENT_STORAGE,
        "Storage is full. Please free up space and try again."
      );
    }
    console.error("File saving failed:", error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, "Error saving the table report.");
  }
};


const buildWhereClause = async ({
  payload,
  reportReqData,
  fields,
  user,
  dataFromMegaAPI,
  filterFlag,
}) => {
  let whereClauseParts = [];

  // Add report filters
  if (payload.reportFilters && Object.keys(payload.reportFilters).length > 0) {
    const reportFilterClause = await buildReportWhereClause(
      payload,
      reportReqData,
      user
    );
    if (reportFilterClause) whereClauseParts.push(reportFilterClause);
  } else {
    // Add regular filters (from UI, payload etc.)
    const regularClause = await buildWhereClauseFromFilters({
      filters: payload.filters || [],
    });
    if (regularClause) whereClauseParts.push(regularClause);
  }
  // Build search filter for whereClause
  if (payload.search) {
    let searchClause = "";
    const aggregateFields = Object.keys(
      reportReqData.aggregationFieldMapping.fieldName
    );

    // Only include non-aggregate fields for WHERE clause
    // const searchFields = fields.filter(field =>
    //     field !== 'timestamp' && !aggregateFields.includes(field)
    // );

    // Step 1: Map frontend labels to backend fields
    const backendFieldCandidates = payload.dataColumns.tableFields
      .map((field) => columnMapping[field])
      .filter(Boolean); // Remove null or undefined values

    // Step 2: Filter out those that actually exist in searchFields
    const searchFields = reportReqData.searchFields.filter((field) =>
      backendFieldCandidates.includes(field)
    );

    // if(!user.isSuperAdmin) {
    //     if(user.type === roleConfig.userTypes.CUSTOMER) {
    //         searchFields = reportReqData.searchFields
    //     }

    //     if(user.type === roleConfig.userTypes.SUPPLIER) {}
    // }

    for (let k = 0; k < searchFields.length; k++) {
      if (searchClause) {
        searchClause = `${searchClause} OR `;
      }
      searchClause = `${searchClause}{${searchFields[k]}} ILIKE '%${payload.search}%' `;
    }

    if (searchClause) {
      whereClauseParts.push(searchClause);
    }
  }
  let userDetails = user;

  let userFilters = null;
  if (!userDetails.dataValues.isSuperAdmin) {
    userFilters = await getCustomerSupplierFilter({
      user: userDetails,
      reportFields: fields,
      megaAPI: dataFromMegaAPI,
      filterFlag,
    });
  }

  if (userFilters) whereClauseParts.push(userFilters);

  // Add customer bind clause
  const bindClause = await addCustomerBindClause(
    userDetails,
    fields,
    dataFromMegaAPI
  );
  if (bindClause) whereClauseParts.push(bindClause);

  // Combine all clauses with AND
  const whereClause = whereClauseParts.map((part) => `(${part})`).join(" and ");

  return whereClause;
};

const getPanelData = async (user, payload, res) => {
  try {
    const reportReqData = reportsMapping["Dynamic Report"];

    // For user analysis use user based filters
    if (payload.userAnalysis && payload.userId) {
      user = await getUserDetails(payload.userId);
    }

    if (!user.timezone) user.timezone = config.DEFAULT_TIMEZONE;

    if (
      (!user.isSuperAdmin || payload.userAnalysis) &&
      Object.keys(payload?.reportFilters || {}).length === 0
    ) {

      const supplierFilters = payload.filters
        .filter((filter) => filter.field === "Supplier Name")
        .flatMap((filter) =>
          Array.isArray(filter.value) ? filter.value : [filter.value]
        );

      const customerFilters = payload.filters
        .filter((filter) => filter.field === "Customer Name")
        .flatMap((filter) =>
          Array.isArray(filter.value) ? filter.value : [filter.value]
        );


      const userSupplierNames = user.supplierList.map((s) => s.name);
      const userCustomerNames = user.customerList.map((c) => c.name);

      const hasValidSupplier =
        supplierFilters.length === 0 || // No filter = allow all
        supplierFilters.some((supplier) =>
          userSupplierNames.includes(supplier)
        );

      const hasValidCustomer =
        customerFilters.length === 0 || // No filter = allow all
        customerFilters.some((customer) =>
          userCustomerNames.includes(customer)
        );

      if (!hasValidSupplier || !hasValidCustomer) {
        if (payload.userAnalysis) {
          throw new Error(
            "This user does not have permission to view this panel."
          );
        } else {
          throw new Error("You do not have permission to view this panel.");
        }
      }
    }

    payload.page = Number(payload.page) || 1;

    const filterFlag = findFilterFlag(payload, user);

    const startDate = dayjs().format("YYYY-MM-DD 00:00:00");
    const endDate = dayjs().format("YYYY-MM-DD HH:mm:ss");

    payload.startDate = payload.startDate?.trim() || startDate;
    payload.endDate = payload.endDate?.trim() || endDate;

    let viewBy = payload.interval;
    if (!viewBy)
      viewBy = await getViewByValue(payload.startDate, payload.endDate, true);

    // For Table report support pagination
    if (
      payload.visualizationType === "Table Report" &&
      !(payload.download || payload.sendEmail || payload.save)
    ) {
      // if (!payload.limit)
      payload.limit = 100;
    } else {
      payload.limit = maxPageLimit;
    }

    //get all fields to be fetched
    const origFieldList = [];
    const fields = [];
    const multiAxisHeaders = [];
    if (!payload.dataColumns) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Data Column is required");
    }
    if (payload.timezone) user.timezone = payload.timezone;

    switch (payload.visualizationType) {
      case "Line Graph":
        columns = payload.dataColumns.derivedFields;
        for (let i = 0; i < columns.length; i++) {
          if (columnMapping[columns[i]] != null) {
            fields.push(columnMapping[columns[i]]);
            origFieldList.push(columns[i]);
          }
        }
        if (!fields.includes("timestamp")) {
          fields.push("timestamp");
          origFieldList.push("Date");
        }
        break;
      case "Bar Graph":
        columns = payload.dataColumns.derivedFields;
        columns = columns.concat(payload.dataColumns["X-Axis"]);
        for (let i = 0; i < columns.length; i++) {
          if (columnMapping[columns[i]]) {
            fields.push(columnMapping[columns[i]]);
            origFieldList.push(columns[i]);
          }
        }
        if (payload.dataColumns.noOfRecords) {
          pageSize = payload.dataColumns.noOfRecords;
        }
        break;
      case "Pie Chart":
        columns = payload.dataColumns.derivedFields;
        for (let i = 0; i < columns.length; i++) {
          if (columnMapping[columns[i]]) {
            fields.push(columnMapping[columns[i]]);
            origFieldList.push(columns[i]);
          }
        }
        break;
      case "Table Report":
        columns = payload.dataColumns.tableFields || [];
        columns = columns.concat(payload.dataColumns.derivedFields);
        // columns = payload.dataColumns.derivedFields;
        for (let i = 0; i < columns.length; i++) {
          if (columnMapping[columns[i]]) {
            fields.push(columnMapping[columns[i]]);
            origFieldList.push(columns[i]);
          }
        }
        if (!fields.includes("timestamp")) {
          fields.unshift("timestamp");
          origFieldList.unshift("DateTime");
        }
        break;
      case "MultiAxis Graph":
        columns = payload.dataColumns.derivedFields;
        for (let i = 0; i < columns.length; i++) {
          let multiColumns = columns[i].name.split("and");

          for (let j = 0; j < multiColumns.length; j++) {
            multiColumns[j] = multiColumns[j].trim();
            if (columnMapping[multiColumns[j]]) {
              fields.push(columnMapping[multiColumns[j]]);
              origFieldList.push(columns[i]);
              multiAxisHeaders.push(multiColumns[j]);
            }
          }
          if (!fields.includes("timestamp")) {
            fields.unshift("timestamp");
            origFieldList.unshift("Date");
            multiAxisHeaders.unshift("Date");
          }
        }
        break;
      default:
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Invalid visualization type"
        );
    }

    // Add panel filters to the fields
    for (const { field } of payload?.filters) {
      if (columnMapping[field] && !origFieldList.includes(field)) {
        // fields.push(columnMapping[field]);
        // origFieldList.push(field);

        if (multiAxisHeaders.length > 0) {
          multiAxisHeaders.push(field);
        }
      }
    }

    console.log("origFieldList->", origFieldList)
    // Add report filters to the fields
    if (payload?.reportFilters) {
      // Add negative to payload for handle in mega api query
      payload.negativeReport = payload?.reportFilters?.negative_report
        ? true
        : false;

      delete payload.reportFilters.negative_report;

      Object.keys(payload.reportFilters).forEach((backendField) => {
        // Find the display name in columnMapping where the value matches the backend field
        const displayField = Object.keys(columnMapping).find(
          (key) => columnMapping[key] === backendField
        );

        if (displayField && !origFieldList.includes(displayField)) {
          // fields.push(backendField); // push backend field name, as before
          // origFieldList.push(displayField);

          if (multiAxisHeaders.length > 0) {
            multiAxisHeaders.push(displayField);
          }
        }
      });
    }

    // Check if any of the fields are in the FieldsInMegaAPI array
    const fieldList =
      payload.visualizationType === "MultiAxis Graph"
        ? multiAxisHeaders
        : origFieldList;

    // const dataFromMegaAPI = fieldList.some(field => FieldsInMegaAPI.includes(field));
    const dataFromMegaAPI = true;

    // set graph true the data layer itself aggregation happening
    payload.graph = true;

    logger.info(`dataFromMegaAPI: ${dataFromMegaAPI}`);

    const whereClause = await buildWhereClause({
      payload,
      reportReqData,
      fields,
      user,
      dataFromMegaAPI,
      filterFlag,
    });

    // Find aggregation fields
    const aggregateResponse = await getAggregationData(
      payload,
      reportReqData,
      whereClause,
      fields,
      viewBy,
      user,
      dataFromMegaAPI
    );

    const headers =
      multiAxisHeaders.length > 0 ? multiAxisHeaders : origFieldList;

    const totalCount = await getTotalCount(
      payload,
      whereClause,
      viewBy,
      user,
      reportReqData,
      dataFromMegaAPI,
      fields
    );

    if (payload?.download || payload?.save || payload?.sendEmail) {
      // Set report name as a name of the panel
      payload.reportName = payload.name

      if (payload?.download) {
        await handleDownload({
          payload,
          user,
          fields,
          headers,
          viewBy,
          whereClause,
          totalCount,
          dataFromMegaAPI,
          aggregateResponse,
          res,
        });
        return;
      }

      if (payload?.sendEmail) {
        return await handleSendEmail({
          payload,
          user,
          fields,
          headers,
          viewBy,
          whereClause,
          totalCount,
          aggregateResponse,
          dataFromMegaAPI,
        });
      }

      if (
        payload?.save &&
        payload.save == 1 &&
        payload.visualizationType === "Table Report"
      ) {
        return await handleAutoSave({
          payload,
          user,
          fields,
          headers,
          viewBy,
          whereClause,
          totalCount,
          aggregateResponse,
          dataFromMegaAPI,
        });
      }
    }

    const { data, metaData } = await getDataFromMegaOrChartApi(
      user,
      payload,
      fields,
      viewBy,
      whereClause,
      dataFromMegaAPI,
      totalCount
    );

    data.data = padding(data.data);

    //Masking the number
    if (!user.isSuperAdmin && user.reportMasking && data?.data) {
      data.data = maskNumber(data.data);
    }

    let responseTobeSent;
    let graphdata;
    // Formate data based on the visualization type
    graphdata = await formPanelPreviewResponse(
      data.data,
      origFieldList,
      payload
    );
    // graphdata = data.data
    responseTobeSent = {
      data: graphdata,
      "Y-Axis": payload.dataColumns.derivedFields || null,
    };
    // Add total count for Table
    // if (payload.visualizationType == "Table Report") {
    //     responseTobeSent = { ...metaData, ...responseTobeSent };
    //     // responseTobeSent.count = graphdata.length;
    // }
    responseTobeSent = { ...metaData, filterFlag, ...responseTobeSent };

    if (aggregateResponse) {
      responseTobeSent.aggregateResponse = aggregateResponse;
    }

    return responseTobeSent;
  } catch (error) {
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

const buildReportWhereClause = async (payload, reportReqData, user) => {
  let reportFilterClause = "";

  // Build reportFilters for whereClause
  if (payload.reportFilters) {
    const reportFilters = payload.reportFilters;
    const { otherFilters = {} } = reportReqData || {};
    const otherFilterFields = Object.keys(otherFilters);

    const cleanedOtherPayload = {};
    const cleanedRegularFilters = {};
    const andGroups = [];

    // Separate other filters and regular filters
    for (const [key, value] of Object.entries(reportFilters)) {
      if (
        (Array.isArray(value) && value.length === 0) ||
        value === null ||
        value === undefined ||
        value === ""
      ) {
        continue; // skip empty
      }

      if (otherFilterFields.includes(key)) {
        cleanedOtherPayload[key] = value;
      } else {
        cleanedRegularFilters[key] = value;
      }
    }
    // Construct raw query for other filters
    if (Object.keys(cleanedOtherPayload).length !== 0) {
      andGroups.push(
        ...(await handleOtherFilters(
          cleanedOtherPayload,
          reportReqData,
          payload,
          user
        ))
      );
    }

    // Construct raw query for regular filters
    if (Object.keys(cleanedRegularFilters).length !== 0) {
      andGroups.push(
        ...handleRegularFilters(cleanedRegularFilters, reportReqData)
      );
    }

    reportFilterClause = andGroups.length > 0 ? andGroups.join(" and ") : null;
  }

  return reportFilterClause;
};

const buildRegularWhereClause = async ({ filters }) => {
  let whereClause = "";

  let filterClause = "";
  let nextOperator = "or";

  for (let j = 0; j < filters.length; j++) {
    // if (columnMapping[filters[j].field] == "customer_name" || columnMapping[filters[j].field] == "supplier")
    if (filters[j].condition == "Equal") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += nextOperator;
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} ='${filters[j].value}') `;
      }
    }

    if (filters[j].condition == "Not Equal") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += nextOperator;
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} !='${filters[j].value}') `;
      }
    }
    if (filters[j].condition == "Like") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += nextOperator;
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} ILIKE '%${filters[j].value}%') `;
      }
    }
    if (filters[j].condition == "Not Like") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += nextOperator;
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} NOT LIKE '%${filters[j].value}%') `;
      }
    }
    nextOperator = filters[j].operator || "and";
  }

  if (filterClause.length > 0) {
    if (whereClause.length > 0) {
      whereClause = `${whereClause} and (${filterClause})`;
    } else {
      whereClause = `${filterClause}`;
    }
  }
  return whereClause;
};

const panelQueryBuilder = ({
  payload,
  whereClause,
  fields,
  viewBy,
  user,
  dataFromMegaAPI,
}) => {
  const queryBuilder = dataFromMegaAPI
    ? constructGrapghqlQuery.getMegaAPIQuery
    : constructGrapghqlQuery.getChartApiQuery;

  let query = queryBuilder({
    payload,
    whereClause,
    fields,
    viewBy,
    user,
  });

  return query;
};

const getDataFromMegaOrChartApi = async (
  user,
  payload,
  fields,
  viewBy,
  whereClause,
  dataFromMegaAPI,
  totalCount
) => {
  logger.info("getDataFromMegaOrChartApi");

  // If The total count is greater than the limit, then we will fetch only the limit number of rows
  const totalCountToFetch =
    config.DATA_LIMIT_FOR_GRAPGH > 0 &&
      config.DATA_LIMIT_FOR_GRAPGH < totalCount
      ? config.DATA_LIMIT_FOR_GRAPGH
      : totalCount;

  if (totalCountToFetch != totalCount)
    logger.info(
      `Total count ${totalCount} is greater than the limit. Fetching only ${totalCountToFetch} rows`
    );

  const pageSize = config.NO_OF_DATA_PER_QUERY;
  const totalPages = Math.ceil(totalCountToFetch / pageSize);

  const payloadArray = [];

  if (
    totalCountToFetch > pageSize &&
    payload.visualizationType !== "Table Report"
  ) {
    for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
      payloadArray.push({
        ...payload,
        page: pageNumber,
        limit: Number(pageSize),
        // download: 1,
      });
    }
  } else {
    payloadArray.push({
      ...payload,
      page: payload.page,
      limit: Number(payload.limit),
      // download: 1,
    });
  }
  // If dataFromMegaAPI is true, we will use the mega API, otherwise we will use the chart API
  const name = dataFromMegaAPI ? "getSmsCdr" : "getDashboardChartData";
  let combinedData = [];
  let previousTimestamp = "";

  for (let i = 0; i < payloadArray.length; i++) {
    const currPayload = payloadArray[i];

    const query = panelQueryBuilder({
      payload: currPayload,
      whereClause,
      fields,
      viewBy,
      user,
      dataFromMegaAPI,
    });

    const response = await graphqlService.getGraphqlData({
      name,
      payload: query,
    });

    const responseData = await changeDateFormate(response.items, viewBy);
    combinedData.push(...responseData);
  }

  return {
    data: {
      data: combinedData,
      startDate: payload.startDate,
      endDate: payload.endDate,
    },
    metaData: {
      totalCount: totalCount,
      pageNo: payload.page,
      pageSize: payload.limit,
      count: combinedData.length,
    },
  };
};

const changeDateFormate = async (data, viewBy) => {
  const output = [];
  let previousTimestamp = "";

  for (const obj of data) {
    // Format timestamp
    if (
      timestampParam &&
      obj[timestampParam] &&
      previousTimestamp !== obj[timestampParam]
    ) {
      obj[timestampParam] = await formatReportDate(obj[timestampParam], viewBy);
    }
    previousTimestamp = obj[timestampParam];

    // Map column keys
    for (const key of Object.keys(obj)) {
      const columnkey = Object.keys(columnMapping).find(
        (ckey) => columnMapping[ckey] === key
      );
      if (columnkey) {
        obj[columnkey] = obj[key];
        delete obj[key];
      }
    }

    output.push(obj); // Push processed object
  }

  return output;
};

const formDataForDownload = async (data) => {
  const rows = [];

  for (let i = 0; i < data.length; i += 1) {
    const rowdata = [];
    for (const key in data[i]) {
      if (data[i][key] == null) {
        rowdata.push("");
      } else {
        rowdata.push(data[i][key]);
      }
    }
    rows.push(rowdata);
  }

  return rows;
};

const formPanelPreviewResponse = async (data, fields, payload) => {
  let x_axis = [],
    y_axis = [],
    responseData = data,
    tmp_xaxis,
    tmp_yaxis,
    x_axisagg = {};
  switch (payload.visualizationType) {
    case "Pie Chart":
      let pieData = {};
      let count = 0;
      data.map((obj) => {
        Object.keys(obj).map((key) => {
          if (payload.dataColumns.derivedFields.includes(key)) {
            if (!isNaN(obj[key])) {
              if (!pieData[key] && pieData[key] != 0) {
                pieData[key] = 0;
                if (percentageFields.includes(key)) pieData[key + "_count"] = 0;
              }
              if (percentageFields.includes(key)) {
                pieData[key] = pieData[key] + obj[key] / 100;
                pieData[key + "_count"]++;
              } else {
                pieData[key] += obj[key];
              }
            }
          }
        });
      });
      for (let j = 0; j < percentageFields.length; j++) {
        if (pieData[percentageFields[j]])
          pieData[percentageFields[j]] = Number(
            (
              (pieData[percentageFields[j]] /
                pieData[percentageFields[j] + "_count"]) *
              100
            ).toFixed(2)
          );

        delete pieData[percentageFields[j] + "_count"];
      }
      responseData = pieData;

      break;
    case "Bar Graph":
      if (
        payload.dataColumns["X-Axis"] &&
        payload.dataColumns["X-Axis"].length > 0
      ) {
        data.map((obj) => {
          tmp_xaxis = {};
          tmp_yaxis = {};

          if (!x_axisagg[obj[payload.dataColumns["X-Axis"][0]]]) {
            x_axisagg[obj[payload.dataColumns["X-Axis"][0]]] = {};
          }

          Object.keys(obj).map((key) => {
            if (payload.dataColumns["X-Axis"].includes(key)) {
              tmp_xaxis[key] = obj[key];
            }
            if (payload.dataColumns.derivedFields.includes(key)) {
              if (
                !x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] &&
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] != 0
              ) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] = 0;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ] = 0;
              }
              if (percentageFields.includes(key)) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] =
                  x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +
                  obj[key] / 100;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ]++;
              } else
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +=
                  obj[key];
              tmp_yaxis[key] = obj[key];
            }
          });
        });
        let keydata = {};

        Object.keys(x_axisagg).map((key) => {
          keydata = {};

          for (const property in x_axisagg[key]) {
            if (
              percentageFields.includes(property) &&
              x_axisagg[key][property]
            ) {
              x_axisagg[key][property] = Number(
                (
                  (x_axisagg[key][property] /
                    x_axisagg[key][property + "_count"]) *
                  100
                ).toFixed(2)
              );
            }
            delete x_axisagg[key][property + "_count"];
          }

          keydata[payload.dataColumns["X-Axis"][0]] = key;

          x_axis.push(keydata);
          y_axis.push(x_axisagg[key]);
        });

        x_axis = x_axis.map((entry) => Object.values(entry)[0]); // Extracts values dynamically

        // Step 2: Combine X and Y axis into single array of objects
        const combinedData = x_axis.map((customer, index) => {
          const yData = y_axis[index];
          const yKeys = Object.keys(yData); // Get all keys dynamically
          return {
            customer,
            metrics: yKeys.map((key) => ({ [key]: yData[key] })),
          };
        });

        // Step 3: Sort combinedData based on the first key in the first object in Y-axis
        combinedData.sort((a, b) => {
          const aFirstKey = Object.keys(a.metrics[0])[0]; // Get the first key dynamically
          const bFirstKey = Object.keys(b.metrics[0])[0]; // Get the first key dynamically
          return b.metrics[0][aFirstKey] - a.metrics[0][bFirstKey];
        });

        // Step 4: Extract sorted X-axis names and sorted Y-axis metrics
        x_axis = [];
        const sortedX_axis = combinedData.map((entry) => entry.customer);
        for (let k = 0; k < sortedX_axis.length; k++) {
          let tmpData = {};

          tmpData[payload.dataColumns["X-Axis"][0]] = sortedX_axis[k];
          x_axis.push(tmpData);
        }

        const sortedY_axis = combinedData.map((entry) => {
          const yData = {};
          entry.metrics.forEach((metric) => {
            const key = Object.keys(metric)[0];
            yData[key] = metric[key];
          });
          return yData;
        });

        x_axis = x_axis.slice(0, payload.dataColumns.noOfRecords);
        y_axis = sortedY_axis.slice(0, payload.dataColumns.noOfRecords);
        responseData = {
          x_axis,
          y_axis,
        };
      } else {
        responseData = {
          x_axis: [],
          y_axis: [],
        };
      }

      break;
    case "Line Graph":
      payload.dataColumns["X-Axis"] = ["Datetime"];
      if (
        payload.dataColumns["X-Axis"] &&
        payload.dataColumns["X-Axis"].length > 0
      ) {
        data.map((obj) => {
          tmp_xaxis = {};
          tmp_yaxis = {};

          if (!x_axisagg[obj[payload.dataColumns["X-Axis"][0]]]) {
            x_axisagg[obj[payload.dataColumns["X-Axis"][0]]] = {};
          }
          let count = 0;

          Object.keys(obj).map((key) => {
            if (payload.dataColumns["X-Axis"].includes(key)) {
              tmp_xaxis[key] = obj[key];
            }
            if (payload.dataColumns.derivedFields.includes(key)) {
              if (
                !x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] &&
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] != 0
              ) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] = 0;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ] = 0;
              }
              if (percentageFields.includes(key)) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] =
                  x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +
                  obj[key] / 100;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ]++;
              } else
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +=
                  obj[key];
              tmp_yaxis[key] = obj[key];
            }
          });
        });
        let keydata = {};
        Object.keys(x_axisagg).map((key) => {
          keydata = {};
          keydata[payload.dataColumns["X-Axis"][0]] = key;

          for (const property in x_axisagg[key]) {
            if (percentageFields.includes(property)) {
              x_axisagg[key][property] = Number(
                (
                  (x_axisagg[key][property] /
                    x_axisagg[key][property + "_count"]) *
                  100
                ).toFixed(2)
              );
            }
            delete x_axisagg[key][property + "_count"];
          }

          x_axis.push(keydata);
          y_axis.push(x_axisagg[key]);
        });

        responseData = {
          x_axis,
          y_axis,
        };
      } else {
        responseData = {
          x_axis: [],
          y_axis: [],
        };
      }

      break;

    case "Table Report":
      responseData = data;

      break;

    case "MultiAxis Graph":
      let yaxisfields = [];
      for (let i = 0; i < fields.length; i++) {
        if (fields[i].name) {
          yaxisfields = yaxisfields.concat(fields[i].name.split("and"));
        }
      }
      yaxisfields = yaxisfields.map((s) => s.trim());

      // y_axis = payload.dataColumns.derivedFields;
      payload.dataColumns["X-Axis"] = ["Datetime"];
      if (
        payload.dataColumns["X-Axis"] &&
        payload.dataColumns["X-Axis"].length > 0
      ) {
        data.map((obj) => {
          tmp_xaxis = {};
          tmp_yaxis = {};

          if (!x_axisagg[obj[payload.dataColumns["X-Axis"][0]]]) {
            x_axisagg[obj[payload.dataColumns["X-Axis"][0]]] = {};
          }

          Object.keys(obj).map((key) => {
            if (payload.dataColumns["X-Axis"].includes(key)) {
              tmp_xaxis[key] = obj[key];
            }
            if (yaxisfields.includes(key)) {
              if (
                !x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] &&
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] != 0
              ) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] = 0;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ] = 0;
              }

              if (percentageFields.includes(key)) {
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] =
                  x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +
                  obj[key] / 100;
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][
                  key + "_count"
                ] += 1;
              } else
                x_axisagg[obj[payload.dataColumns["X-Axis"][0]]][key] +=
                  obj[key];
              tmp_yaxis[key] = obj[key];
            }
          });
        });

        let keydata = {};
        Object.keys(x_axisagg).map((key) => {
          keydata = {};
          keydata[payload.dataColumns["X-Axis"][0]] = key;
          for (const property in x_axisagg[key]) {
            if (
              percentageFields.includes(property) &&
              x_axisagg[key][property + "_count"] &&
              x_axisagg[key]
            ) {
              x_axisagg[key][property] = Number(
                (
                  (x_axisagg[key][property] /
                    x_axisagg[key][property + "_count"]) *
                  100
                ).toFixed(2)
              );
            }
            delete x_axisagg[key][property + "_count"];
          }

          x_axis.push(keydata);
          x_axisagg[key][payload.dataColumns["X-Axis"][0]] = key;
          y_axis.push(x_axisagg[key]);
        });
        responseData = y_axis;
      } else {
        responseData = [];
      }
  }

  return responseData;
};

module.exports = {
  createPanel,
  getPanel,
  getPanelByName,
  getPanelByNames,
  getDynamicPanels,
  getAdminDynamicPanels,
  getPanels,
  updatePanel,
  deletePanel,
  getPanelData,
};
