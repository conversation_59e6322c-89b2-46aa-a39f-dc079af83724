const jwt = require("jsonwebtoken");
const moment = require("moment");
const httpStatus = require("http-status");
const config = require("../config/config");
const userService = require("./user.service");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const { tokenTypes } = require("../config/tokens");
const logger = require("../config/logger"),
  { Token } = models,
  /**
   * generate token
   * @param {ObjectId} userId
   * @param {Moment} expires
   * @param {string} [secret]
   * @returns {string}
   */
  generateToken = (userId,expires,type,timezone,secret = config.jwt.secret) => {
    const payload = {
      sub: userId,
      timezone,
      iat: moment().unix(),
      exp: expires.unix(),
      type,
    };
    return jwt.sign(payload,secret);
  },
  /**
   * save a token
   * @param {string} token
   * @param {ObjectId} userId
   * @param {Moment} expires
   * @param {string} type
   * @param {boolean} [blacklisted]
   * @returns {Promise<Token>}
   */
  saveToken = async (token,userId,expires,type,blacklisted = false) => {
    const tokenDoc = await Token.create({
      token,
      userId,
      expires: expires.toDate(),
      type,
      blacklisted,
    });
    return tokenDoc;
  },
  /**
   * verify token and return token doc (or throw an error if it is not valid)
   * @param {string} token
   * @param {string} type
   * @returns {Promise<Token>}
   */
  verifyToken = async (token,type) => {
    let payload;
    try {
      payload = jwt.verify(token,config.jwt.secret);
    }
 catch (err) {
      throw new ApiError(httpStatus.BAD_REQUEST,"Token expired");
    }

    tokenDoc = await Token.findOne({
      where: {
        token,
        type,
        userId: payload.sub,
      },
    });
    if (!tokenDoc) {
      throw new ApiError(httpStatus.BAD_REQUEST,"Invalid token");
      // throw new Error('Token not found');
    }
    return tokenDoc;
  },
  verifyOTP = async (otp,type,userId) => {
    const tokenDoc = await Token.findOne({
      where: {
        token: otp,
        type,
        userId,
      },
      raw: true,
    });
    if (!tokenDoc) {
      logger.error("OTP verification failed");
      throw new ApiError(httpStatus.UNAUTHORIZED,"OTP verification failed");
    }
 else if (moment(tokenDoc.expires).unix() < moment().unix()) {
      await Token.destroy({
        where: {
          token: otp,
          type,
          userId,
        },
      });
      throw new ApiError(httpStatus.UNAUTHORIZED,"OTP is expired");
    }
    return tokenDoc;
  },
  /**
   * generate auth tokens
   * @param {User} user
   * @returns {Promise<Object>}
   */
  generateAuthTokens = async (user,timezone = "Asia/Kolkata") => {
    const accessTokenExpires = moment().add(
        config.jwt.accessExpirationMinutes,
        "minutes"
      ),
      accessToken = generateToken(
        user.id,
        accessTokenExpires,
        tokenTypes.ACCESS,
        timezone
      ),
      refreshTokenExpires = moment().add(
        config.jwt.refreshExpirationDays,
        "days"
      ),
      refreshTokenMaxAge = refreshTokenExpires.diff(moment().add(5,"minutes")),
      refreshToken = generateToken(
        user.id,
        refreshTokenExpires,
        tokenTypes.REFRESH
      );
    await saveToken(
      refreshToken,
      user.id,
      refreshTokenExpires,
      tokenTypes.REFRESH
    );

    return {
      access: {
        token: accessToken,
        expires: accessTokenExpires.toDate(),
      },
      refresh: {
        token: refreshToken,
        maxAge: refreshTokenMaxAge,
      },
    };
  },
  /**
   * generate reset password token
   * @param {string} email
   * @returns {Promise<string>}
   */
  generateResetPasswordToken = async (email) => {
    const user = await userService.getUserByEmail(email);
    if (!user) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        "No users found with this email"
      );
    }
    const expires = moment().add(
        config.jwt.resetPasswordExpirationMinutes,
        "minutes"
      ),
      resetPasswordToken = generateToken(
        user.id,
        expires,
        tokenTypes.RESET_PASSWORD
      );
    await saveToken(
      resetPasswordToken,
      user.id,
      expires,
      tokenTypes.RESET_PASSWORD
    );
    return resetPasswordToken;
  },
  /**
   * generate email verification token
   * @param {string} email
   * @returns {Promise<string>}
   */
  generateEmailVerificationToken = async (user) => {
    const expires = moment().add(
        config.jwt.emailVerificationExpirationDays,
        "days"
      ),
      emailVerificationToken = generateToken(
        user.id,
        expires,
        tokenTypes.EMAIL_VERIFICATION
      );
    await saveToken(
      emailVerificationToken,
      user.id,
      expires,
      tokenTypes.EMAIL_VERIFICATION
    );
    return emailVerificationToken;
  },
  generate2FAToken = async (user) => {
    const expires = moment().add(
        config.jwt.otpVerificationExpirationMinutes,
        "minutes"
      ),
      digits = "0123456789";
    let otp = "";
    for (let i = 0; i < config.otpLength; i += 1) {
      otp += digits[Math.floor(Math.random() * 10)];
    }
    await Token.destroy({
      where: {
        userId: user.id,
        type: tokenTypes.EMAIL_VERIFICATION,
      },
    });
    await saveToken(otp,user.id,expires,tokenTypes.EMAIL_VERIFICATION);
    return otp;
  };

module.exports = {
  generateToken,
  saveToken,
  verifyToken,
  generateAuthTokens,
  generateResetPasswordToken,
  generateEmailVerificationToken,
  generate2FAToken,
  verifyOTP,
};