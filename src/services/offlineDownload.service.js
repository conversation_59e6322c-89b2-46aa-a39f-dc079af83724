const { offline_downloads } = require("../config/config");
const { adminUser, resources, rolePermissions } = require("../config/roles");
const { models } = require("../models");
const { convertToClientTime } = require("../utils/misc");
const sortBy = require("../utils/sortBy");
const { offline_download } = models;
const { Op } = require("sequelize");

const createOfflineDownload = async (data) => {
  try {
    const createdRecord = await offline_download.create(data);
    return createdRecord;
  } catch (error) {
    console.error("Error creating Offline download record:", error);
    throw error;
  }
};

const getOfflineDownloadData = async (filter = {}, options = {}, user) => {
const roleService = require("./role.service");

  // ── 1. Pagination & Sorting Defaults ──────────────────────────────────────
  const limit = parseInt(options.limit, 10) || 10;
  const page = parseInt(options.page, 10) || 1;
  const offset = (page - 1) * limit;
  const sortByArray = [
    ["createdAt", "DESC"],
    ...(options.sortBy ? sortBy(options.sortBy) : []),
  ];

  // ── 3. Exclude reports the role can’t download ──────────────────────────
  const role = await roleService.getRole(user, user.role.id);
  // console.log("role is ", JSON.stringify(role, null, 2));

  if(!user.isSuperAdmin) {
    filter.userId = user.id;

    // We check role.staticReports because role.resources permssion is 0 for download
    const isReportDownloadAllowed = role.staticReports.some(
      (r) => r.permissions.download == 1
    );

    const isCdrDownloadAllowed = role.resources.some(
      (r) => r.name === resources.CDR_SEARCH && r.permissions.download == 1
    );

    const categoryFilter = [];

    if (!isReportDownloadAllowed)
      categoryFilter.push(offline_downloads.category.static);

    if (!isCdrDownloadAllowed)
      categoryFilter.push(offline_downloads.category.cdr);

    if (categoryFilter.length > 0)
      filter.category = { [Op.notIn]: categoryFilter };

    if (
      isReportDownloadAllowed &&
      Array.isArray(role.staticReports) &&
      role.staticReports.length
    ) {
      const disallowedReports = role.staticReports
        .filter((r) => r.permissions.download != 1)
        .map((r) => r.name);

      if (disallowedReports.length) {
        filter.reportName = { [Op.notIn]: disallowedReports };
      }
    }
  
  }

  // ── 4. Free‐text search across several fields ────────────────────────────
  if (filter.search) {
    const q = `%${filter.search}%`;
    delete filter.search;
    filter[Op.or] = [
      { fileName: { [Op.like]: q } },
      { status: { [Op.like]: q } },
      { id: { [Op.like]: q } },
      { createdAt: { [Op.like]: q } },
      { initiatedBy: { [Op.like]: q } },
    ];
  }

  console.log("filter is ", filter);

  // ── 5. Fire off count & fetch in parallel ───────────────────────────────
  const [totalCount, rows] = await Promise.all([
    offline_download.count({ where: filter }),
    offline_download.findAll({
      where: filter,
      limit,
      offset,
      order: sortByArray,
    }),
  ]);

  // ── 6. Convert timestamps in parallel ───────────────────────────────────
  await Promise.all(
    rows.map(async (rec) => {
      rec.dataValues.createdAt = await convertToClientTime(
        rec.createdAt,
        user.timezone
      );
      rec.dataValues.updatedAt = await convertToClientTime(
        rec.updatedAt,
        user.timezone
      );
    })
  );

  // ── 7. Return paginated envelope ────────────────────────────────────────
  return {
    data: rows,
    pageNo: page,
    pageSize: limit,
    totalCount,
    totalPages: Math.ceil(totalCount / limit),
  };
};

const updateOfflineDownload = async (id, data) => {
  return await offline_download.update(data, { where: { id } });
};

const downloadCrdFile = async (id) => {
  const offlineDownloadData = await offline_download.findOne({ where: { id } });
  // console.log("offlineDownloadData is ", offlineDownloadData);
  return offlineDownloadData;
};

const getDownloadById = async (id, user) => {
  const userId = user.id || {};
  const timezone = user.timezone || {};
  // console.log("id is ", id);
  const offlineDownloadData = await offline_download.findOne({ where: { id } });

  if (user.name.toLowerCase() != adminUser.name.toLowerCase()) {
    if (offlineDownloadData.userId !== userId) {
      return null;
    }
  }

  offlineDownloadData.dataValues.createdAt = await convertToClientTime(
    offlineDownloadData.createdAt,
    user.timezone
  );

  offlineDownloadData.dataValues.updatedAt = await convertToClientTime(
    offlineDownloadData.updatedAt,
    user.timezone
  );

  // console.log("offlineDownloadData is ", offlineDownloadData);
  return { data: offlineDownloadData };
};

const deleteOfflineDownload = async (id) => {
  const offlineDownloadData = await offline_download.delete({ where: { id } });
  // console.log("offlineDownloadData is ", offlineDownloadData);
  return offlineDownloadData;
};

module.exports = {
  createOfflineDownload,
  getOfflineDownloadData,
  getDownloadById,
  updateOfflineDownload,
  downloadCrdFile,
  deleteOfflineDownload,
};
