const httpStatus = require('http-status');
const bcrypt = require('bcryptjs');
const userService = require('./user.service');
const tokenService = require('./token.service');
const roleService = require('./role.service');
const emailService = require('./email.service');
const auditLogService = require('./audit-log.service')
const { models } = require('../models');
const ApiError = require('../utils/ApiError');
const ldapAuth = require('../utils/ldapAuth');
const { tokenTypes } = require('../config/tokens');
const { auditLogEvents,defaultUserRole } = require('../config/roles');
const { clientURL,ldap_config } = require('../config/config');
const { Error } = require('sequelize');
const logger = require('../config/logger'),


  { User,Token } = models,
  /**
   * login with username and password
   * @param {string} email
   * @param {string} password
   * @returns {Promise<User>}
   */
  loginUserWithEmailAndPassword = async (email,password) => {
    let platformLogin = false,
      user;
    if (ldap_config.enabled) {
      let checkIfEmailExists = await userService.getUserByEmail(email)
      if(!checkIfEmailExists)
      {
        let auditLogPayload = {
          username: email,
          userId: "",
          roleName: "",
          event: auditLogEvents.LOGIN_FAILURE,
          action: "Incorrect email/password"
        }
        auditLogService.addAuditLog(auditLogPayload)
        throw new ApiError(httpStatus.UNAUTHORIZED,'Incorrect email/password');
      }
      let ldapResponse = await ldapAuth(email,password);
      logger.info("ldap response is " + ldapResponse);
      if (ldapResponse) {
        if(ldapResponse == -61) {
          let auditLogPayload = {
            username: email,
            userId: email,
            roleName: "",
            event: auditLogEvents.LOGIN_FAILURE,
            action: "LDAP connection failed"
          }
          auditLogService.addAuditLog(auditLogPayload);
          platformLogin = true;
        }
        else {
        //check if user exists in DB
        user = await userService.getUserByEmail(email);
        if (!user) {
          try {
            user = await userService.createUser({
              name: email,
              email,
              isEmailVerified: true,
              lastLoggedin: new Date(),
              isLdapUser: true
            },true);
          }
          catch (err) {
            logger.error("error in creating user ",err)
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR,'Something went wrong. Please try after sometime');
          }
          //send mail to admin

          const emailObj = {
            from: process.env.EMAIL_FROM,
            to: process.env.ADMIN_EMAIL || "<EMAIL>",
            subject: "New user registered",
            template: 'ldap_user_create.template',
            context: {
              email,
              loginUrl: `${clientURL}/auth/login/`

            },
          },
            emailResponse = await emailService.MailService(emailObj);
        }
        else if (!user.isUserActive) {
          let auditLogPayload = {
            username: email,
            userId: user.id,
            roleName: "",
            event: auditLogEvents.LOGIN_FAILURE,
            action: "User is not active"
          }
          auditLogService.addAuditLog(auditLogPayload)
          throw new ApiError(httpStatus.BAD_REQUEST,'User is not active');
        }
       
      }
      }
      else {
        platformLogin = true;
      }
    }
    else {
      platformLogin = true;
    }
    if (platformLogin) {
      user = await userService.getUserByEmail(email);
      if (!user || !await user.isPasswordMatch(password)) {
        let auditLogPayload = {
          username: email,
          userId: email,
          roleName: "",
          event: auditLogEvents.LOGIN_FAILURE,
          action: "Incorrect email/password"
        }
        auditLogService.addAuditLog(auditLogPayload)
        throw new ApiError(httpStatus.UNAUTHORIZED,'Incorrect email or password');
      }
      if (!user.isUserActive) {
        let auditLogPayload = {
          username: email,
          userId: user.id,
          roleName: "",
          event: auditLogEvents.LOGIN_FAILURE,
          action: "User is not active"
        }
        auditLogService.addAuditLog(auditLogPayload)
        throw new ApiError(httpStatus.UNAUTHORIZED,'User is not active');
      }
    }
      userService.updateUser(user,{ lastLoggedin: new Date() });
      const role = await roleService.getRole(user,user.roleId);
      let auditLogPayload = {
        username: user.name,
        userId: user.id,
        roleName: role.name,
        event: auditLogEvents.LOGIN,
        action: "Login to the portal"
      }
      auditLogService.addAuditLog(auditLogPayload)
    

    return user;
  },

  /**
   * logout
   * @param {string} refreshToken
   * @returns {Promise}
   */
  logout = async (refreshToken) => {
    const refreshTokenDoc = await Token.findOne({
      where: {
        token: refreshToken,
        type: tokenTypes.REFRESH,
        blacklisted: false
      },
    });
    if (!refreshTokenDoc) {
      throw new ApiError(httpStatus.NOT_FOUND,'Not found');
    }
    await refreshTokenDoc.destroy();
  },

  /**
   * refresh auth tokens
   * @param {string} refreshToken
   * @returns {Promise<Object>}
   */
  refreshAuth = async (refreshToken) => {
    try {
      const refreshTokenDoc = await tokenService.verifyToken(refreshToken,tokenTypes.REFRESH);
      const user = await userService.getUserById(refreshTokenDoc.userId);

      if (!user) {
        throw new Error();
      }
      await refreshTokenDoc.destroy();
      return {
        user,
        tokens: await tokenService.generateAuthTokens(user)
      };
    }
    catch (error) {
      logger.error(error)
      throw new ApiError(httpStatus.UNAUTHORIZED,'Please authenticate');
    }
  },

  /**
   * reset password
   * @param {string} resetPasswordToken
   * @param {string} newPassword
   * @returns {Promise}
   */
  resetPassword = async (resetPasswordToken,newPassword) => {
 
      const resetPasswordTokenDoc = await tokenService.verifyToken(resetPasswordToken,tokenTypes.RESET_PASSWORD),
        user = await userService.getUserById(resetPasswordTokenDoc.userId);
      if (!user) {
        throw new ApiError(httpStatus.UNAUTHORIZED,'Password reset failed');
      }
      if (await user.isPasswordMatch(newPassword)) {
        throw new ApiError(httpStatus.BAD_REQUEST,'New password cannot be same as old password');
      }
      
      let userData = user.dataValues;
      if (userData.oldPasswords && userData.oldPasswords.length > 0) {
        for (let i = 0; i < userData.oldPasswords.length; i++) {
          let passwordMatch = await bcrypt.compare(newPassword,userData.oldPasswords[i]);

          if (passwordMatch) {
 throw new ApiError(httpStatus.BAD_REQUEST,'New password cannot be same as last 3 passwords'); 
}
        }
      }
      let { oldPasswords } = userData;
      if (!oldPasswords) {
 oldPasswords = []; 
}
      oldPasswords.unshift(userData.password);
      oldPasswords = oldPasswords.slice(0,2)
      
      try{
      await userService.updateUserById(user.id,{
        password: newPassword,
        isEmailVerified: true,
        oldPasswords
      });
      await Token.destroy({
        where: {
          userId: user.id,
          type: tokenTypes.RESET_PASSWORD
        }
      });
    }
    catch (error) {
      logger.error("error is "+error)
      throw new ApiError(httpStatus.UNAUTHORIZED,'Password reset failed');
    }
  },

  /**
   * email verification
   * @param {string} emailVerificationToken
   * @returns {Promise}
   */
  emailVerification = async (emailVerificationToken) => {
    try {
      const emailVerificationTokenDoc = await tokenService.verifyToken(emailVerificationToken,tokenTypes.EMAIL_VERIFICATION);
      let user = await userService.getUserById(emailVerificationTokenDoc.user);
      if (!user) {
        throw new Error();
      }
      await Token.deleteMany({
        user: user.id,
        type: tokenTypes.EMAIL_VERIFICATION
      });
      user = await userService.updateUserById(user.id,{ isEmailVerified: true });
      return user;
    }
    catch (error) {
      throw new ApiError(httpStatus.UNAUTHORIZED,'Email verification failed');
    }
  },


  sendResetPasswordEmail = async (email) => {
    let user = await userService.getUserByEmail(email);
    if (!user) {
      throw new ApiError(httpStatus.BAD_REQUEST,'Invalid email id');
    }
    if (user.isLdapUser) {
      throw new ApiError(httpStatus.BAD_REQUEST,'LDAP users not allowed to change password from platform');
    }
    let token = await tokenService.generateResetPasswordToken(email);
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: "Welcome to Smshub!",
      template: 'reset_password.template',
      context: {
        user: user.name,
        resetPasswordUrl: `${clientURL}/auth/reset-password/${token}`

      }
    },
      emailResponse = await emailService.MailService(emailObj);
    if (!emailResponse.isSuccess) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR,"Failed sending mail");
    }

  },


  sendOTP = async (user) => {
    let otp = await tokenService.generate2FAToken(user);
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: user.email,
      subject: "Welcome to Smshub!",
      template: 'otp_verify.template',
      context: {
        user: user.name,
        OTP: otp

      },
    };
    const emailResponse = await emailService.MailService(emailObj);

    if (!emailResponse.isSuccess) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR,'Failed sending OTP mail');
    }

  },


  verifyOTP = async (otp,email) => {

    const user = await userService.getUserByEmail(email);
    if (!user) {
      throw new ApiError(httpStatus.UNAUTHORIZED,'OTP Validation failed');
    }
    await tokenService.verifyOTP(otp,tokenTypes.EMAIL_VERIFICATION,user.id);

    await Token.destroy({
      where: {
        userId: user.id,
        type: tokenTypes.EMAIL_VERIFICATION
      }
    });
    return user


  };


module.exports = {
  loginUserWithEmailAndPassword,
  logout,
  refreshAuth,
  resetPassword,
  emailVerification,
  sendResetPasswordEmail,
  sendOTP,
  verifyOTP
};
