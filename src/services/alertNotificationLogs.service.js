




const createAlertNotificationLogs = async (payload) => {
  try {
    const { alertId, name, description, state, startsAt, everyRunMinute, intervalStart, intervalEnd, status, category, alertType, triggeredAt, notificationPayload } = payload;
    const log = await AlertNotificationLog.create({
      alert_id: alertId,
      name,
      description,
      state,
      startsAt,
      every_run_minute: everyRunMinute,
      interval_start: intervalStart,
      interval_end: intervalEnd,
      status,
      category,
      alert_type: alertType,
      triggered_at: triggeredAt,
      notification_payload: notificationPayload
    });
    return log;
  } catch (error) {
    console.error("error in create notification logs", error)
    throw error
  }
};

module.exports = {
  createAlertNotificationLogs,
};