const httpStatus = require("http-status");
const { Op, ValidationError, Sequelize } = require("sequelize");
const lodash = require("lodash");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const logger = require("../config/logger");
const { graphqlRequestHandler, gqlOp } = require("./graphql.service");
const {
  createAlertConfig,
  alertConfig,
  alertApiFields,
  createContactPointConfig,
  getContactPointsConfig,
  getAlertsConfig,
  deleteAlertConfig,
  alertGrafanaStates,
  getErrDesMappingConfig,
  nodesConfig,
} = require("../config/alertConfig");
const {
  singleToDoubleQuotes,
  decodeHtmlEntities,
  getSortOrderList,
  usePageLimit,
  getOrDefaultDateRange,
} = require("../utils/helper");
const { useTablesRollback } = require("../utils/rollback");
const { c } = require("tar");
const { buildWhereClauseFromFilters } = require("../utils/build-advanced-filter");
const { error } = require("winston");
const { buildObjectFromAlert } = require("./grafanaWebhook.service");
const { convertToClientTime } = require("../utils/misc"),
  { Email_group, AlertsModel, AlertsHistoryModel } = models
const createGroup = async (payload) => {
  let group;
  if (
    payload.members &&
    payload.members.length > 0 &&
    payload.members.some((val, i) => payload.members.indexOf(val) !== i)
  ) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Members has duplicate values"
    );
  }

  if (payload.subGroups) {
    for (let i = 0; i < payload.subGroups.length; i++) {
      let groupDetails = await Email_group.findOne({
        where: { id: payload.subGroups[i] },
      });
      if (!groupDetails) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid sub groups");
      }
    }
  }
  try {
    group = await Email_group.create(payload);
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Group name used before"
            );
        }
      });
    }
  }
  return group;
};
const getGroupById = async (id) => {
  const group = await Email_group.findOne({ where: { id }, raw: true });

  if (!group) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Group does not exists");
  }
  let subGroups = [];
  if (group.subGroups && group.subGroups.length > 0) {
    for (let j = 0; j < group.subGroups.length; j++) {
      let groupDetails = await Email_group.findOne({
        where: { id: group.subGroups[j] },
      });

      if (groupDetails) {
        subGroups.push({
          name: groupDetails.name,
          members: groupDetails.members,
          id: groupDetails.id,
        });
      }
    }
    group.subGroups = subGroups;
  }
  return group;
};
const getGroupByName = async (name) => {
  const group = await Email_group.findOne({ where: { name } });
  if (!group) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Group does not exists");
  }
  let subGroups = [];
  if (group.subGroups && group.subGroups.length > 0) {
    let groupDetails = await Email_group.findOne({
      where: { id: group.subGroups[i] },
    });
    if (groupDetails) {
      subGroups.push({
        name: groupDetails.name,
        members: groupDetails.members,
        id: groupDetails.id,
      });
    }
    group.subGroups = subGroups;
  }
  return group;
};
const getGroups = async (user, filter, options) => {
  let limit, logs, offset, page;
  if (options) {
    limit = Number(options.limit) || null;
    page = Number(options.page) || null;
    offset = (page - 1) * limit;
  }
  let subGroup = 0;
  if (filter && filter.subGroup) {
    subGroup = 1;
  }
  if (Object.keys(filter).length == 0) {
    logs = await Email_group.findAndCountAll({
      limit,
      offset,
      where: { createdBy: user.id },
      order: [["createdAt", "DESC"]],
    });
  }
  if (filter.subGroup) {
    logs = await Email_group.findAndCountAll({
      limit,
      offset,
      where: {
        isSubGroup: 1,
        createdBy: user.id,
      },
      order: [["createdAt", "DESC"]],
    });
  } else if (filter.search) {
    let searchQuery = filter.search;
    // filter = "{[Op.or]: [name: { [Op.like]: '%' " + searchQuery + "'%' },description: { [Op.like]: '%' + searchQuery2 + '%' }]}"
    logs = await Email_group.findAndCountAll({
      limit,
      offset,
      where: {
        [Op.or]: {
          name: { [Op.like]: `%${searchQuery}%` },
          description: { [Op.like]: `%${searchQuery}%` },
        },
        createdBy: user.id,
      },
      order: [["createdAt", "DESC"]],
    });
  }
  let groups = logs.rows;
  for (let i = 0; i < groups.length; i++) {
    let group = groups[i];
    group.dataValues.createdAt = await convertToClientTime(
      group.createdAt,
      user.timezone
    );
    group.dataValues.updatedAt = await convertToClientTime(
      group.updatedAt,
      user.timezone
    );

    let subGroups = [];
    if (group.subGroups && group.subGroups.length > 0) {
      for (let j = 0; j < group.subGroups.length; j++) {
        let groupDetails = await Email_group.findOne({
          where: { id: group.subGroups[j] },
        });
        if (groupDetails) {
          subGroups.push({
            name: groupDetails.name,
            members: groupDetails.members,
            id: groupDetails.id,
          });
        }
      }
      group.subGroups = subGroups;
    }
    groups[i] = group;
  }

  let responseTobeSent = {
    totalCount: logs.count,
    pageNo: page,
    pageSize: limit,
    data: groups,
  };
  return responseTobeSent;
};
const updateGroup = async (groupId, payload) => {
  let group = await Email_group.findByPk(groupId);
  if (
    payload.members &&
    payload.members.length > 0 &&
    payload.members.some((val, i) => payload.members.indexOf(val) !== i)
  ) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Members has duplicate values"
    );
  }

  if (payload.hasOwnProperty("isSubGroup")) {
    delete payload.isSubGroup;
  }
  if (payload.subGroups) {
    for (let i = 0; i < payload.subGroups.length; i++) {
      let groupDetails = await Email_group.findOne({
        where: { id: payload.subGroups[i] },
      });
      if (!groupDetails) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Invalid sub groups");
      }
    }
  }

  try {
    Object.assign(group, payload);
    await group.save();
    return group;
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Group name used before"
            );
        }
      });
    }
  }
};
const deleteGroup = async (id) => {
  let group = await getGroupById(id);
  await Email_group.destroy({ where: { id } });
  return group;
};
const getGroupMembers = async (groupList) => {
  let membersList = [];
  for (let i = 0; i < groupList.length; i++) {
    const group = await Email_group.findOne({
      where: { id: groupList[i] },
      raw: true,
    });

    if (!group) {
      continue;
    }
    let subGroupMembers = [];
    if (group.subGroups && group.subGroups.length > 0) {
      for (let j = 0; j < group.subGroups.length; j++) {
        let groupDetails = await Email_group.findOne({
          where: { id: group.subGroups[j] },
        });
        subGroupMembers = groupDetails.members;
      }
      membersList = membersList.concat(subGroupMembers);
    }
    membersList = membersList.concat(group.members);
  }
  return membersList;
};
const createContactPoint = async (contactPointStr) => {
  const contactPointName = `SMSHub-BE-${Date.now()}`;
  const mutation = `
            createContactPoint(
                name: "${contactPointName}"
                description: "${contactPointName} auto generated contact points"
                emails: "${contactPointStr}"
            ) {
                description
                emails
                id
            }
    `;

  const contactPointData = await graphqlRequestHandler(
    createContactPointConfig.name,
    mutation,
    gqlOp.mutation
  );
  if (contactPointData) {
    logger.info(
      `Created Contact point for ${contactPointStr}: "id" is ${contactPointData.id} `
    );
  }
  return { contactPointName };
};
const getContactPoints = async () => {
  const query = `
      getContactPoints {
        description
        emails
        id
        name
      }
    `;
  const contactPoints = await graphqlRequestHandler(
    getContactPointsConfig.name,
    query
  );
  return contactPoints;
};
const getContactPointNameByEmails = async (emailsArr) => {
  const contactPoints = await getContactPoints();
  const contactPoint = contactPoints.find(({ emails }) => {
    return emails.sort().toString() === emailsArr.sort().toString();
  });
  return contactPoint ? contactPoint.name : null;
};
const getContactPointsNameOrCreate = async (emailsArr) => {
  const existingContactPointName = await getContactPointNameByEmails(
    emailsArr
  );
  if (existingContactPointName) return existingContactPointName;

  logger.info(
    `Contact point is not present for the emails ${emailsArr}, creating new contact point`
  );
  const createdContactPoint = await createContactPoint(emailsArr.join(","));
  if (!createdContactPoint) throw new Error("Failed to create contact point");

  return createdContactPoint.contactPointName;
};
const getContactEmails = async ({ individuals = [], groups = [] }) => {
  if (!individuals.length && !groups.length) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "At least one of individuals or groups must be provided"
    );
  }

  if (individuals && individuals.length) {
    return {
      groupsEmails: [],
      individualEmails: individuals,
      isGroupEmail: false,
    };
  }
  if (!groups && !groups.length) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Group missing or Invalid group names`
    );
  }
  const groupsData = await Email_group.findAll({
    where: {
      name: Sequelize.where(Sequelize.fn("lower", Sequelize.col("name")), {
        [Op.in]: groups.map((m) => m.toLowerCase().trim()),
      }),
    },
    raw: true,
    attributes: ["name", "members", "subGroups"],
  });
  let emailsArr = [];
  groupsData.forEach((group) => {
    emailsArr = [...emailsArr, ...group.members];
  });
  const subGroupIds = groupsData.reduce((acc, cur) => {
    if (cur.subGroups) acc = [...acc, ...cur.subGroups];
    return acc;
  }, []);
  if (subGroupIds.length) {
    const subGroupsData = await Email_group.findAll({
      where: {
        id: { [Op.in]: subGroupIds },
      },
      raw: true,
      attributes: ["name", "members", "subGroups"],
    });

    subGroupsData.forEach((group) => {
      emailsArr = [...emailsArr, ...group.members];
    });
  }

  return {
    groupsEmails: emailsArr,
    individualEmails: groups.filter((f) => f.includes("@")),
    isGroupEmail: true,
  };
};
// const createAlert = async (payload) => {
//   const {
//     user,
//     id,
//     timediff,
//     timerange,
//     evaluationTimeframe,
//     runEvery,
//     filters,
//     customers,
//     suppliers,
//     destinations,
//     alertName,
//     alertType,
//     threshold,
//     minSubmissionCount,
//     resultCode,
//     errorDescription,
//     node,
//     volumeType,
//     category,
//     individuals, // contactPoints
//     groups, // contactPoints
//   } = payload;

//   const isAlertNameExist = await AlertsModel.findOne({
//     where: {
//       id: { [Op.ne]: id },
//       name: Sequelize.where(Sequelize.fn("lower", Sequelize.col("name")), {
//         [Op.eq]: alertName.toLowerCase().trim(),
//       }),
//       status: alertConfig.alertStatus.ACTIVE,
//     },
//   });

//   if (isAlertNameExist) {
//     throw new ApiError(httpStatus.CONFLICT, `Alert Name already exist.`);
//   }
//   const timezone = "UTC"; // "Asia/Kolkata"; // "UTC";

//   const fields = [];
//   const filtersConfig = {
//     customers: { field: "customer_name", values: customers },
//     suppliers: { field: "supplier", values: suppliers },
//     destinations: { field: "destination", values: destinations },
//   };

//   let _commonFilter = Object.values(filtersConfig)
//     .filter(({ values }) => values.length > 0)
//     .map(({ field, values }) => {
//       fields.push(field);
//       return `${field} IN ${singleToDoubleQuotes(JSON.stringify(values))}`;
//     })
//     .join(" AND ");

//   const webhookEnabled = true;

//   let _contactPoint;
//   if (webhookEnabled) {
//     _contactPoint = `"webhook"`;
//   } else {
//     const contactPointNames = await getContactPointsNameOrCreate(
//       contactPoint
//     );
//     _contactPoint = `"${contactPointNames}"`;
//   }

//   let triggeringExpressions;
//   let metaAdditionalType = "";
//   const {
//     delivery_failed,
//     total_submitted,
//     error_description,
//     result_code,
//     host_id,
//     delivery_success,
//     submission_success,
//     delivery_report_success,
//   } = alertApiFields;
//   const { DELIVERY_REPORT_DROP, DELIVERY_DROP, ERROR, VOLUME_DROP_OR_SPIKE } =
//     alertConfig.types;
//   switch (alertType) {
//     case DELIVERY_REPORT_DROP: {
//       fields.push(total_submitted);
//       fields.push(delivery_failed);

//       const _minSubmissionCount =
//         minSubmissionCount || `{${total_submitted}:current}`; // Defaults to today's total submissions

//       triggeringExpressions = `
//       [
//         {
//           name: "valid_submissions_today",
//           type: "math",
//           expression: "({${total_submitted}:current} || 0) >= (${_minSubmissionCount} || 0)"
//         },
//         {
//           name: "delivery_success_today",
//           type: "math",
//           expression: "(({${total_submitted}:current} || 0) - ({${delivery_failed}:current} || 0))"
//         },
//         {
//           name: "delivery_success_previous",
//           type: "math",
//           expression: "(({${total_submitted}:previous} || 0) - ({${delivery_failed}:previous} || 0))"
//         },
//         {
//           name: "percentage_difference",
//           type: "math",
//           expression: "abs((({delivery_success_today} / ({${total_submitted}:current} || 1)) * 100) - (({delivery_success_previous} / ({${total_submitted}:previous} || 1)) * 100))"
//         },
//         {
//           name: "trigger_alert",
//           type: "math",
//           expression: "{valid_submissions_today} && ({percentage_difference} > ${threshold})"
//         }
//       ]
//       `;
//       break;
//     }

//     case DELIVERY_DROP: {
//       /*
//        * Filters - total submission | submission success | submission failure | delivery success | delivery failure
//        *
//        * case 1  - (total submission | submission success | submission failure) - at a time any one can be selected
//        * case 2  - (delivery success | delivery failure) - at a time any one can be selected
//        */

//       fields.push(total_submitted);
//       fields.push(delivery_failed);

//       triggeringExpressions = `
//       [
//         {
//           name: "delivery_success_percentage_today",
//           type: "math",
//           expression: "( ({${total_submitted}:current} - {${delivery_failed}:current}) / {${total_submitted}:current} ) * 100"
//         },
//         {
//           name: "avg_delivery_success_percentage_previous",
//           type: "math",
//           expression: "avg_over_time(( ({${total_submitted}:previous} - {${delivery_failed}:previous}) / {${total_submitted}:previous} ) * 100, ${
//         timerange || "1"
//       }d )"
//         },
//         {
//           name: "delivery_success_percentage_difference",
//           type: "math",
//           expression: "abs( {delivery_success_percentage_today} - {avg_delivery_success_percentage_previous} )"
//         },
//         {
//           name: "trigger_condition",
//           type: "math",
//           expression: "{delivery_success_percentage_difference} > 10 && {delivery_success_percentage_today} < ${threshold}"
//         }
//       ]
//       `;

//       if (Array.isArray(filters) && filters.length) {
//         _commonFilter +=
//           " AND " + filters.map(decodeHtmlEntities).join(" AND ");
//       }

//       break;
//     }

//     case ERROR: {
//       fields.push(total_submitted);
//       fields.push(delivery_failed);

//       triggeringExpressions = `
//       [
//         {
//           name: "delivery_failure_percentage_today",
//           type: "math",
//           expression: "abs( (({${delivery_failed}:current}) / ({${total_submitted}:current})) * 100 )"
//         },
//         {
//           name: "trigger_condition",
//           type: "math",
//           expression: "{delivery_failure_percentage_today} > ${threshold}"
//         }
//       ]
//       `;

//       if (node.length) {
//         // fields.push(host_id);
//         const nodeStr = singleToDoubleQuotes(JSON.stringify(node));
//         _commonFilter += ` AND ${host_id} IN ${nodeStr}`;
//       }

//       if (resultCode) {
//         fields.push(result_code);
//         const resultCodeStr = singleToDoubleQuotes(
//           JSON.stringify([resultCode])
//         );
//         _commonFilter += ` AND ${result_code} IN ${resultCodeStr}`;
//       }

//       if (errorDescription) {
//         fields.push(error_description);
//         const errDescStr = singleToDoubleQuotes(
//           JSON.stringify([errorDescription])
//         );
//         _commonFilter += ` AND ${error_description} IN ${errDescStr}`;
//       }
//       break;
//     }

//     case VOLUME_DROP_OR_SPIKE: {
//       /*
//       today_total_sub = 2000, min_sub_ui = 1000, avg_previous_total_sub = 2400, threshold = 20%

//       // spike(">") | drop("<")

//       if ((today_total_sub > $min_sub_ui) || (avg_previous_total_sub > $min_sub_ui)) {
//         variation = ((today_total_sub - avg_previous_total_sub) / avg_previous_total_sub) * 100
//         if ( |-variation| > threshold ) alert
//       }
//     	*/

//       fields.push(total_submitted);
//       fields.push(delivery_report_success);

//       const isSpike = volumeType === alertConfig.volumeTypes.SPIKE;
//       const isDrop = volumeType === alertConfig.volumeTypes.DROP;

//       let condition = "";
//       if (isSpike) {
//         // Spike condition: Only check for values greater than the threshold
//         condition = `{percentage_difference} > ${threshold}`;
//         metaAdditionalType = "spike";
//       } else if (isDrop) {
//         // Drop condition: Only check for values less than the negative threshold
//         condition = `{percentage_difference} < -${threshold}`;
//         metaAdditionalType = "drop";
//       } else {
//         // Both spike and drop condition: Check for either spike or drop
//         condition = `{percentage_difference} > ${threshold} || {percentage_difference} < -${threshold}`;
//         metaAdditionalType = "both";
//       }

//       // triggeringExpressions = `
//       // [
//       //   {
//       //     name: "today_total_sub",
//       //     type: "math",
//       //     expression: "({${total_submitted}:current})"
//       //   },
//       //   {
//       //     name: "avg_previous_total_sub",
//       //     type: "math",
//       //     expression: "(({${total_submitted}:previous}) / ${timerange || 1})"
//       //   },
//       //   {
//       //     name: "percentage_difference",
//       //     type: "math",
//       //     expression: "((({today_total_sub} - {avg_previous_total_sub}) / ({avg_previous_total_sub})) * 100)"
//       //   },
//       //   {
//       //     name: "min_sub_check",
//       //     type: "math",
//       //     expression: "(({today_total_sub} > ${minSubmissionCount}) || ({avg_previous_total_sub} > ${minSubmissionCount}))"
//       //   },
//       //   {
//       //     name: "today_dlr_percentage",
//       //     type: "math",
//       //     expression: "(({${delivery_report_success}:current} / {today_total_sub}) * 100)"
//       //   },
//       //   {
//       //     name: "pre_dlr_percentage",
//       //     type: "math",
//       //     expression: "(({${delivery_report_success}:previous} / {avg_previous_total_sub}) * 100)"
//       //   },
//       //   {
//       //     name: "no_data_check",
//       //     type: "math",
//       //     expression: "(({today_total_sub} == 0) || ({avg_previous_total_sub} == 0))"
//       //   },
//       //   {
//       //     name: "trigger_alert",
//       //     type: "math",
//       //     expression: "(({min_sub_check}) && (${condition})) || ({no_data_check})"
//       //   }
//       // ]
//       // `;

//       triggeringExpressions = `
//       [
//         {
//           name: "today_total_sub",
//           type: "math",
//           expression: "sum_over_time({${total_submitted}:current} by (customer_name))"
//         },
//         {
//           name: "avg_previous_total_sub",
//           type: "math",
//           expression: "sum_over_time({${total_submitted}:previous} by (customer_name)) / ${timerange || 1}"
//         },
//         {
//           name: "percentage_difference",
//           type: "math",
//           expression: "((({today_total_sub} - {avg_previous_total_sub}) / ({avg_previous_total_sub})) * 100)"
//         },
//         {
//           name: "min_sub_check",
//           type: "math",
//           expression: "(({today_total_sub} > ${minSubmissionCount}) || ({avg_previous_total_sub} > ${minSubmissionCount}))"
//         },
//         {
//           name: "trigger_alert",
//           type: "math",
//           expression: "(({min_sub_check}) && (${condition})) by (customer_name)"
//         }
//       ]
//       `;

//       // {
//       //   name: "today_dlr_percentage",
//       //   type: "math",
//       //   expression: "(({delivery_report_success:current} / ({today_total_sub} + ({today_total_sub} == 0))) * 100)"
//       // },
//       // {
//       //   name: "pre_dlr_percentage",
//       //   type: "math",
//       //   expression: "(({delivery_report_success:previous} / ({avg_previous_total_sub} + ({avg_previous_total_sub} == 0))) * 100)"
//       // },
//       // {
//       //   name: "test",
//       //   type: "math",
//       //   expression: "({count_successful_delivery_final:current})"
//       // },
//       break;
//     }

//     default: {
//       throw new Error(`Unsupported alert type: ${alertType}`);
//     }
//   }

//   const { groupsEmails, individualEmails, isGroupEmail } =
//     await getContactEmails({ individuals, groups });
//   let contactEmails = [];
//   if (isGroupEmail) {
//     contactEmails = [...groupsEmails, ...individualEmails];
//   } else {
//     contactEmails = individualEmails;
//   }
//   let query = `
//   alertCreate(
//       comparedTo: { timediff: ${timediff}, timerange: ${timerange}, aggregate_function: "sum" }
//       contactPoint: ${_contactPoint}
//       evaluationTimeframe: ${evaluationTimeframe}
//       fields: ${JSON.stringify(fields)}
//       name: "${alertName}"
//       runEvery: ${runEvery}
//       timezone: "${timezone}"
//       triggeringExpressions: ${triggeringExpressions}
//       commonFilter: "${_commonFilter}"
//       currentFilter: null
//       comparedFilter: null
//       metadata: {
//           max_allowed_percentage_change: "${threshold}%",
//           meta_alert_type: "${alertType}",
//           meta_contact_point: "${contactEmails.toString()}",
//           meta_additional_type: "${metaAdditionalType}",
//           customer_name: "{customer_name}"  // ✅ Add customer name to metadata

//       }
//     ) {
//       common_filter
//       compared_filter
//       compared_to {
//         aggregate_function
//         timediff
//         timerange
//       }
//       contact_point
//       current_filter
//       evaluation_timeframe
//       fields
//       id
//       metadata
//       name
//       run_every
//       timezone
//       triggering_expression
//     }
//   `;
//   console.log("query-.->", query);
//   const res = await graphqlRequestHandler(
//     createAlertConfig.name,
//     query,
//     gqlOp.mutation
//   );

//   const createAlertPayload = {
//     ...lodash.pick(res, [
//       "evaluation_timeframe",
//       "id",
//       "name",
//       "run_every",
//       "timezone",
//       "triggering_expression",
//     ]),
//     filters: {
//       customers: customers,
//       suppliers: suppliers,
//       destinations: destinations,
//       minSubmissionCount: minSubmissionCount,
//       threshold: threshold,
//       resultCode: resultCode,
//       errorDescription: errorDescription,
//     },
//     metadata: res.metadata,
//     compared_timediff: res.compared_to.timediff,
//     compared_timerange: res.compared_to.timerange,
//     compared_aggregate_function: res.compared_to.aggregate_function,
//     created_by: lodash.get(user, "id", "1"),
//     category: category,
//     status: alertConfig.alertStatus.ACTIVE,
//     contact_point: {
//       individuals: individualEmails,
//       groups: groups,
//     },
//   };

//   console.log(`Create alert payload : `, createAlertPayload);
//   const options = {};
//   if (payload._rollbackTxn) options.transaction = payload._rollbackTxn;
//   const SMSHubAlert = await AlertsModel.create(createAlertPayload, options);
//   return { message: "Alert created successfully!", res, SMSHubAlert };
// };


const createAlert = async (payload) => {
  const {
    user,
    id,
    timediff,
    timerange = 1,
    evaluationTimeframe,
    runEvery,
    filters,
    customers,
    suppliers,
    destinations,
    alertName,
    alertType,
    threshold,
    minSubmissionCount,
    resultCode,
    errorDescription,
    errorCode,
    node,
    volumeType,
    category,
    individuals, // contactPoints
    groups, // contactPoints
    deviation
  } = payload;

  console.log("Cretae alert")

  const isAlertNameExist = await AlertsModel.findOne({
    where: {
      id: { [Op.ne]: id },
      name: Sequelize.where(Sequelize.fn("lower", Sequelize.col("name")), {
        [Op.eq]: alertName.toLowerCase().trim(),
      }),
      status: alertConfig.alertStatus.ACTIVE,
    },
  });


  if (isAlertNameExist) {
    throw new ApiError(httpStatus.CONFLICT, `Alert Name already exists.`);
  }

  const timezone = "UTC";

  const filtersConfig = {
    customers: { field: "customer_name", values: customers },
    suppliers: { field: "supplier", values: suppliers },
    destinations: { field: "destination", values: destinations },
  };

  const filterCombinations = generateFilterCombinations(filtersConfig);
  console.log("filterCombinations", filterCombinations);


  // const alertCreationPromises = filterCombinations.map(async (filterCombination) => {
  // const { customers: currentCustomers, suppliers: currentSuppliers, destinations: currentDestinations, combined_name } = filterCombination;

  const fields = [];
  let _commonFilter = Object.values({
    customers: { field: "customer_name", values: customers },
    suppliers: { field: "supplier", values: suppliers },
    destinations: { field: "destination", values: destinations },
  })
    .filter(({ values }) => values.length > 0)
    .map(({ field, values }) => {
      fields.push(field);
      return `${field} IN ${singleToDoubleQuotes(JSON.stringify(values))}`;
    })
    .join(" AND ");

  console.log("_commonFilter->", _commonFilter)

  const webhookEnabled = true; // Assuming this is always true

  let _contactPoint;
  if (webhookEnabled) {
    _contactPoint = `"webhook"`;
  } else {
    const contactPointNames = await getContactPointsNameOrCreate(contactPoint);
    _contactPoint = `"${contactPointNames}"`;
  }

  let triggeringExpressions;
  let metaAdditionalType = "";

  const {
    delivery_failed,
    total_submitted,
    error_description,
    result_code,
    host_id,
    delivery_success,
    submission_success,
    delivery_report_success,
    delivery_report_failed
  } = alertApiFields; // Make sure alertApiFields is defined

  const { DELIVERY_REPORT_DROP, DELIVERY_DROP, ERROR, VOLUME_DROP_OR_SPIKE } = alertConfig.types; // Make sure alertConfig is defined

  let haveFilter = ""; // Initialize haveFilter here

  switch (alertType) {
    case DELIVERY_REPORT_DROP: {
      fields.push(total_submitted);
      fields.push(delivery_report_success);
      fields.push(delivery_report_failed);

      const _minSubmissionCount =
        minSubmissionCount || `{${total_submitted}:current}`; // Defaults to today's total submissions

      triggeringExpressions = `
          [
            {
              name: "valid_submissions_today",
              type: "math",
              expression: "({${total_submitted}:current} || 0) >= (${_minSubmissionCount} || 0)"
            },
            {
              name: threshold_percentage_today,
              type: "math",
              expression: "({delivery_report_success_current} / ({delivery_report_success_current} + ({${delivery_report_failed}:current} || 0))) * 100"
            },
            {
              name: threshold_percentage_previous,
              type: "math",
              expression: "({delivery_report_success_previous} / ({delivery_report_success_previous} + ({${delivery_report_failed}:previous} || 0))) * 100"
            },
            {
              name: "percentage_difference",
              type: "math",
              expression: "abs({threshold_percentage_today} - {threshold_percentage_previous})"
            },
            {
              name: "trigger_alert",
              type: "math",
              expression: "{valid_submissions_today} && ({percentage_difference} > ${threshold})"
            }
          ]
          `;
      break;
    }

    case DELIVERY_DROP: {
      /*
       * Filters - total submission | submission success | submission failure | delivery success | delivery failure
       *
       * case 1  - (total submission | submission success | submission failure) - at a time any one can be selected
       * case 2  - (delivery success | delivery failure) - at a time any one can be selected
       */

      fields.push(total_submitted);
      fields.push(delivery_failed);

      triggeringExpressions = `
        [
          {
            name: "delivery_success_percentage_today",
            type: "math",
            expression: "( ({${total_submitted}:current} - {${delivery_failed}:current}) / {${total_submitted}:current} ) * 100"
          },
          {
            name: "delivery_success_percentage_difference",
            type: "math",
            expression: "abs( {delivery_success_percentage_today} - {avg_delivery_success_percentage_previous})"
          }
          {
            name: "trigger_condition",
            type: "math",
            expression: " {delivery_success_percentage_difference} > ${deviation} && {delivery_success_percentage_today} < ${threshold}"
          }
        ]
        `;

      if (Array.isArray(filters) && filters.length) {
        // _commonFilter +=
        //   " AND " + filters.map(decodeHtmlEntities).join(" AND ");
        haveFilter = await buildWhereClauseFromFilters({ filters });
      }
      break;
    }

    case ERROR: {
      fields.push(total_submitted);
      fields.push(delivery_failed);

      // const errorPayload = {
      //   failure_error: errorDescription
      // }

      // console.log("errorPayload->", errorPayload)

      // const errorDetails = await getErrorDescriptionMappingDetails(errorPayload);

      triggeringExpressions = `
          [
            {
              name: valid_submissions_today,
              type: "math",
              expression: "({${total_submitted}:current} > 0)"
            },
            {
              name: "delivery_failure_percentage_today",
              type: "math",
              expression: "abs( (({${delivery_failed}:current}) / ({${total_submitted}:current})) * 100 )"
            },
            {
              name: "trigger_condition",
              type: "math",
              expression: "{valid_submissions_today} && {delivery_failure_percentage_today} > ${threshold}"
            }
          ]
          `;

      if (node.length) {
        // fields.push(host_id);
        const nodeStr = singleToDoubleQuotes(JSON.stringify(node));
        _commonFilter += ` AND ${host_id} IN ${nodeStr}`;
      }

      if (resultCode) {
        fields.push(result_code);
        const resultCodeStr = singleToDoubleQuotes(
          JSON.stringify([resultCode])
        );
        _commonFilter += ` AND ${result_code} IN ${resultCodeStr}`;
      }

      if (errorDescription) {
        fields.push(error_description);
        const errDescStr = singleToDoubleQuotes(
          JSON.stringify([errorDescription])
        );
        _commonFilter += ` AND ${error_description} IN ${errDescStr}`;
      }
      break;
    }

    case VOLUME_DROP_OR_SPIKE: {
      fields.push(total_submitted);
      fields.push(delivery_success);

      const isSpike = volumeType === alertConfig.volumeTypes.SPIKE;
      const isDrop = volumeType === alertConfig.volumeTypes.DROP;

      let condition = "";
      if (isSpike) {
        condition = `{percentage_difference} > ${threshold}`;
        metaAdditionalType = "spike";
      } else if (isDrop) {
        condition = `{percentage_difference} < -${threshold}`;
        metaAdditionalType = "drop";
      } else {
        condition = `{percentage_difference} > ${threshold} || {percentage_difference} < -${threshold}`;
        metaAdditionalType = "both";
      }

      triggeringExpressions = `
            [
              {
                name: "percentage_difference",
                type: "math",
                expression: "((({total_submissions_previous} - {total_submissions_current}) / ({total_submissions_previous})) * 100)"
              },
              {
                name: "min_sub_check",
                type: "math",
                expression: "(({total_submissions_current} > ${minSubmissionCount}) || ({total_submissions_previous} > ${minSubmissionCount}))"
                },
                {
                name: "today_dlr_percentage",
                type: "math",
                expression: "(({${delivery_success}:current} / {total_submissions_current}) * 100)"
              },
              {
                name: "pre_dlr_percentage",
                type: "math",
                expression: "(({${delivery_success}:previous} / {total_submissions_previous}) * 100)"
              },
              {
                name: "no_data_check",
                type: "math",
                expression: "(({total_submissions_current} == 0) || ({total_submissions_previous} == 0))"
              },
              {
                name: "trigger_alert",
                type: "math",
                expression: "(({min_sub_check}) && (${condition})) || ({no_data_check})"
              }
            ]
          `;
      break;
    }

    default: {
      throw new Error(`Unsupported alert type: ${alertType}`);
    }
  }

  const { groupsEmails, individualEmails, isGroupEmail } = await getContactEmails({ individuals, groups }); // Make sure getContactEmails is defined
  let contactEmails;
  if (isGroupEmail) {
    contactEmails = [...groupsEmails, ...individualEmails];
  } else {
    contactEmails = individualEmails;
  }

  // const alertNameWithFilters = `${alertName}-${combined_name}`;
  // const customerWiseCommonFilter = `{
  //   ${combined_name}:"${_commonFilter}"
  //   }`;

  const customerWiseCommonFilter = `{
        combined_name:"${_commonFilter}"
        }`;

  let query = `
      alertCreate(
          comparedTo: { timediff: ${timediff}, timerange: ${timerange}, aggregate_function: "sum" }
          contactPoint: ${_contactPoint}
          evaluationTimeframe: ${evaluationTimeframe}
          fields: ${JSON.stringify(fields)}
          name: "${alertName}"
          runEvery: ${runEvery}
          timezone: "${timezone}"
          triggeringExpressions: ${triggeringExpressions}
          commonFilter: "${_commonFilter}"
          haveFilter: "${haveFilter}"
          customerWiseCommonFilter: ${customerWiseCommonFilter}
          currentFilter: null
          comparedFilter: null
          metadata: {
              max_allowed_percentage_change: "${threshold}%",
              meta_alert_type: "${alertType}",
              meta_contact_point: "${contactEmails.toString()}",
              meta_additional_type: "${metaAdditionalType}",
              every_run_minutes: "${runEvery}",
              result_code: "${resultCode}",
              error_code: "${errorCode}",
              error_description: "${errorCode} ${errorDescription}",
              node: "${node}",
              volumeType: "${volumeType}",
              triggered_at: "{{ now }}"
          }
        ) {
          common_filter
          compared_filter
          customer_wise_common_filter
          compared_to {
            aggregate_function
            timediff
            timerange
          }
          contact_point
          current_filter
          evaluation_timeframe
          fields
          id
          metadata
          name
          run_every
          timezone
          triggering_expression
        }
      `;

  let res;
  try {
    res = await graphqlRequestHandler(createAlertConfig.name, query, gqlOp.mutation);
  } catch (error) {
    if (error.message && error.message.includes("a conflicting alert rule is found")) {
      throw new ApiError(httpStatus.CONFLICT, "Alert name already exist");
    }
    throw error;
  }
  const createAlertPayload = {
    ...lodash.pick(res, [
      "evaluation_timeframe",
      "id",
      "name",
      "run_every",
      "timezone",
      "triggering_expression",
    ]),
    filters: {
      customers,
      suppliers,
      destinations,
      minSubmissionCount: minSubmissionCount,
      threshold: threshold,
      resultCode: resultCode,
      errorDescription: errorDescription,
      node: node,
      deviation,
      conditions: filters
    },
    metadata: res.metadata,
    compared_timediff: res.compared_to.timediff,
    compared_timerange: res.compared_to.timerange,
    compared_aggregate_function: res.compared_to.aggregate_function,
    created_by: payload.created_by ? payload.created_by : lodash.get(user, "name", "1"),
    category: category,
    status: alertConfig.alertStatus.ACTIVE,
    updated_by: lodash.get(user, "name", "1"),
    contact_point: {
      individuals: individualEmails,
      groups: groups,
    },
  };

  const options = {};
  if (payload._rollbackTxn) options.transaction = payload._rollbackTxn;
  const SMSHubAlert = await AlertsModel.create(createAlertPayload, options);
  return { message: "Alert created successfully!", res, SMSHubAlert };
  // });

  // const results = await Promise.all(alertCreationPromises);
  // return { message: "Alerts created successfully!", results };
};

// Helper function to generate all combinations of filters (including destinations)
function generateFilterCombinations(filtersConfig) {
  const combinations = [];
  const customers = filtersConfig.customers.values;
  const suppliers = filtersConfig.suppliers.values;
  const destinations = filtersConfig.destinations.values;

  if (customers.length === 0 && suppliers.length === 0 && destinations.length === 0) {
    return []; // Return an empty array instead of undefined
  }

  function formatName({ customer = null, supplier = null, destination = null }) {
    const formattedCustomer = customer ? customer.replace(/[.\s]+/g, '_').toLowerCase() : '';
    const formattedSupplier = supplier ? supplier.replace(/[.\s]+/g, '_').toLowerCase() : '';
    const formattedDestination = destination ? destination.replace(/[.\s]+/g, '_').toLowerCase() : '';

    // Construct combined name dynamically
    const combinedNameParts = [];
    if (formattedCustomer) combinedNameParts.push(`cust_${formattedCustomer}`);
    if (formattedSupplier) combinedNameParts.push(`supp_${formattedSupplier}`);
    if (formattedDestination) combinedNameParts.push(`dest_${formattedDestination}`);

    return combinedNameParts.join('_') || ''; // Ensures no trailing underscore
  }

  if (customers.length > 0 && suppliers.length === 0 && destinations.length === 0) {
    return customers.map(customer => ({
      customers: [customer],
      suppliers,
      destinations,
      combined_name: formatName({ customer }),
    }));
  }

  if (customers.length === 0 && suppliers.length > 0 && destinations.length === 0) {
    return suppliers.map(supplier => ({
      customers,
      suppliers: [supplier],
      destinations,
      combined_name: formatName({ supplier }),
    }));
  }

  if (customers.length === 0 && suppliers.length === 0 && destinations.length > 0) {
    return destinations.map(destination => ({
      customers,
      suppliers,
      destinations: [destination],
      combined_name: formatName({ destination }),
    }));
  }

  if (customers.length > 0 && suppliers.length > 0 && destinations.length > 0) {
    for (const customer of customers) {
      for (const supplier of suppliers) {
        for (const destination of destinations) {
          combinations.push({
            customers: [customer],
            suppliers: [supplier],
            destinations: [destination],
            combined_name: formatName({ customer, supplier, destination }),
          });
        }
      }
    }
  } else if (customers.length > 0 && suppliers.length > 0 && destinations.length === 0) {
    for (const customer of customers) {
      for (const supplier of suppliers) {
        combinations.push({
          customers: [customer],
          suppliers: [supplier],
          destinations,
          combined_name: formatName({ customer, supplier }),
        });
      }
    }
  } else if (customers.length > 0 && suppliers.length === 0 && destinations.length > 0) {
    for (const customer of customers) {
      for (const destination of destinations) {
        combinations.push({
          customers: [customer],
          suppliers,
          destinations: [destination],
          combined_name: formatName({ customer, destination }),
        });
      }
    }
  } else if (customers.length === 0 && suppliers.length > 0 && destinations.length > 0) {
    for (const supplier of suppliers) {
      for (const destination of destinations) {
        combinations.push({
          customers,
          suppliers: [supplier],
          destinations: [destination],
          combined_name: formatName({ supplier, destination }),
        });
      }
    }
  }

  return combinations;
};

// const createAlert = async (payload) => {
//   const {
//     user,
//     id,
//     timediff,
//     timerange,
//     evaluationTimeframe,
//     runEvery,
//     filters,
//     customers,
//     suppliers,
//     destinations,
//     alertName,
//     alertType,
//     threshold,
//     minSubmissionCount,
//     resultCode,
//     errorDescription,
//     node,
//     volumeType,
//     category,
//     individuals,
//     groups,
//   } = payload;

//   await validateAlertName(alertName);
//   const fields = [];

//   const filtersConfig = [
//     { key: "customers", field: "customer_name", values: customers },
//     { key: "suppliers", field: "supplier", values: suppliers },
//     { key: "destinations", field: "destination", values: destinations },
//   ].filter(({ values }) => values.length > 0);

//   console.log("filtersConfig->", filtersConfig)

//   let _commonFilter = Object.values(filtersConfig)
//     .filter(({ values }) => values.length > 0)
//     .map(({ field, values }) => {
//       fields.push(field);
//       return `${field} IN ${singleToDoubleQuotes(JSON.stringify(values))}`;
//     })
//     .join(" AND ");

//     console.log("_commonFilter->", _commonFilter)
//     console.log("fields->", fields)

//   const timezone = "UTC";
//   const webhookEnabled = true;
//   // const _contactPoint = webhookEnabled ? `"webhook"` : await getContactPointsNameOrCreate(
//   //   contactPoint
//   // );
//   const contactEmails = await getContactPoint(individuals, groups);

//   let _contactPoint;
//   if (webhookEnabled) {
//     _contactPoint = `"webhook"`;
//   } else {
//     const contactPointNames = await getContactPointsNameOrCreate(
//       contactPoint
//     );
//     _contactPoint = `"${contactPointNames}"`;
//   }

//   // Loop through each customer and create separate alerts
//   for (const customer of customers) {
//     const query = buildAlertQuery({
//       customer,
//       timediff,
//       timerange,
//       evaluationTimeframe,
//       runEvery,
//       filters,
//       suppliers,
//       destinations,
//       alertName,
//       alertType,
//       threshold,
//       minSubmissionCount,
//       resultCode,
//       errorDescription,
//       node,
//       volumeType,
//       _contactPoint,
//       contactEmails,
//       timezone,
//       fields
//     });

//     console.log("Generated Query: ", query);

//     // Send request to Grafana or alert service (Uncomment in production)
//     const res = await graphqlRequestHandler(createAlertConfig.name, query, gqlOp.mutation);

//     const createAlertPayload = buildAlertPayload(payload, res);
//     await saveAlert(createAlertPayload, payload._rollbackTxn);
//   }

//   return { message: "Alerts created successfully for each customer!" };
// };

// // Validate if the alert name already exists
// const validateAlertName = async (alertName, id) => {
//   const isAlertNameExist = await AlertsModel.findOne({
//     where: {
//       id: { [Op.ne]: id },
//       name: Sequelize.where(Sequelize.fn("lower", Sequelize.col("name")), {
//         [Op.eq]: alertName.toLowerCase().trim(),
//       }),
//       status: alertConfig.alertStatus.ACTIVE,
//     },
//   });

//   if (isAlertNameExist) {
//     throw new ApiError(httpStatus.CONFLICT, `Alert Name already exists.`);
//   }
// };

// // Get contact points for alerts
// const getContactPoint = async (individuals, groups) => {
//   const { groupsEmails, individualEmails, isGroupEmail } = await getContactEmails({ individuals, groups });
//   const contactEmails = isGroupEmail ? [...groupsEmails, ...individualEmails] : individualEmails;
//   return `${contactEmails.toString()}`;
// };

// // Build the GraphQL query for creating an alert
// const buildAlertQuery = ({
//   customer,
//   timediff,
//   timerange,
//   evaluationTimeframe,
//   runEvery,
//   filters,
//   alertName,
//   alertType,
//   threshold,
//   minSubmissionCount,
//   resultCode,
//   errorDescription,
//   node,
//   volumeType,
//   _contactPoint,
//   contactEmails,
//   timezone,
//   fields
// }) => {
//   const _commonFilter = `customer_name IN ('${customer}')`;

//   // Get triggering expressions and fields dynamically
//   const { triggeringExpressions, metaAdditionalType } = getTriggeringExpressions(
//     alertType,
//     threshold,
//     fields,
//     filters,
//     minSubmissionCount,
//     timerange,
//     node,
//     resultCode,
//     errorDescription,
//     volumeType,
//     _commonFilter,
//   );

//   return `
//     alertCreate(
//       comparedTo: { timediff: ${timediff}, timerange: ${timerange}, aggregate_function: "sum" }
//       contactPoint: ${_contactPoint}
//       evaluationTimeframe: ${evaluationTimeframe}
//       fields: ${JSON.stringify(fields)}
//       name: "Alert_${customer}_${alertName}"
//       runEvery: ${runEvery}
//       timezone: "${timezone}"
//       triggeringExpressions: ${triggeringExpressions}
//       commonFilter: "${_commonFilter}"
//       currentFilter: null
//       comparedFilter: null
//       metadata: {
//         max_allowed_percentage_change: "${threshold}%",
//         meta_alert_type: "${alertType}",
//         meta_contact_point: "${contactEmails.toString()}",
//         meta_additional_type: "${metaAdditionalType}",
//       }
//     ) {
//       common_filter
//       compared_filter
//       compared_to {
//         aggregate_function
//         timediff
//         timerange
//       }
//       contact_point
//       current_filter
//       evaluation_timeframe
//       fields
//       id
//       metadata
//       name
//       run_every
//       timezone
//       triggering_expression
//     }
//   `;
// };

// // Get triggering expressions based on alert type
// const getTriggeringExpressions = (
//   alertType,
//   threshold,
//   fields,
//   filters,
//   minSubmissionCount,
//   timerange,
//   node,
//   resultCode,
//   errorDescription,
//   volumeType,
//   _commonFilter
// ) => {
//   const { DELIVERY_REPORT_DROP, DELIVERY_DROP, ERROR, VOLUME_DROP_OR_SPIKE } = alertConfig.types;
//   const { delivery_failed, total_submitted, result_code, error_description, delivery_report_success } = alertApiFields;
//   let triggeringExpressions = "";
//   let metaAdditionalType = "";

//   switch (alertType) {
//     case DELIVERY_REPORT_DROP: {
//       fields.push(total_submitted, delivery_failed);
//       const _minSubmissionCount = minSubmissionCount || `{${total_submitted}:current}`;

//       triggeringExpressions = `
//       [
//         {
//           name: "valid_submissions_today",
//           type: "math",
//           expression: "({${total_submitted}:current} || 0) >= (${_minSubmissionCount} || 0)"
//         },
//         {
//           name: "delivery_success_today",
//           type: "math",
//           expression: "(({${total_submitted}:current} || 0) - ({${delivery_failed}:current} || 0))"
//         },
//         {
//           name: "delivery_success_previous",
//           type: "math",
//           expression: "(({${total_submitted}:previous} || 0) - ({${delivery_failed}:previous} || 0))"
//         },
//         {
//           name: "percentage_difference",
//           type: "math",
//           expression: "abs((({delivery_success_today} / ({${total_submitted}:current} || 1)) * 100) - (({delivery_success_previous} / ({${total_submitted}:previous} || 1)) * 100))"
//         },
//         {
//           name: "trigger_alert",
//           type: "math",
//           expression: "{valid_submissions_today} && ({percentage_difference} > ${threshold})"
//         }
//       ]
//       `;
//       break;
//     }

//     case DELIVERY_DROP: {
//       fields.push(total_submitted, delivery_failed);

//       triggeringExpressions = `
//       [
//         {
//           name: "delivery_success_percentage_today",
//           type: "math",
//           expression: "( ({${total_submitted}:current} - {${delivery_failed}:current}) / {${total_submitted}:current} ) * 100"
//         },
//         {
//           name: "avg_delivery_success_percentage_previous",
//           type: "math",
//           expression: "avg_over_time(( ({${total_submitted}:previous} - {${delivery_failed}:previous}) / {${total_submitted}:previous} ) * 100, ${timerange || "1"}d )"
//         },
//         {
//           name: "delivery_success_percentage_difference",
//           type: "math",
//           expression: "abs( {delivery_success_percentage_today} - {avg_delivery_success_percentage_previous} )"
//         },
//         {
//           name: "trigger_condition",
//           type: "math",
//           expression: "{delivery_success_percentage_difference} > 10 && {delivery_success_percentage_today} < ${threshold}"
//         }
//       ]
//       `;
//       if (Array.isArray(filters) && filters.length) {
//         _commonFilter +=
//           " AND " + filters.map(decodeHtmlEntities).join(" AND ");
//       }
//       break;
//     }

//     case ERROR: {
//       fields.push(total_submitted, delivery_failed);

//       triggeringExpressions = `
//       [
//         {
//           name: "delivery_failure_percentage_today",
//           type: "math",
//           expression: "abs( (({${delivery_failed}:current}) / ({${total_submitted}:current})) * 100 )"
//         },
//         {
//           name: "trigger_condition",
//           type: "math",
//           expression: "{delivery_failure_percentage_today} > ${threshold}"
//         }
//       ]
//       `;

//       if (node.length) {
//         fields.push("host_id");
//       }

//       if (resultCode) {
//         fields.push(result_code);
//       }

//       if (errorDescription) {
//         fields.push(error_description);
//       }
//       break;
//     }

//     case VOLUME_DROP_OR_SPIKE: {
//       fields.push(total_submitted, delivery_report_success);
//       const isSpike = volumeType === alertConfig.volumeTypes.SPIKE;
//       const isDrop = volumeType === alertConfig.volumeTypes.DROP;
//       let condition = "";

//       if (isSpike) {
//         condition = `{percentage_difference} > ${threshold}`;
//         metaAdditionalType = "spike";
//       } else if (isDrop) {
//         condition = `{percentage_difference} < -${threshold}`;
//         metaAdditionalType = "drop";
//       } else {
//         condition = `{percentage_difference} > ${threshold} || {percentage_difference} < -${threshold}`;
//         metaAdditionalType = "both";
//       }

//       triggeringExpressions = `
//       [
//         {
//           name: "today_total_sub",
//           type: "math",
//           expression: "({${total_submitted}:current})"
//         },
//         {
//           name: "avg_previous_total_sub",
//           type: "math",
//           expression: "(({${total_submitted}:previous}) / ${timerange || 1})"
//         },
//         {
//           name: "percentage_difference",
//           type: "math",
//           expression: "((({today_total_sub} - {avg_previous_total_sub}) / ({avg_previous_total_sub})) * 100)"
//         },
//         {
//           name: "min_sub_check",
//           type: "math",
//           expression: "(({today_total_sub} > ${minSubmissionCount}) || ({avg_previous_total_sub} > ${minSubmissionCount}))"
//         },
//         {
//           name: "trigger_alert",
//           type: "math",
//           expression: "(({min_sub_check}) && (${condition}))"
//         }
//       ]
//       `;
//       break;
//     }

//     default:
//       throw new Error(`Unsupported alert type: ${alertType}`);
//   }

//   return { triggeringExpressions, fields, metaAdditionalType };
// };


// // Build the alert payload to be stored in DB
// const buildAlertPayload = (payload, res) => ({
//   ...lodash.pick(res, ["evaluation_timeframe", "id", "name", "run_every", "timezone", "triggering_expression"]),
//   filters: {
//     customers: payload.customers,
//     suppliers: payload.suppliers,
//     destinations: payload.destinations,
//     minSubmissionCount: payload.minSubmissionCount,
//     threshold: payload.threshold,
//     resultCode: payload.resultCode,
//     errorDescription: payload.errorDescription,
//   },
//   metadata: res.metadata,
//   compared_timediff: res.compared_to.timediff,
//   compared_timerange: res.compared_to.timerange,
//   compared_aggregate_function: res.compared_to.aggregate_function,
//   created_by: lodash.get(payload.user, "id", "1"),
//   category: payload.category,
//   status: alertConfig.alertStatus.ACTIVE,
//   contact_point: {
//     individuals: payload.individualEmails,
//     groups: payload.groups,
//   },
// });

// // Save the alert in the database
// const saveAlert = async (createAlertPayload, transaction) => {
//   const options = {};
//   if (transaction) options.transaction = transaction;
//   await AlertsModel.create(createAlertPayload, options);
// };


const getAlerts = async (req) => {
  // let query = `
  //   alertsGet {
  //     id
  //     metadata
  //     name
  //     run_every
  //     timezone
  //     triggering_expression
  //     fields
  //     evaluation_timeframe
  //     current_filter
  //     contact_point
  //     compared_filter
  //     common_filter
  //     compared_to {
  //       aggregate_function
  //       timediff
  //       timerange
  //     }
  //   }`;

  // const alerts = await graphqlRequestHandler(
  //   getAlertsConfig.name,
  //   query
  // );
  // return alerts;

  const { limit, offset, page } = usePageLimit(req);

  const defaultSort = [["created_at", "DESC"]];
  const sorting = await getSortOrderList(
    req.query.sortBy,
    req.query.orderBy,
    defaultSort
  );

  const searchQuery = req.query.search || "";

  const whereClause = {
    status: alertConfig.alertStatus.ACTIVE,
  };

  if (searchQuery) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'name'
      { category: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
      Sequelize.where(
        Sequelize.json("metadata.meta_alert_type"),
        { [Op.like]: `%${searchQuery}%` } // Search within JSON field
      ),
    ];
  }
  const result = await AlertsModel.findAndCountAll({
    where: whereClause,
    attributes: [
      "name",
      "id",
      "created_at",
      "created_by",
      "updated_at",
      "updated_by",
      [Sequelize.json("metadata.meta_alert_type"), "alert_type"],
      "category",
      "status",
    ],
    order: sorting,
    limit,
    offset,
  });
  if (result) {
    result.page = page;
    result.limit = limit;
  }
  return result;
};
const getAlertById = async (req) => {
  const { id: alertId } = req.params;

  const alert = await AlertsModel.findOne({
    where: {
      id: alertId,
    },
    raw: true,
    // attributes: [
    //   "name",
    //   "id",
    //   "created_at",
    //   'created_by',
    //   'updated_at',
    //   'updated_by',
    //   [Sequelize.json("metadata.meta_alert_type"), "alert_type"],
    //   "category",
    //   "status"
    // ],
  });
  if (!alert) {
    throw new ApiError(httpStatus.NOT_FOUND, "Alert not found");
  }
  if (alert && alert.status === alertConfig.alertStatus.INACTIVE) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      `Alert with id ${alertId} is inactive`
    );
  }
  const result = lodash.omit(alert, [
    "created_by",
    "updated_by",
    "fields",
    "alert_timezone",
    "updated_at",
    "triggering_expression",
  ]);

  const contactPoint = lodash.get(result, "contact_point", {});
  const individualEmails = lodash.get(contactPoint, "individuals", []);
  const groupsEmails = lodash.get(contactPoint, "groups", []);
  if (groupsEmails.length) {
    result.contact_point = {
      groupsEmails: [...new Set([...groupsEmails, ...individualEmails])],
      individualEmails: [],
    };
  }
  return result;
};
const updateAlert = async (req) => {
  const { id } = req.params;
  const options = {};
  if (req._rollbackTxn) options.transaction = req._rollbackTxn;

  // Fetch original alert to get created_by
  const originalAlert = await AlertsModel.findOne({ where: { id }, raw: true });

  // console.log("originalAlert: ", originalAlert);

  await AlertsModel.update(
    { status: alertConfig.alertStatus.INACTIVE },
    { where: { id }, ...options }
  );
  console.log("alert db");
  await deleteAlert({
    params: { id },
    _rollbackTxn: req._rollbackTxn,
    user: req.user,
  });

  // Pass original created_by to createAlert payload
  await createAlert({
    ...req.body,
    user: req.user,
    created_by: originalAlert ? originalAlert.created_by : undefined,
    _rollbackTxn: req._rollbackTxn,
  });

  return `Updated successfully`;
};
const deleteAlert = async (req) => {
  const { id: alertId } = req.params;

  const options = {};
  if (req._rollbackTxn) options.transaction = req._rollbackTxn;
  await AlertsModel.update(
    {
      status: alertConfig.alertStatus.INACTIVE,
      updated_at: new Date(),
      updated_by: req.user.id,
    },
    { where: { id: alertId }, ...options }
  );
  await AlertsHistoryModel.update(
    {
      status: alertConfig.alertsHistoryStatus.DELETED,
      updated_at: new Date(),
      updated_by: req.user.id,
    },
    { where: { alert_id: alertId }, ...options }
  );

  let query = `
     alertDelete(alertId: ${alertId})
    `;

  const alert = await graphqlRequestHandler(
    deleteAlertConfig.name,
    query,
    gqlOp.mutation
  );

  await AlertsModel.update(
    {
      status: alertConfig.alertStatus.DELETED,
      updated_at: new Date(),
      updated_by: req.user.id,
    },
    { where: { id: alertId }, ...options }
  );
  return {
    message: `Alert with id ${alertId} deleted successfully`,
    name: deleteAlertConfig.name,
  };
};
const getAlertsMetadata = async (req) => {
  const { keys } = req.params;
  const configMeta = {
    volumeTypesOptions: Object.values(alertConfig.volumeTypesOptions).map(
      (m) => ({ label: m, value: m })
    ),
    alertFilterOptions: Object.keys(alertConfig.filterOptions).reduce(
      (acc, key) => {
        const arr = alertConfig.filterOptions[key];
        acc[key] = arr.map((m) => ({ label: m, value: m }));
        return acc;
      },
      {}
    ),
    alertTypesOptions: Object.values(alertConfig.types).map((m) => ({
      label: m,
      value: m,
    })),
    timePeriodOptions: alertConfig.timePeriodOptions,
    timeIntervalOptions: alertConfig.timeIntervalOptions,
  };

  const keysArr = keys.split(",");
  const metadata = lodash.pick(configMeta, keysArr, {});
  metadata.keys = Object.keys(configMeta);

  return metadata;
};
const getErrorDescriptionMappingDetails = async (payload) => {
  let filter = ""; // (failure_result:"2" failureError: "32")
  let filterObj = lodash.pick(payload, ["failureError", "errorDescription", "errorCode"]);
  if (Object.keys(filterObj).length) {
    filter = `(${Object.entries(filterObj)
      .map(
        ([key, value]) =>
          `${getErrDesMappingConfig.reqFields[key]}: "${value}"`
      )
      .join(" ")})`;
  }
  let query = `
      ${getErrDesMappingConfig.name}${filter} {
        error_code
        error_description
        failure_error
        failure_result
      }
   `;

  console.log("query: ", query);
  const errorMapping = await graphqlRequestHandler(
    getErrDesMappingConfig.name,
    query,
    gqlOp.query
  );

  console.log("errorMapping: ", errorMapping);

  return errorMapping;
};
const getErrorDescriptionList = async (req) => {
  const mapping = await getErrorDescriptionMappingDetails();
  console.log("mapping: ", mapping);
  return mapping.map(({ error_code, error_description, failure_error }) => ({
    label: `${error_code} ${error_description}`,
    failureError: failure_error,
    errorCode: error_code,
    errorDescription: error_description,
    // value: error_description
  }));
};
const getNodeList = () => {
  return nodesConfig;
};

const getResultCodeList = async (req) => {
  const payload = req.query || {};
  const mapping = await getErrorDescriptionMappingDetails(payload);
  return mapping.map(({ failure_result }) => ({
    label: failure_result,
    value: failure_result,
  }));
};
const getAlertNameList = async (req) => {

  const sortBy = [];

  if (req.query.sortBy) {
    // Accept formats like "timestamp:desc" or "timestamp:asc"
    const [field, direction] = req.query.sortBy.split(":");
    sortBy.push([field, (direction || "DESC").toUpperCase()]);
  }

  const { _endDate, _startDate } = await getOrDefaultDateRange(req);
  console.log("_endDate, _startDate: ", _endDate, _startDate);
  const whereClause = {
    status: {
      [Op.notIn]: [alertConfig.alertsHistoryStatus.DELETED],
    },
    state: { [Op.in]: [alertGrafanaStates.ALERTING] },
    timestamp: {
      [Op.between]: [_startDate, _endDate],
    },
  };
  if (req.query.status) {
    whereClause.status = {
      [Op.in]: req.query.status.split(",").map((m) => m.trim()),
    };
  }

  const searchQuery = req.query.search || "";

  if (searchQuery) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'name'
      { category: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
      { alert_type: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
    ];
  }


  const alertsHistory = await AlertsHistoryModel.findAll({
      where: whereClause,
      order: sortBy,
    attributes: ["id", "alert_id", "name", "status"],
    raw: true,
  });

  // Group by name
  const groupedAlerts = alertsHistory.reduce((acc, alert) => {
    if (!acc[alert.name]) {
      acc[alert.name] = [];
    }
    acc[alert.name].push(alert);
    return acc;
  }, {});

  // Convert groupedAlerts object into an array
  const groupedAlertsArray = Object.entries(groupedAlerts).map(
    ([name, items]) => {
      const alertId = items.find((f) => f.name === name).alert_id;
      return {
        label: name,
        value: items.map(({ id }) => id).toString(),
        alert_id: alertId,
        alerts: items.map(({ id, status }) => ({ id, status })),
      };
    }
  );
  return groupedAlertsArray;
};
const viewAlertHistoryById = async (req) => {
  const { alertHistoryId } = req.params;
  const alertHistory = await AlertsHistoryModel.findOne({
    where: {
      id: alertHistoryId,
      // state: { [Op.in]: [alertGrafanaStates.ALERTING] },
    },
    raw: true,
  });
  if (!alertHistory) {
    throw new ApiError(httpStatus.NOT_FOUND, "Alert history not found");
  }
  // if (alertHistory.state === alertGrafanaStates.ALERTING) {
  const { description } = alertHistory;
  const closingBraceIndex = description.indexOf("}") + 1; // include the '}'
  let basicDetails = description;
  let valueStr = "";

  if (closingBraceIndex > 0 && closingBraceIndex < description.length) {
    basicDetails = description.slice(0, closingBraceIndex).trim();
    valueStr = description.slice(closingBraceIndex).trim();

    // Remove leading '-' if present
    if (valueStr.startsWith("-")) {
      valueStr = valueStr.slice(1).trim();
    }
  }
  let variation;
  const extractedValues = valueStr.split(", ").reduce((acc, cur) => {
    const [key, value] = cur.split("=");
    acc[key] = { labels: "-", value: value };
    return acc;
  }, {});

  // const labels = lodash.get(extractedValues, "trigger_alert.labels", "");
  const match = basicDetails.match(/\{(.+)\}/);
  let labels = {};

  if (match && match[1]) {
    labels = match[1].split(", ").reduce((acc, cur) => {
      const [key, value] = cur.split("=");
      acc[key] = value || ""; // Handle empty values
      return acc;
    }, {});
  }
  
  let obj = buildObjectFromAlert(labels, extractedValues, alertHistory.alert_type);
  let customerName = "";
  if (labels) {
    customerName = labels.customer_name;
    // customerName = labels.replace(/customer_name=([^ ]+)/, "$1");
    // customerNameArr.push(customerName);
  }

  if (customerName) {
    obj = {
      Customer: customerName,
      ...obj,
    };
  }
  return [Object.keys(obj), Object.values(obj)];
  // }
  return alertHistory;
};
const getAlertHistoryById = async (req) => {
  const { alertId } = req.params;
  const query = `alertStatechanges(alertId: ${alertId}) {
      description
      state
      timestamp
      values
    }`;
  const history = await graphqlRequestHandler(
    "alertStatechanges",
    query,
    gqlOp.query
  );
  return history;
};
const getAlertHistory = async (req) => {
  const { limit, offset, page } = usePageLimit(req);
  const sortBy = [];

  if (req.query.sortBy) {
    // Accept formats like "timestamp:desc" or "timestamp:asc"
    const [field, direction] = req.query.sortBy.split(":");
    sortBy.push([field, (direction || "DESC").toUpperCase()]);
  }

  const { _endDate, _startDate } = await getOrDefaultDateRange(req);
  console.log("_endDate, _startDate: ", _endDate, _startDate);
  const whereClause = {
    status: {
      [Op.notIn]: [alertConfig.alertsHistoryStatus.DELETED],
    },
    state: { [Op.in]: [alertGrafanaStates.ALERTING] },
    timestamp: {
      [Op.between]: [_startDate, _endDate],
    },
  };
  if (req.query.status) {
    whereClause.status = {
      [Op.in]: req.query.status.split(",").map((m) => m.trim()),
    };
  }

  const searchQuery = req.query.search || "";

  if (searchQuery) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'name'
      { category: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
      { alert_type: { [Op.like]: `%${searchQuery}%` } }, // Case-insensitive search on 'category'
    ];
  }

  let alertsHistory;

  if (Number(req.query.download)) {
    alertsHistory = await AlertsHistoryModel.findAndCountAll({
      where: whereClause,
      order: sortBy,
      attributes: [
        "id",
        "description",
        "state",
        "timestamp",
        "status",
        "name",
        "category",
        "alert_type",
      ],
      raw: true,
    });
  } else {

    alertsHistory = await AlertsHistoryModel.findAndCountAll({
      where: whereClause,
      order: sortBy,
      limit,
      offset,
      attributes: [
        "id",
        "description",
        "state",
        "timestamp",
        "status",
        "name",
        "category",
        "alert_type",
      ],
      raw: true,
    });
  }

  if (alertsHistory) {
    alertsHistory.page = page;
    alertsHistory.limit = limit;
  }
  return alertsHistory;
};
const updateAlertHistories = async (req) => {
  const { alertHistoryIds } = req.body;

  const options = {};
  if (req._rollbackTxn) options.transaction = req._rollbackTxn;

  // Fetch all alerts by IDs
  const alertHistories = await AlertsHistoryModel.findAll({
    where: { id: { [Op.in]: alertHistoryIds } },
  });

  if (alertHistories.length === 0) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "No alert histories found for the provided IDs"
    );
  }

  // Prepare payload for update
  const payload = {
    status: req.body.status,
    updated_at: new Date(),
    updated_by: req.user.id,
  };

  // Update associated alerts in the AlertsModel
  const alertIdsToUpdate = alertHistories.map((alertH) => alertH.alert_id);
  await AlertsModel.update(
    { updated_at: new Date(), updated_by: req.user.id },
    { where: { id: { [Op.in]: alertIdsToUpdate } }, ...options }
  );

  // Update alert histories in bulk
  await AlertsHistoryModel.update(payload, {
    where: { id: { [Op.in]: alertHistoryIds } },
    ...options,
  });

  return {
    message: `${alertHistories.length} alert histories updated successfully`,
  };
};
const getAlertNotifications = async (req) => {
  const commonWhere = {
    status: {
      [Op.in]: [
        alertConfig.alertsHistoryStatus.OPEN,
        alertConfig.alertsHistoryStatus.ANALYZING,
      ],
    },
    state: { [Op.in]: [alertGrafanaStates.ALERTING] },
  };

  const [criticalCount, majorCount, minorCount] = await Promise.all([
    AlertsHistoryModel.count({
      where: { ...commonWhere, category: alertConfig.categoryTypes.CRITICAL },
    }),
    AlertsHistoryModel.count({
      where: { ...commonWhere, category: alertConfig.categoryTypes.MAJOR },
    }),
    AlertsHistoryModel.count({
      where: { ...commonWhere, category: alertConfig.categoryTypes.MINOR },
    }),
  ]);

  return { critical: criticalCount, major: majorCount, minor: minorCount };
};


const getAlertNames = async (req) => {
  const sortBy = [];

  if (req.query.sortBy) {
    // Accept formats like "timestamp:desc" or "timestamp:asc"
    const [field, direction] = req.query.sortBy.split(":");
    sortBy.push([field, (direction || "DESC").toUpperCase()]);
  }
  const whereClause = {
    status: {
      [Op.notIn]: [alertConfig.alertsHistoryStatus.DELETED],
    },
    state: { [Op.in]: [alertGrafanaStates.ALERTING] },
  };

  let alertsHistoryNames;

  alertsHistoryNames = await AlertsHistoryModel.findAndCountAll({
    where: whereClause,
      order: sortBy,
      attributes: [
        "id",
        "name",
      ],
      raw: true,
    });

  return alertsHistoryNames;
};

const getAlertByName = async(name) => {
  try {
    const alert = await AlertsHistoryModel.findOne({
      where: {
        name: name,
      },
      raw: true,
    });
    return alert;
  } catch (error) {
    console.error("Error fetching alert by name:", error);
    throw error;
  }
}

module.exports = {
  getGroupById,
  getGroupByName,
  getGroups,
  createGroup,
  updateGroup,
  deleteGroup,
  getGroupMembers,
  createAlert: useTablesRollback(createAlert),
  updateAlert: useTablesRollback(updateAlert),
  deleteAlert: useTablesRollback(deleteAlert),
  getAlerts,
  getAlertById,
  getAlertsMetadata,
  getErrorDescriptionList,
  getResultCodeList,
  getAlertNameList,
  viewAlertHistoryById,
  getAlertHistoryById,
  getAlertHistory,
  updateAlertHistories: useTablesRollback(updateAlertHistories),
  getAlertNotifications,
  getNodeList,
  getAlertNames,
  getAlertByName
};
