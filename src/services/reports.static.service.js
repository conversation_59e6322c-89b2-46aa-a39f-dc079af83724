const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const graphqlService = require("./graphql.service");
const emailService = require("./email.service");
const groupService = require("./alerts.service");
const axios = require("axios");

const config = require("../config/config");
const globalConfig = require("../config/globalConfig");
const logger = require("../config/logger");
const { auditLogEvents } = require("../config/roles");
const {
  reportsMapping,
  restapiUri,
  timestampParam,
} = require("../config/reportConfig");

const {
  formatReportDate,
  getWhereClause,
} = require("../utils/misc");

const auditLogService = require("./audit-log.service");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");

dayjs.extend(utc);
dayjs.extend(timezone);

const constructCSV = require("../utils/generateCsv");
const constructPDF = require("../utils/generatePdf");
const constructExcel = require("../utils/generateExcelFile");
const offlineDownloadService = require("./offlineDownload.service");
const path = require("path");
const { saveCsvOffline, createZipFile, createZipBuffer } = require("../utils/saveCsvOffline");
const constructGrapghqlQuery = require("../utils/constructGrapghqlQuery.js");
const { customReports } = require("./reports.custom.service.js");
const isWeekMonthApi = require("../utils/isWeekMonthAPi.js");
const { getTotalCount } = require("../utils/get-total-count.js");
const { maskDownloadedRows, maskNumber } = require("../utils/maskNumber.js");
const { padding, PaddingDownloadedRows } = require("../utils/padding.js");
const streamCsvToZipBuffer = require("../utils/streamCsvToZipBuffer.js");
const streamExcelToZipBuffer = require("../utils/streamExcelToZipBuffer.js");
const streamPdfToZipBuffer = require("../utils/streamPdfToZipBuffer.js");
const { reportsService } = require("./index.js");
const { getReportValues } = require("../utils/helper.js");


// Helper: Parse and format downloaded data
function parseDownloadedData(rawData) {
  const lines = rawData.split("\n");
  if (lines.length < 2)
    throw new Error("No data available in downloadable response.");

  const headers = lines[0].split("\t");
  const timestampIndex = headers.indexOf("timestamp");

  const rows = lines.slice(1).map((line) => {
    if (!line) return [];

    const columns = line.replace(/\\N|NaN/g, "-").split("\t");
    return columns.map((col) => {
      if (["", "NaN", "\\N", "-"].includes(col)) return "-";

      const regexPattern =
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;
      if (regexPattern.test(col)) {
        return dayjs(col.split("+")[0]).format("YYYY-MMM-DD HH:mm:ss");
      }

      return isNaN(col) ? col : parseFloat(col);
    });
  });

  // console.log("rows->", rows.slice(0, 3))

  if (rows.length === 0) throw new Error("Parsed rows are empty.");
  return { headers, rows };
}

// Helper: Format start and end dates
function getStartAndEndDates(payload, user) {
  const startDate = payload.startDate
    ? payload.startDate
    : dayjs.utc().format("YYYY-MM-DD 00:00:00");

  const endDate = payload.endDate
    ? payload.endDate
    : dayjs.utc().format("YYYY-MM-DD HH:mm:ss");

  return { startDate, endDate };
}

const downloadStaticReports = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  aggregateResponse,
  totalCount,
}) => {

  try {
    let response;

    if (isWeekMonthApi(viewBy, reportReqData, payload)) {
      const updatedPayload = {
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      };

      const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
        payload: updatedPayload,
        whereClause,
        viewBy,
        user,
        reportReqData,
      });

      logger.info("sending graphql request to download week month report");
      response = await graphqlService.getWeekMonthyReport(query, user, isDownload = true);
    } else {
      const data = {
        method: "post",
        url: `${restapiUri}/api/v1/download_report`,
        data: {
          view_by: viewBy,
          report_name: reportReqData.normalizedName,
          start_time: payload.startDate.trim(),
          end_time: payload.endDate.trim(),
          order_by: "timestamp",
          fields: payload?.selectedColumns?.length > 0 ? getReportValues(reportReqData, payload.selectedColumns) : Object.keys(reportReqData.responseFields),
          time_zone: user.timezone,
          ...(whereClause && { where: whereClause }),
        },
      };

      console.log("Sending axios request to download report", data);
      response = await axios(data);
    }

    let { headers, rows } = parseDownloadedData(response.data);

    // console.log("rows->", rows.slice(0, 3))

    const heading = payload?.selectedColumns ? payload.selectedColumns : Object.values(reportReqData.responseFields);

    rows = PaddingDownloadedRows(heading, rows)

    // Mask row values
    if (!user.isSuperAdmin && user.reportMasking) {
      rows = maskDownloadedRows(heading, rows);
    }

    switch (payload.type) {
      case "EXCEL":
        // return constructExcel(heading, rows, payload, aggregateResponse);
        return streamExcelToZipBuffer(heading, rows, payload, aggregateResponse);
      case "PDF":
        // return await constructPDF(heading, rows, payload, aggregateResponse);
        return await streamPdfToZipBuffer(heading, rows, payload, aggregateResponse);
      default:
        // return await constructCSV(heading, rows, payload, aggregateResponse);
        return await streamCsvToZipBuffer(heading, rows, payload, aggregateResponse);
    }
  } catch (error) {
    logger.error("Download static report failed:", error);

    // Error from API
    if (error.response?.data?.detail) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.response.data.detail);
    }

    // // Log audit trail for unexpected failure
    // await auditLogService.addAuditLog({
    //   username: user.name,
    //   userId: user.id,
    //   roleName: user.role.name,
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading: ${error.message}`,
    // });

    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Something went wrong. Please try again later."
    );
  }
};

const handleBillWeekMonthOfflineDownload = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  aggregateResponse,
  totalCount,
  offlineDataRes
}) => {
  try {
    const weakMonthQueryArray = []
    const pageSize = config.NO_OF_DATA_PER_QUERY;
    const totalPages = Math.ceil(totalCount / pageSize);

    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > pageSize) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        const updatedPayload = {
          ...payload,
          page: pageNumber,
          limit: Number(pageSize),
          download: 1,
        };

        // Construct the query for each page
        const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
          payload: updatedPayload,
          whereClause,
          viewBy,
          user,
          reportReqData,
        });

        weakMonthQueryArray.push(query);
      }

    } else {
      const updatedPayload = {
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      };

      // Construct the query for the entire dataset
      const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
        payload: updatedPayload,
        whereClause,
        viewBy,
        user,
        reportReqData,
      });

      weakMonthQueryArray.push(query);
    }


    // Iterate and download data
    for (let i = 0; i < weakMonthQueryArray.length; i++) {
      const data = weakMonthQueryArray[i];
      const isFinal = i === weakMonthQueryArray.length - 1;

      if (whereClause) {
        data.data.where = whereClause;
      }

      // Fetch data from FastAPI
      const response = await graphqlService.getWeekMonthyReport(data, user, isDownload = true);

      // Parse the response data
      let { headers, rows } = parseDownloadedData(response.data);

      const heading = payload?.selectedColumns ? payload.selectedColumns : Object.values(reportReqData.responseFields);

      rows = PaddingDownloadedRows(heading, rows);

      // Mask row values
      if (!user.isSuperAdmin && user.reportMasking) {
        rows = maskDownloadedRows(heading, rows);
      }

      // Construct CSV from the parsed data
      const csv = await constructCSV(heading, rows, payload, aggregateResponse);

      // Save the CSV file offline as muli-parted tar.gz file
      await saveCsvOffline(csv, offlineDataRes, isFinal);
    }
    return null;
  } catch (error) {
    logger.error(`Offline download failed: ${error.message}`);

    throw new Error(
      `Offline download failed: ${error.message}`
    );
  }
}

const handleOtherRepOfflineDownload = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  aggregateResponse,
  totalCount,
  offlineDataRes
}) => {
  try {

    const restApiPayload = [];
    const pageSize = config.NO_OF_DATA_PER_QUERY;
    const totalPages = Math.ceil(totalCount / pageSize);
    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > pageSize) {
      // Create multiple queries for pagination
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        restApiPayload.push({
          method: "post",
          url: `${restapiUri}/api/v1/download_report`,
          data: {
            view_by: viewBy,
            report_name: reportReqData.normalizedName,
            start_time: payload.startDate.trim(),
            end_time: payload.endDate.trim(),
            order_by: "timestamp",
            fields: payload?.selectedColumns?.length > 0 ? getReportValues(reportReqData, payload.selectedColumns) : Object.keys(reportReqData.responseFields),
            time_zone: user.timezone,
            pageNumber,
            pageSize,
          },
        });
      }
    } else {
      // Create a single query for the entire dataset
      restApiPayload.push({
        method: "post",
        url: `${restapiUri}/api/v1/download_report`,
        data: {
          view_by: viewBy,
          report_name: reportReqData.normalizedName,
          start_time: payload.startDate.trim(),
          end_time: payload.endDate.trim(),
          order_by: "timestamp",
          fields: payload?.selectedColumns?.length > 0 ? getReportValues(reportReqData, payload.selectedColumns) : Object.keys(reportReqData.responseFields),
          time_zone: user.timezone,
        },
      });
    }

    // Iterate and download data
    for (let i = 0; i < restApiPayload.length; i++) {
      const data = restApiPayload[i];
      const isFinal = i === restApiPayload.length - 1;

      if (whereClause) {
        data.data.where = whereClause;
      }
      // Fetch data from FastAPI  
      const response = await axios(data);

      // Parse the response data
      let { headers, rows } = parseDownloadedData(response.data);

      const heading = payload?.selectedColumns ? payload.selectedColumns : Object.values(reportReqData.responseFields);

      rows = PaddingDownloadedRows(heading, rows)

      // Mask row values
      if (!user.isSuperAdmin && user.reportMasking) {
        rows = maskDownloadedRows(heading, rows);
      }

      // Construct CSV from the parsed data
      const csv = await constructCSV(heading, rows, payload, aggregateResponse);

      // Save the CSV file offline as muli-parted tar.gz file
      await saveCsvOffline(csv, offlineDataRes, isFinal);
    }
    return null;
  } catch (error) {
    // console.log("error->", error)
    logger.error(`Offline download failed: ${error.message}`);

    throw new Error(
      `Offline download failed: ${error.message}`
    );
  }
}

const handleOfflineDownload = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  aggregateResponse,
  totalCount,
  res
}) => {
  if (payload.type != "CSV") {
    res.status(httpStatus.BAD_REQUEST).send({
      message: "Only CSV download is supported for offline download",
    });
    return;
  }

  let offlineDataRes = null;

  try {
    const fileName = payload.fileName || `${payload.reportName}_${payload.startDate}_${payload.endDate}`;

    logger.info(`Creating download entry for ${fileName}`);

    const filePath = path.resolve(
      config.offline_downloads.download_directory,
      `${fileName}.csv`
    );

    // Construct the offline download data
    const offlineFileData = {
      userId: user.id,
      initiatedBy: user.name,
      fileName,
      reportName: payload.reportName,
      category: config.offline_downloads.category.static,
      filePath,
      status: config.offline_downloads.status.INITIATED,
    };

    // Create the offline download entry
    offlineDataRes = await offlineDownloadService.createOfflineDownload(
      offlineFileData
    );

    // Respond to user immediately to indicate offline download initiation
    res.send({ isOffineDownload: true, message: config.offline_downloads.message });


    // For Billing Reports with viewBy as week/month
    if (isWeekMonthApi(viewBy, reportReqData, payload)) {

      // Handle offline download for billing reports
      return await handleBillWeekMonthOfflineDownload({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        aggregateResponse,
        totalCount,
        offlineDataRes
      });
    } else {
      // Handle offline download for other reports 
      return await handleOtherRepOfflineDownload({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        aggregateResponse,
        totalCount,
        offlineDataRes
      })
    }
  } catch (error) {
    logger.error(`Offline download failed: ${error.message}`);

    // Handle error and update offline download status as failed
    if (offlineDataRes) {
      await offlineDownloadService.updateOfflineDownload(offlineDataRes.id, {
        status: config.offline_downloads.status.FAILED,
      });
    }

    // // Update audit log for failed download
    // await auditLogService.addAuditLog({
    //   username: user.name,
    //   userId: user.id,
    //   roleName: user.role.name,
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading ${error.message}`,
    // });
  }

  return null;
};

const processStaticRepDownload = async ({
  user,
  payload,
  reportReqData,
  viewBy,
  whereClause,
  res,
  aggregateResponse,
  totalCount,
}) => {
  try {
    // If the report total count is greater than the configured limit, initiate offline download
    // If payload.sendFile is true, it means the user is requesting a file download (used for Billing report FTP download)
    if (Number(totalCount) > config.offline_downloads.initiate_offline_download_rows) {
      const response = await handleOfflineDownload({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        aggregateResponse,
        totalCount,
        res
      });

      return response;
    }

    // If the report total count is less than the configured limit, proceed with online download
    let file = await downloadStaticReports({
      user,
      payload,
      reportReqData,
      whereClause,
      viewBy,
      aggregateResponse,
      totalCount,
    });

    logger.info('zip File constructed')

    // Create a tar.gz buffer from the file
    // const compressedFile = await createTarGzBuffer(file.buf, file.filename);

    // const compressedFile = await createZipBuffer([file]);

    // // // Send the tar.gz buffer as a response
    // console.log("zip buffer received")
    // res.setHeader("Content-Type", "application/zip");
    // res.setHeader("Content-Disposition", `attachment; filename=${file.filename}.zip`);
    // res.send(compressedFile);

    res.setHeader("Content-Type", "application/zip");
    res.setHeader("Content-Disposition", `attachment; filename=${file.filename}`);
    res.send(file.buf);

    return
  } catch (error) {
    console.error("Error in processStaticRepDownload:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

const processStaticRepSendEmail = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  aggregateResponse,
  totalCount,
}) => {
  console.log("processStaticRepSendEmail called");
  try {
    // If the report total count is greater than the configured limit, throw an error
    if (totalCount > config.NO_OF_ROWS_PER_FILE) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Maximum response size for send email is reached `
      );
    }

    // Download the report data ad buffer file
    let file = await downloadStaticReports({
      user,
      payload,
      reportReqData,
      whereClause,
      viewBy,
      aggregateResponse,
      totalCount,
    });

    logger.info("zipped file generated")

    // Create a tar.gz buffer from the file
    // const tarGzBuffer = await createTarGzBuffer(file.buf, file.filename);
    // const compressedFile = await createZipBuffer([file]);

    // Calculate the file size in MB
    const fileSizeInMB = (
      Buffer.byteLength(file.buf) /
      (1024 * 1024)
    ).toFixed(2);

    logger.info("fileSizeInMB" + fileSizeInMB)

    // Check if the file size exceeds the maximum allowed size
    if (fileSizeInMB > globalConfig.MAX_EMAIL_ATTACHMENT_SIZE) {
      throw new ApiError(413, "File size too large");
    }
    let groupMembers = [];
    if (payload.groupList) {
      groupMembers = await groupService.getGroupMembers(payload.groupList);
    }
    let emailList = payload.mailList.concat(groupMembers);
    emailList = emailList.filter(
      (item, index) => emailList.indexOf(item) === index
    );

    if (!emailList || emailList.length == 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Minimum 1 email ID required");
    }

    // Create the email object
    const emailObj = {
      from: process.env.EMAIL_FROM,
      to: emailList.toString(),
      subject: payload.reportName,
      template: "email_report.template",
      context: {
        REPORT_NAME: payload.reportName,
        STARTDATE: file.startDate,
        ENDDATE: file.endDate,
      },
      attachments: {
        // encoded string as an attachment
        filename: file.filename,
        content: file.buf,
        encoding: "base64",
      },
    };

    // Send the email
    return await emailService.MailService(emailObj);
  } catch (error) {
    console.error("Error in processStaticRepSendEmail:", error.message);
    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message
    );
  }
};

/**
 * Handles retrieval and processing of static or custom reports.
 * - Validates report name and configuration
 * - Supports custom reports, static reports, and billing reports (weekly/monthly)
 * - Manages download and email functionality
 * - Fetches and formats report data using FastAPI GraphQL service
 * - Logs user activity for audit purposes
 */

const getStaticReports = async (user, payload, options, res) => {

  if (!payload.reportName || !reportsMapping[payload.reportName]) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Report name not found");
  }

  const reportReqData = reportsMapping[payload.reportName];

  let userPreference = null;

  // If the report is not in the expandable columns list, fetch user preferences
  if (!config.REPORTS_NOT_INCLUDES_EXPANDABLE_COLUMNS.includes(payload.reportName)) {
    const userPreferenceFilter = {
      reportType: "static",
      reportName: payload.reportName,
    }
    // Validate user preference for selected columns
    userPreference = await reportsService.getSelectedColumns(user.id, userPreferenceFilter);
  }


  if (userPreference && userPreference.selectedColumns) {
    payload.selectedColumns = userPreference.selectedColumns;
    payload.selectAll = userPreference.selectAll;
  }

  if (!reportReqData) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Report configuration not found for: ${payload.reportName}`
    );
  }

  // Handle Custom Reports
  if (reportReqData.isCustomReport) {
    logger.info(
      `Custom report requested: ${reportReqData.name} by user: ${user.id}`
    );
    return await customReports(payload, user, reportReqData, options, res);
  }

  logger.info(
    `Static report requested: ${reportReqData.name} by user: ${user.id}`
  );

  // Build where clause and advanced filter query
  const { whereClause, viewBy } =
    await getWhereClause({
      user,
      payload,
      reportReqData,
      options,
    });

  // Get Aggregation Mapping
  const { aggregationFieldMapping } = reportReqData || {};

  let aggregateResponse = [];

  if (aggregationFieldMapping) {
    // Build Aggregation Query
    const aggregateQuery = constructGrapghqlQuery.getAggregationQuery({
      payload,
      whereClause,
      viewBy,
      user,
      reportReqData
    });

    // Get Aggregation Response from FastAPI
    aggregateResponse = await graphqlService.getAggregationFields(
      aggregateQuery,
      reportReqData
    );
  }

  // Handle Download and Send Email
  if (Number(payload?.download) || Number(payload?.sendEmail)) {
    // To find total count of the Report

    const totalCount = await getTotalCount(payload, whereClause, viewBy, user, reportReqData);

    logger.info(`Total count found ${totalCount}`);

    // Handle offline and online Download
    if (Number(payload.download)) {
      return await processStaticRepDownload({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        res,
        aggregateResponse,
        totalCount,
      });
    }
    // Handle Send Email
    if (Number(payload.sendEmail)) {
      return await processStaticRepSendEmail({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        aggregateResponse,
        totalCount,
      });
    }
  }
  // Handle View
  let response;

  // Handle Weekly and Monthly Reports Billing
  if (isWeekMonthApi(viewBy, reportReqData, payload)) {

    // Build weekly and monthly report query
    const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
      payload,
      whereClause,
      viewBy,
      user,
      reportReqData,
    });


    // Get response for weekly and monthly report from FastAPI
    response = await graphqlService.getWeekMonthyReport(query, user, isDownload = false);

  } else {
    // Build static report query
    const graphQlPayload = constructGrapghqlQuery.getStaticRepQuery({
      payload,
      whereClause,
      viewBy,
      user,
      reportReqData,
    });

    // Get response for static report from GraphQL
    response = await graphqlService.getGraphqlData({
      name: reportReqData.name,
      payload: graphQlPayload,
    });
  }

  let data = response.items;

  const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

  await Promise.all(
    data.map(async (obj) => {
      obj[timestampParam] = await formatReportDate(obj[timestampParam], viewBy);

      for (const key of Object.keys(obj)) {
        if (regexPattern.test(obj[key])) {
          obj[key] = obj[key].split("+")[0];
          obj[key] = dayjs(obj[key]).format("YYYY-MMM-DD HH:mm:ss");
        }

        if (reportReqData.responseFields[key]) {
          obj[reportReqData.responseFields[key]] = obj[key];
          delete obj[key];
        }
      }

      return obj;
    })
  );

  data = padding(data)

  //Masking the number
  if (!user.isSuperAdmin && user.reportMasking) {
    data = maskNumber(data);
  }

  //Masking the number
  if (!user.isSuperAdmin && user.reportMasking) {
    data = maskNumber(data);
  }

  let responseTobeSent = {
    totalCount: response.page_meta.total_items,
    pageNo: response.page_meta.current_page,
    pageSize: response.page_meta.page_size,
    aggregateResponse,
    data,
  };
  return responseTobeSent;
};

module.exports = {
  parseDownloadedData,
  getStaticReports,
};
