const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const axios = require("axios");
const path = require("path");
const fs = require("fs");
const {
  getViewByValue,
} = require("../utils/misc");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { Parser } = require("json2csv");
const moment = require("moment");

dayjs.extend(utc);
dayjs.extend(timezone);
const {
  reportsMapping,
  restapiUri,
} = require("../config/reportConfig");
const constructGrapghqlQuery = require("../utils/constructGrapghqlQuery.js");
const graphqlService = require("./graphql.service");
const constructCSV = require("../utils/generateCsv.js");
const { parseDownloadedData } = require("./reports.static.service.js");
const logger = require("../config/logger.js");
const config = require("../config/config");
const readline = require("readline");
const { getTotalCount } = require("../utils/get-total-count.js");
const isWeekMonthApi = require("../utils/isWeekMonthAPi.js");
const { auditLogService, reportsService } = require("./index.js");
const { auditLogEvents } = require("../config/roles.js");
const { zipSubdirectories } = require("../utils/saveCsvOffline.js");
const { getReportValues } = require("../utils/helper.js");



// Helper functions
const calculateMonthlyBoundaries = (startDate, endDate) => ({
  previousStartOfMonth: dayjs(startDate).startOf("month").format("YYYY-MM-DD"),
  previousEndOfMonth: dayjs(startDate).endOf("month").format("YYYY-MM-DD"),
  currentStartOfMonth: dayjs(endDate).startOf("month").format("YYYY-MM-DD"),
  currentEndOfMonth: dayjs(endDate).endOf("month").format("YYYY-MM-DD"),
});

const formatTimezone = (timezone) => timezone.replace("/", "-").toLowerCase();

// const prepareCsvHeader = (header, startOfMonth, endOfMonth, currentDate, payloadEndDate) =>
//   `Reports for ${startOfMonth} to ${payloadEndDate}\n${header}`;

const prepareCsvHeader = (
  headers,
  fileDate
) => {
  // Adjust title to clean date (optional)
  const titleLine = `Reports for ${fileDate}`;
  const aggHeaderLine = headers[1];   // e.g. 'Unit Rate,Messages Processed,Messaging Fee'
  const aggValueLine = headers[2];    // e.g. '1318.9227,2707596,32869.208'
  const dataHeaderLine = headers[3];  // e.g. 'Date,Source Customer,...'

  return [
    titleLine,
    aggHeaderLine,
    aggValueLine,
    dataHeaderLine
  ].join("\n");
};



const ensureDirectoriesExist = async () => {
  if (!fs.existsSync(config.secured_folder)) {
    fs.mkdirSync(config.secured_folder, { recursive: true });
  }
  if (!fs.existsSync(config.BILLING_REPORTS.reports_directory)) {
    fs.mkdirSync(config.BILLING_REPORTS.reports_directory, { recursive: true });
  }
  if (!fs.existsSync(config.FY_REPORTS.reports_directory)) {
    fs.mkdirSync(config.FY_REPORTS.reports_directory, { recursive: true });
  }
};

// const handleMonthData = async (
//   headers,
//   monthRows,
//   formattedTimezone,
//   startOfMonth,
//   endOfMonth,
//   filename,
//   datePosition
// ) => {
//   const securedFilePath = generateFilePath(
//     filename,
//     formattedTimezone,
//     startOfMonth,
//     endOfMonth
//   );

//   const newRowIdentifiers = new Set(
//     monthRows.map(row => row.split(",")[datePosition]?.trim())
//   );

//   let existingDataRows = [];

//   if (fs.existsSync(securedFilePath)) {
//     const fileStream = fs.createReadStream(securedFilePath);
//     const rl = readline.createInterface({
//       input: fileStream,
//       crlfDelay: Infinity,
//     });

//     for await (const line of rl) {
//       const trimmed = line.trim();
//       if (!trimmed || trimmed.startsWith("Date") || !/^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
//         continue; // Skip headers or empty lines
//       }

//       const parts = trimmed.split(",");
//       const dateValue = parts[datePosition]?.trim();

//       // Only keep row if it’s not in new data
//       if (!newRowIdentifiers.has(dateValue)) {
//         existingDataRows.push(trimmed);
//       }
//     }
//   }

//   const allRows = [...existingDataRows, ...monthRows];

//   // const ws = fs.createWriteStream(securedFilePath);

//   // // Ensure only one header
//   // ws.write(`${headers.join("\n")}\n`);
//   // for (const row of allRows) {
//   //   ws.write(`${row}\n`);
//   // }
//   // ws.end();

//   const finalCsv = `${headers.join("\n")}\n${allRows.join("\n")}`;
//   fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
// };
// const handleMonthData = async (
//   headers,
//   monthRows,
//   formattedTimezone,
//   startOfMonth,
//   endOfMonth,
//   filename,
//   datePosition
// ) => {
//   const securedFilePath = generateFilePath(
//     filename,
//     formattedTimezone,
//     startOfMonth,
//     endOfMonth
//   );

//   const newRowIdentifiers = new Set(
//     monthRows.map(row => row.split(",")[datePosition]?.trim())
//   );

//   let existingDataRows = [];

//   if (fs.existsSync(securedFilePath)) {
//     const fileStream = fs.createReadStream(securedFilePath);
//     const rl = readline.createInterface({
//       input: fileStream,
//       crlfDelay: Infinity,
//     });

//     for await (const line of rl) {
//       const trimmed = line.trim();
//       if (!trimmed || trimmed.startsWith("Date") || !/^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
//         continue; // Skip headers or empty lines
//       }

//       const parts = trimmed.split(",");
//       const dateValue = parts[datePosition]?.trim();

//       // Only keep row if it’s not in new data
//       if (!newRowIdentifiers.has(dateValue)) {
//         existingDataRows.push(trimmed);
//       }
//     }
//   }

//   const allRows = [...existingDataRows, ...monthRows];

//   const finalCsv = `${headers.join("\n")}\n${allRows.join("\n")}`;
//   fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
// };



// const finalizeCsvFile = (
//   filename,
//   currentStartOfMonth,
//   isFYReport
// ) => {
//   console.log("currentStartOfMonth->", currentStartOfMonth);

//   const directory_name = isFYReport ? config.FY_REPORTS.reports_directory : config.BILLING_REPORTS.reports_directory;

//   // Determine subfolder name
//   const subfolderName = isFYReport
//     ? dayjs(currentStartOfMonth).format('YYYY')           // e.g., "2025"
//     : dayjs(currentStartOfMonth).format('MMM-YY').toUpperCase(); // e.g., "JAN-25"

//   const securedFilePath = generateFilePath(filename);
//   const subfolderPath = path.join(directory_name, subfolderName);

//   // Ensure subfolder exists
//   if (!fs.existsSync(subfolderPath)) {
//     fs.mkdirSync(subfolderPath, { recursive: true });
//   }

//   const reportsFilePath = path.join(subfolderPath, path.basename(securedFilePath));

//   fs.copyFileSync(securedFilePath, reportsFilePath);
//   console.log(`CSV file saved and copied to: ${reportsFilePath}`);

//   return reportsFilePath;
// };


const writeMonthlyCsvData = async (
  headers,
  monthRows,
  formattedTimezone,
  startOfMonth,
  endOfMonth,
  filename,
  datePosition,
  isFYReport,
  payload
) => {
  console.log("isFYReport-->", isFYReport)
  const directory_name = isFYReport
    ? config.FY_REPORTS.reports_directory
    : config.BILLING_REPORTS.reports_directory;

  let subfolderName = isFYReport
    ? dayjs(startOfMonth).format('YYYY')           // e.g., "2025"
    : dayjs(startOfMonth).format('MMM-YY').toUpperCase(); // e.g., "JAN-25"

  if (!isFYReport) {
    subfolderName = subfolderName + '/' + payload.reportName
  }

  const subfolderPath = path.join(directory_name, subfolderName);

  // Ensure target directory exists
  if (!fs.existsSync(subfolderPath)) {
    fs.mkdirSync(subfolderPath, { recursive: true });
  }

  const finalFilePath = path.join(subfolderPath, filename);
  const finalDir = path.dirname(finalFilePath);
  if (!fs.existsSync(finalDir)) {
    fs.mkdirSync(finalDir, { recursive: true });
  }

  console.log("filename->", filename)
  console.log("finalFilePath->", finalFilePath)

  // Build a set of new row identifiers to avoid duplication
  const newRowIdentifiers = new Set(
    monthRows.map(row => row.split(",")[datePosition]?.trim())
  );

  let existingDataRows = [];

  if (fs.existsSync(finalFilePath)) {
    const fileStream = fs.createReadStream(finalFilePath);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith("Date") || !/^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
        continue; // Skip headers or empty lines
      }

      const parts = trimmed.split(",");
      const dateValue = parts[datePosition]?.trim();

      if (!newRowIdentifiers.has(dateValue)) {
        existingDataRows.push(trimmed);
      }
    }
  }

  const allRows = [...existingDataRows, ...monthRows];
  const finalCsv = `${headers.join("\n")}\n${allRows.join("\n")}`;

  fs.writeFileSync(finalFilePath, finalCsv, "utf-8");
  console.log(`CSV written directly to: ${finalFilePath}`);

  return finalFilePath;
};


// const writeMonthlyCsvData = async (
//   headers,
//   monthRows,
//   formattedTimezone,
//   startOfMonth,
//   endOfMonth,
//   filename,
//   datePosition,
//   isFYReport,
//   fileDate
// ) => {
//   const directory_name = isFYReport
//     ? config.FY_REPORTS.reports_directory
//     : config.BILLING_REPORTS.reports_directory;

//   let subfolderName = isFYReport
//     ? dayjs(startOfMonth).format('YYYY')
//     : dayjs(startOfMonth).format('MMM-YY').toUpperCase(); // "JAN-25"

//   const fullMonthlyFolder = path.join(directory_name, subfolderName);

//   if (!isFYReport) {
//     subfolderName = subfolderName + '/' + fileDate;
//   }

//   const subfolderPath = path.join(directory_name, subfolderName);

//   if (!fs.existsSync(subfolderPath)) {
//     fs.mkdirSync(subfolderPath, { recursive: true });
//   }

//   const finalFilePath = path.join(subfolderPath, filename);

//   const newRowIdentifiers = new Set(
//     monthRows.map(row => row.split(",")[datePosition]?.trim())
//   );

//   let existingDataRows = [];

//   if (fs.existsSync(finalFilePath)) {
//     const fileStream = fs.createReadStream(finalFilePath);
//     const rl = readline.createInterface({
//       input: fileStream,
//       crlfDelay: Infinity,
//     });

//     for await (const line of rl) {
//       const trimmed = line.trim();
//       if (!trimmed || trimmed.startsWith("Date") || !/^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
//         continue;
//       }

//       const parts = trimmed.split(",");
//       const dateValue = parts[datePosition]?.trim();

//       if (!newRowIdentifiers.has(dateValue)) {
//         existingDataRows.push(trimmed);
//       }
//     }
//   }

//   const allRows = [...existingDataRows, ...monthRows];
//   const finalCsv = `${headers.join("\n")}\n${allRows.join("\n")}`;

//   fs.writeFileSync(finalFilePath, finalCsv, "utf-8");
//   console.log(`CSV written directly to: ${finalFilePath}`);

//   if (!isFYReport) {
//     await zipSubdirectories(fullMonthlyFolder);
//   }

//   return finalFilePath;
// };


const generateFilePath = (filename) => {
  console.log("filename->", filename)
  return path.join(
    config.secured_folder,
    filename
  )
};

const parseExistingCsv = (csv) => {
  const lines = csv.split("\n");
  return {
    existingHeaders: lines.slice(0, 3),
    existingRows: lines.slice(4),
  };
};


const getUptoDateAggregation = async (payload, reportReqData, user) => {
  try {

    const aggregateQuery = constructGrapghqlQuery.getAggregationQuery({
      payload,
      whereClause: '',
      viewBy: payload.defaultViewBy,
      user,
      reportReqData
    });

    // Get Aggregation Response from FastAPI
    const aggregationResponse = await graphqlService.getAggregationFields(
      aggregateQuery,
      reportReqData
    );

    // const headers = Object.keys(aggregationResponse);
    // const values = Object.values(aggregationResponse);
    // return `${headers}\n${values}`;
    return aggregationResponse;

  } catch (error) {
    console.log("error in getUptoDateAggregation->", error)
    throw error
  }
}



const saveBillingReports = async (csvFile, payload, reportReqData, user, isFYReport = false, part = 0) => {
  try {
    const datePosition = reportReqData.responseFields
      ? Object.values(reportReqData.responseFields).indexOf("Date")
      : 0;

    const { startDate, endDate } = payload;

    const { timezone } = user

    const formattedTimezone = formatTimezone(timezone);

    const startDateOnly = dayjs.utc(startDate).format("YYYY-MM-DD");
    const endDateOnly = dayjs.utc(endDate).format("YYYY-MM-DD");

    const fileDate = isFYReport ? `${startDateOnly}_${endDateOnly}` : startDateOnly;

    const {
      currentStartOfMonth,
      currentEndOfMonth,
    } = calculateMonthlyBoundaries(startDate, endDate);
    const currentDate = dayjs.utc().format("YYYY-MM-DD");
    const payloadEndDate = dayjs.utc(payload.endDate).format("YYYY-MM-DD");


    // const filename = `${payload.reportName}_${currentStartOfMonth}_${currentEndOfMonth}_${formattedTimezone}.csv`;
    let filename = `${payload.reportName}_${fileDate}_${formattedTimezone}/${payload.reportName}_${fileDate}_${formattedTimezone}.csv`;

    if (part) {
      filename = `${payload.reportName}_${fileDate}_${formattedTimezone}/${payload.reportName}_${fileDate}_${formattedTimezone}_part${part}.csv`
    }

    console.log("filename->", filename);

    const csvString = csvFile.buf.toString('utf8');
    const lines = csvString.split(/\r?\n/).filter(line => line.trim());

    const headers = lines.slice(0, 4);
    const dataLines = lines.slice(4);
    // console.log("lines->", lines);
    // console.log("headers->", headers);

    // This function prepares the CSV header for the current and previous month
    const currentMonthHeader = prepareCsvHeader(
      headers,
      fileDate
    );
    const headerLines = currentMonthHeader.split("\n");


    await ensureDirectoriesExist();

    await writeMonthlyCsvData(
      headerLines,
      dataLines,
      formattedTimezone,
      currentStartOfMonth,
      currentEndOfMonth,
      filename,
      datePosition,
      isFYReport,
      payload
    );


    return true;
  } catch (error) {
    console.error('Error saving billing reports:', error);
    throw error;
  }
};

const handleOtherAutosave = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  totalCount,
  isFYReport = false
}) => {
  try {

    // const {
    //   currentStartOfMonth,
    //   currentEndOfMonth,
    // } = calculateMonthlyBoundaries(payload.startDate, payload.endDate);

    const uptodateAggregation = await getUptoDateAggregation(payload, reportReqData, user);


    const restApiPayload = [];
    const pageSize = config.NO_OF_ROWS_PER_FILE;
    const totalPages = Math.ceil(totalCount / pageSize);
    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > config.NO_OF_ROWS_PER_FILE) {
      // Create multiple queries for pagination
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        restApiPayload.push({
          method: "post",
          url: `${restapiUri}/api/v1/download_report`,
          data: {
            view_by: viewBy,
            report_name: reportReqData.normalizedName,
            start_time: payload.startDate.trim(),
            end_time: payload.endDate.trim(),
            order_by: "timestamp",
            fields: Object.keys(reportReqData.responseFields),
            time_zone: user.timezone,
            pageNumber,
            pageSize,
          },
        });
      }
    } else {
      // Create a single query for the entire dataset
      restApiPayload.push({
        method: "post",
        url: `${restapiUri}/api/v1/download_report`,
        data: {
          view_by: viewBy,
          report_name: reportReqData.normalizedName,
          start_time: payload.startDate.trim(),
          end_time: payload.endDate.trim(),
          order_by: "timestamp",
          fields: payload?.selectedColumns?.length > 0 ? getReportValues(reportReqData, payload.selectedColumns) : Object.keys(reportReqData.responseFields),
          time_zone: user.timezone,
        },
      });
    }

    // console.log("restApiPayload->", restApiPayload)

    // Iterate and download data
    for (let i = 0; i < restApiPayload.length; i++) {
      const data = restApiPayload[i];

      logger.info(`Sending axios request to download report for ${data.data.start_time} to ${data.data.end_time}`);

      if (whereClause) {
        data.data.where = whereClause;
      }
      // Fetch data from FastAPI  
      const response = await axios(data);

      // Parse the response data
      const { headers, rows } = parseDownloadedData(response.data);

      const heading = payload?.selectedColumns ? payload.selectedColumns : reportReqData?.responseFields ? Object.values(reportReqData?.responseFields) : headers;

      // Construct CSV from the parsed data
      const csv = await constructCSV(heading, rows, payload, uptodateAggregation);

      let part = 0;

      if (restApiPayload.length > 1) {
        part = i + 1
      }

      await saveBillingReports(csv, payload, reportReqData, user, isFYReport, part);

    }
    return null;
  } catch (error) {
    logger.error(`handleOtherAutosave failed: ${error.message}`);

    throw new Error(
      `handleOtherAutosave failed: ${error.message}`
    );
  }
}


const handleBillWeekMonthAutosave = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  totalCount,
  isFYReport = false
}) => {
  try {
    const weakMonthQueryArray = []
    const pageSize = config.NO_OF_ROWS_PER_FILE;
    const totalPages = Math.ceil(totalCount / pageSize);


    // const {
    //   currentStartOfMonth,
    //   currentEndOfMonth,
    // } = calculateMonthlyBoundaries(payload.startDate, payload.endDate);

    const uptodateAggregation = await getUptoDateAggregation(payload, reportReqData, user);

    // console.log("uptodateAggregation->", uptodateAggregation)


    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > config.NO_OF_ROWS_PER_FILE) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        const updatedPayload = {
          ...payload,
          page: pageNumber,
          limit: Number(pageSize),
          download: 1,
        };

        // Construct the query for each page
        const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
          payload: updatedPayload,
          whereClause,
          viewBy,
          user,
          reportReqData,
        });

        weakMonthQueryArray.push(query);
      }

    } else {
      const updatedPayload = {
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      };

      // Construct the query for the entire dataset
      const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
        payload: updatedPayload,
        whereClause,
        viewBy,
        user,
        reportReqData,
      });

      weakMonthQueryArray.push(query);
    }


    // Iterate and download data
    for (let i = 0; i < weakMonthQueryArray.length; i++) {
      const data = weakMonthQueryArray[i];

      if (whereClause) {
        data.data.where = whereClause;
      }

      logger.info(`Sending getWeekMonthyReport request to download report for ${data.startDate} to ${data.endDate}`);

      // Fetch data from FastAPI
      const response = await graphqlService.getWeekMonthyReport(data, user, isDownload = true);

      // Parse the response data
      const { headers, rows } = parseDownloadedData(response.data);

      const heading = payload?.selectedColumns ? payload.selectedColumns : reportReqData?.responseFields ? Object.values(reportReqData?.responseFields) : headers;

      // Construct CSV from the parsed data
      const csv = await constructCSV(heading, rows, payload, uptodateAggregation);

      let part = 0;

      if (weakMonthQueryArray.length > 1) {
        part = i + 1
      }

      await saveBillingReports(csv, payload, reportReqData, user, isFYReport, part);

    }
    return null;
  } catch (error) {
    logger.error(`handleBillWeekMonthAutosave failed: ${error.message}`);

    throw new Error(
      `handleBillWeekMonthAutosave failed: ${error.message}`
    );
  }
}


const handleCustomReports = async ({
  user,
  payload,
  reportReqData,
  whereClause,
  viewBy,
  totalCount,
  isFYReport = false
}) => {
  try {

    const apiUrl = `${config.dataServiceUrl}${reportReqData?.apiEndpoint}`;
    const customRepQueryArray = []
    const pageSize = config.NO_OF_ROWS_PER_FILE;
    const totalPages = Math.ceil(totalCount / pageSize);


    // const {
    //   currentStartOfMonth,
    //   currentEndOfMonth,
    // } = calculateMonthlyBoundaries(payload.startDate, payload.endDate);

    const uptodateAggregation = await getUptoDateAggregation(payload, reportReqData, user);

    console.log("uptodateAggregation->", uptodateAggregation)


    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > config.NO_OF_ROWS_PER_FILE) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        const updatedPayload = {
          ...payload,
          page: pageNumber,
          limit: Number(pageSize),
          download: 1,
        };

        const query = await constructGrapghqlQuery.getCustomRepQuery({
          payload: updatedPayload,
          otherFilterQuery: '',
          viewBy,
          user,
          reportReqData,
        });

        customRepQueryArray.push(query);
      }

    } else {
      const updatedPayload = {
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      };

      // Construct the query for the entire dataset
      const query = await constructGrapghqlQuery.getCustomRepQuery({
        payload: updatedPayload,
        otherFilterQuery: '',
        viewBy,
        user,
        reportReqData,
      });


      customRepQueryArray.push(query);
    }


    // Iterate and download data
    for (let i = 0; i < customRepQueryArray.length; i++) {
      const data = customRepQueryArray[i];

      if (whereClause) {
        data.data.where = whereClause;
      }

      logger.info(`Sending getWeekMonthyReport request to download report for ${data.startDate} to ${data.endDate}`);

      // Fetch data from FastAPI
      let response = await graphqlService.getGraphqlCustomData({
        apiUrl,
        payload: data,
      });

      // Parse the response data
      const { headers, rows } = parseDownloadedData(response.data);

      const heading = reportReqData?.responseFields ? Object.values(reportReqData?.responseFields) : headers;

      // Construct CSV from the parsed data
      const csv = await constructCSV(heading, rows, payload, uptodateAggregation);

      let part = 0;

      if (customRepQueryArray.length > 1) {
        part = i + 1
      }

      await saveBillingReports(csv, payload, reportReqData, user, isFYReport, part);

    }
    return null;
  } catch (error) {
    logger.error(`handleCustomReports autosave failed: ${error.message}`);

    throw new Error(
      `handleCustomReports autosave failed: ${error.message}`
    );
  }
}


const processReportsByGranularity = async ({
  user,
  originalPayload,
  isFYReport = false,
  unit = "day", // or "month"
  isResSended = false
}) => {
  console.log("isFYReport--==>>", isFYReport)
  try {
    const start = dayjs(originalPayload.startDate);
    const end = dayjs(originalPayload.endDate);

    const reportReqData = reportsMapping[originalPayload.reportName];
    const viewBy = originalPayload.defaultViewBy;
    let whereClause = ''

    const userPreferenceFilter = {
      reportType: "static",
      reportName: originalPayload.reportName,
    }

    // Validate user preference for selected columns
    const userPreference = await reportsService.getSelectedColumns(user.id, userPreferenceFilter);

    if (!reportReqData) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Report configuration not found for: ${originalPayload.reportName}`
      );
    }

    let current = start.startOf(unit);

    while (current.isBefore(end, unit) || current.isSame(end, unit)) {
      const startDate = current.startOf(unit).format("YYYY-MM-DD 00:00:00");
      const endDate = current.endOf(unit).format("YYYY-MM-DD 23:59:59");

      const payload = {
        ...originalPayload,
        startDate,
        endDate,
      };


      if (userPreference && userPreference.selectedColumns) {
        payload.selectedColumns = userPreference.selectedColumns;
        payload.selectAll = userPreference.selectAll;
      }

      // console.log("payload->", payload);
      logger.info(`Processing report for ${unit} wise from ${startDate} to ${endDate} `);

      const totalCount = await getTotalCount(payload, whereClause, viewBy, user, reportReqData);

      console.log("totalCount->", totalCount)

      if (totalCount === 0) {
        logger.info(`🔸 Skipping: No data for ${originalPayload.reportName} range ${startDate} to ${endDate}`);
        current = current.add(1, unit);
        continue; // Skip this iteration
      }

      console.log("totalCount->", totalCount);

      const handlerFn = isWeekMonthApi(viewBy, reportReqData, originalPayload)
        ? handleBillWeekMonthAutosave
        : reportReqData?.isCustomReport
          ? handleCustomReports
          : handleOtherAutosave;

      await handlerFn({
        user,
        payload,
        reportReqData,
        whereClause,
        viewBy,
        totalCount,
        ...(isFYReport && { isFYReport }), // include flag only if true
      });

      current = current.add(1, unit);
    }
  } catch (error) {
    logger.error(`Error processing reports by granularity: ${error.message}`);
    // const auditLogPayload = {
    //   username: user.name,
    //   userId: user.id,
    //   roleName: user.role.name,
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Monthly report or FYR autosave failed:  ${error.message}`,
    // }
    // await auditLogService.addAuditLog(auditLogPayload);
    if (!isResSended) {
      throw error;
    }
  }
};



module.exports = {
  calculateMonthlyBoundaries,
  saveBillingReports,
  handleOtherAutosave,
  handleBillWeekMonthAutosave,
  processReportsByGranularity
}