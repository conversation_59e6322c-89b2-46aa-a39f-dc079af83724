const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const axios = require("axios");
const path = require("path");
const fs = require("fs");
const {
  getViewByValue,
} = require("../utils/misc");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const {
  NETWORK_REPORTS,
  secured_folder,
} = require("../config/config");
const { Parser } = require("json2csv");
const moment = require("moment");

dayjs.extend(utc);
dayjs.extend(timezone);
const {
  reportsMapping,
  restapiUri,
} = require("../config/reportConfig");
const logger = require("../config/logger");


const generateDailyData = async (baseData, startDate, endDate) => {
  const start = moment(startDate, "YYYY-MM-DD HH:mm:ss");
  const end = moment(endDate, "YYYY-MM-DD HH:mm:ss");
  const dataArray = [];

  while (start.isBefore(end)) {
    const nextDay = start.clone().endOf("day"); // Ensure the next day ends at 23:59:59

    dataArray.push({
      ...baseData,
      data: {
        ...baseData.data,
        start_time: start.format("YYYY-MM-DD HH:mm:ss"),
        end_time: nextDay.isAfter(end)
          ? end.format("YYYY-MM-DD HH:mm:ss")
          : nextDay.format("YYYY-MM-DD HH:mm:ss"),
      },
    });

    // Move to the next day's start (00:00:00)
    start.add(1, "day").startOf("day");
  }

  // console.log("Generated Data Array:", dataArray);
  return dataArray;
};

const fetchAndCombineData = async (convertedData, whereClause) => {
  try {
    const responses = [];

    for (const data of convertedData) {
      console.log("data->", data);
      if (whereClause?.length > 0) {
        data.data.where = whereClause;
      }
    
      try {
        const response = await axios(data);
        responses.push(response.data);
      } catch (error) {
        if (error.response && error.response.data) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            error.response.data.detail
          );
        } else {
          throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            "Something went wrong. Please try again after some time."
          );
        }
      }
    }

    logger.info("response received for network reports")

    // Combine the result data
    const combinedResult = responses.join("\n");

    // Process the result (e.g., splitting into lines for further processing)
    const downloadableData = combinedResult.split("\n");

    return downloadableData;
  } catch (error) {
    console.error("Error during data fetching:", JSON.stringify(error, null, 2));
    throw error;
  }
};

const processDownloadableData = (data) => {
  const headerRow = data.find((row) => row.trim() !== "");
  const filteredData = data.filter((row, index) => {
    // Keep the first occurrence of the header row and all non-header rows
    return row !== headerRow || index === data.indexOf(headerRow);
  });

  // Remove any empty rows
  const cleanedData = filteredData.filter((row) => row.trim() !== "");

  return cleanedData;
};

const saveNetworkReports = async (payload, csv, startDate, endDate) => {
  const { reportName, timezone } = payload
  const responseFields = reportsMapping[reportName].responseFields;

  const datePosition = Object.values(responseFields).indexOf("Date") + 1;
  const filenamePosition =
    Object.values(responseFields).indexOf("CDR File Name") + 1;

  const formattedTimezone = formatTimezone(timezone);
  const {
    firstStartOfMonth,
    firstEndOfMonth,
    secondStartOfMonth,
    secondEndOfMonth,
  } = calculateMonthlyBoundaries(startDate, endDate);

  // csv = prepareCsvHeader(csv, secondStartOfMonth, secondEndOfMonth);

  const { headers, rows } = splitCsvContent(csv);

  const currentMonthHeader = prepareCsvHeader(
    headers,
    secondStartOfMonth,
    secondEndOfMonth
  );
  const prevousMonthHeader = prepareCsvHeader(
    headers,
    firstStartOfMonth,
    firstEndOfMonth
  );

  ensureDirectoriesExist();

  const { previousMonthRows, currentMonthRows } = splitRowsByMonth(
    rows,
    secondStartOfMonth,
    datePosition
  );

  // console.log("previousMonthRows->", previousMonthRows);

  if (previousMonthRows.length > 0) {
    handleMonthData(
      prevousMonthHeader,
      previousMonthRows,
      formattedTimezone,
      firstStartOfMonth,
      firstEndOfMonth,
      datePosition,
      filenamePosition,
      reportName
    );

    const previousMonthFilePath = finalizeCsvFile(
      formattedTimezone,
      firstStartOfMonth,
      firstEndOfMonth,
      reportName
    );
  }

  handleMonthData(
    currentMonthHeader,
    currentMonthRows,
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth,
    datePosition,
    filenamePosition,
    reportName
  );

  const currentMonthFilePath = finalizeCsvFile(
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth,
    reportName
  );

  return currentMonthFilePath;
};

// Helper functions
const calculateMonthlyBoundaries = (startDate, endDate) => ({
  firstStartOfMonth: dayjs(startDate).startOf("month").format("YYYY-MM-DD"),
  firstEndOfMonth: dayjs(startDate).endOf("month").format("YYYY-MM-DD"),
  secondStartOfMonth: dayjs(endDate).startOf("month").format("YYYY-MM-DD"),
  secondEndOfMonth: dayjs(endDate).endOf("month").format("YYYY-MM-DD"),
});

const formatTimezone = (timezone) => timezone.replace("/", "-").toLowerCase();

const prepareCsvHeader = (header, startOfMonth, endOfMonth) =>
  `Reports for ${startOfMonth} to ${endOfMonth}\n${header}`;

const splitCsvContent = (csv) => {
  const lines = csv.split("\n");
  return {
    headers: lines.slice(0, 1).join("\n"),
    rows: lines.slice(1),
  };
};

const ensureDirectoriesExist = () => {
  if (!fs.existsSync(secured_folder)) {
    fs.mkdirSync(secured_folder, { recursive: true });
  }
  if (!fs.existsSync(NETWORK_REPORTS.reports_directory)) {
    fs.mkdirSync(NETWORK_REPORTS.reports_directory, { recursive: true });
  }
};

const splitRowsByMonth = (rows, secondStartOfMonth, datePosition) => {
  const previousMonthRows = [];
  const currentMonthRows = [];

  rows.forEach((row) => {
    // Extract and clean the date part from the row
    const rowDate = row.split(",")[datePosition].trim().replace(/"/g, ""); // Remove quotes

    // Parse the rowDate and secondStartOfMonth using dayjs
    const parsedRowDate = dayjs(rowDate);
    const parsedSecondStartOfMonth = dayjs(secondStartOfMonth);

    // Check validity of both dates
    if (!parsedRowDate.isValid()) {
      console.error(`Invalid date parsing for rowDate: "${rowDate}"`);
      return;
    }

    if (!parsedSecondStartOfMonth.isValid()) {
      console.error(
        `Invalid date parsing for secondStartOfMonth: "${secondStartOfMonth}"`
      );
      return;
    }

    // Compare dates to split rows
    if (parsedRowDate.isBefore(parsedSecondStartOfMonth)) {
      previousMonthRows.push(row);
    } else {
      currentMonthRows.push(row);
    }
  });

  return { previousMonthRows, currentMonthRows };
};

const handleMonthData = (
  headers,
  monthRows,
  formattedTimezone,
  startOfMonth,
  endOfMonth,
  datePosition,
  filenamePosition,
  reportName
) => {
  const securedFilePath = generateFilePath(
    formattedTimezone,
    startOfMonth,
    endOfMonth,
    reportName
  );

  let existingData = [];
  if (fs.existsSync(securedFilePath)) {
    const existingCsv = fs.readFileSync(securedFilePath, "utf-8");
    const { existingHeaders, existingRows } = parseExistingCsv(existingCsv);

    // Handle new rows by removing duplicates and adding the new row
    existingData = monthRows.reduce((acc, newRow) => {
      return handleDuplicateRow(newRow, acc, filenamePosition);
    }, existingRows);

    // Update row indices (if needed)
    const updatedRows = updateRowIndices(existingData, datePosition);

    // Combine the headers and rows for final CSV
    const finalCsv = `${existingHeaders.join("\n")}\n${updatedRows.join("\n")}`;
    fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
  } else {
    const finalCsv = `${headers}\n${monthRows.join("\n")}`;
    fs.writeFileSync(securedFilePath, finalCsv, "utf-8");
  }
};

const finalizeCsvFile = (
  formattedTimezone,
  secondStartOfMonth,
  secondEndOfMonth,
  reportName
) => {
  const securedFilePath = generateFilePath(
    formattedTimezone,
    secondStartOfMonth,
    secondEndOfMonth,
    reportName
  );
  const networkReportsFilePath = path.join(
    NETWORK_REPORTS.reports_directory,
    path.basename(securedFilePath)
  );

  fs.copyFileSync(securedFilePath, networkReportsFilePath);
  console.log(`CSV file saved and copied to: ${networkReportsFilePath}`);

  return networkReportsFilePath;
};

const generateFilePath = (formattedTimezone, start, end, reportName) =>
  path.join(
    secured_folder,
    `${reportName}_${start}-${end}-${formattedTimezone}.csv`
  );

const parseExistingCsv = (csv) => {
  const lines = csv.split("\n");
  return {
    existingHeaders: lines.slice(0, 2),
    existingRows: lines.slice(2),
  };
};

const handleDuplicateRow = (newRow, existingRows, filenamePosition) => {
  const newCdrFileName = newRow.split(",")[filenamePosition]; // File name (assuming it's in the 3rd column, index 2)

  // Remove the existing row with the same file name
  const updatedRows = existingRows.filter(
    (existingRow) => existingRow.split(",")[filenamePosition] !== newCdrFileName
  );

  // Add the new row to the updated rows
  updatedRows.push(newRow);

  return updatedRows;
};

const updateRowIndices = (rows, datePosition) => {
  const sortedRows = rows.sort((rowA, rowB) => {
    const dateA = new Date(rowA.split(",")[datePosition].replace(/"/g, ""));
    const dateB = new Date(rowB.split(",")[datePosition].replace(/"/g, ""));
    return dateA - dateB; // Sort in ascending order
  });

  // Update the serial number (index 0) based on the sorted rows
  return sortedRows.map((row, index) => {
    const rowParts = row.split(",");
    if (rowParts.length > 0) {
      rowParts[0] = (index + 1).toString(); // Update serial number
    }
    return rowParts.join(",");
  });
};


const addSequenceNumbers = (csvData) => {
  // console.log("csvData->", csvData);
  const calculatedFields = reportsMapping["Network Reports"].calculatedFields;

  let sNo = 1;
  let firstSeqNum = 1; // Start with first sequence number as 1

  return csvData.map((row, index) => {
    // Extract the date from the row for comparison
    const rowDate = row.Date.split(" ")[0]; // Just the date part (e.g., '2025-01-05')
    const prevRowDate =
      index > 0 ? csvData[index - 1].Date.split(" ")[0] : null;

    let lastSeqNum;

    // If it's the first row or the first row of the day, reset sequence numbers
    if (index === 0 || rowDate !== prevRowDate) {
      firstSeqNum = 1;
      lastSeqNum = row["Total SMS"];
    } else {
      lastSeqNum = firstSeqNum + row["Total SMS"];
    }

    // Creating new row with sequence numbers
    const newRow = {
      [calculatedFields.s_no]: sNo,

      ...row,
      [calculatedFields.first_seq_no]: firstSeqNum,
      [calculatedFields.last_seq_no]: lastSeqNum,
    };

    // Update first sequence number for next row
    firstSeqNum = lastSeqNum;
    sNo += 1;

    // console.log("newRow->", newRow)

    return newRow;
  });
};


const downloadNetworkReports = async (payload) => {
  try {
    let reportReqData = reportsMapping[payload.reportName];
    let searchClause = "";

    if (payload.search) {
      let fields = reportReqData.searchFields;

      for (let k = 0; k < fields.length; k++) {
        if (searchClause.length > 0) {
          searchClause = `${searchClause} or `;
        }
        searchClause = `${searchClause + fields[k]} ILIKE '%${payload.search}%' `;
      }

      if (searchClause.length > 0) {
        if (whereClause.length > 0) {
          whereClause = `(${whereClause}) and (${searchClause})`;
        } else {
          whereClause = searchClause;
        }
      }
    }

    const startDate = payload.startDate
      ? payload.startDate
      : dayjs.utc().subtract(3, "day").format("YYYY-MM-DD 00:00:00");

    const endDate = payload.endDate
      ? payload.endDate
      : dayjs.utc().subtract(1, "day").format("YYYY-MM-DD 23:59:59");

    // Parse dates with dayjs for validation
    const startDateObj = dayjs.utc(startDate);
    const endDateObj = dayjs.utc(endDate);

    // Ensure start and end dates are within 3 days
    const diffInDays = endDateObj.diff(startDateObj, "day");
    console.log("diffInDays", diffInDays);
    if (diffInDays > NETWORK_REPORTS.no_of_days) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `Start date and end date must be within ${NETWORK_REPORTS.no_of_days} days, now it is ${diffInDays}.`
      );
    }

    let { viewBy } = reportReqData.defaultFilters;
    if (payload.defaultViewBy) {
      viewBy = payload.defaultViewBy;
    } else if (
      reportReqData.defaultFilters.useDefaultViewBy == undefined ||
      reportReqData.defaultFilters.useDefaultViewBy == false
    ) {
      viewBy = await getViewByValue(startDate, endDate);
    }

    let whereClause = "";
    if (searchClause?.length > 0) {
      whereClause = `(${searchClause})`;
    }

    const baseData = {
      method: "post",
      url: `${restapiUri}/api/v1/download_report`,
      data: {
        view_by: viewBy,
        report_name: reportReqData.normalizedName,
        order_by: "timestamp",
        fields: Object.keys(reportReqData.responseFields),
        time_zone: payload.timezone,
      },
    };

    // const convertedData = await generateDailyData(baseData, startDate, endDate);

    let result = null;
    let downloadableData;
    try {
      const convertedData = await generateDailyData(baseData, startDate, endDate);
      if (whereClause?.length > 0) {
        data.data.where = whereClause;
      }

      const fetchedData = await fetchAndCombineData(convertedData, whereClause);

      downloadableData = processDownloadableData(fetchedData);

    } catch (error) {
      console.error("Error:", error.message);
    }

    // uTC date time convertion

    if (downloadableData.length < 2) {
      throw new ApiError(httpStatus.NOT_FOUND, "No record found"); // handle if there are no or invalid rows
    }

    // split the header row to get column names
    const headers = downloadableData[0].split("\t");

    // console.log("headers are : ", headers);
    // find index of columns with datetime values (assuming createdAt and updatedAt here)
    datetime = headers.indexOf("timestamp");

    rows = [];
    // iterate over each row (starting from the second row, as the first row is headers)
    for (let i = 1; i < downloadableData.length; i++) {
      //console.log("data is : " ,downloadableData[i])

      if (downloadableData[i] == null || downloadableData[i] == undefined) {
        downloadableData[i] = "-";
      } else {
        downloadableData[i] = downloadableData[i].replace(/\\N/g, "-");
      }
      let columns = downloadableData[i].split("\t");
      const regexPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/;

      if (columns.length > 0) {
        for (let j = 0; j < columns.length; j++) {
          if (regexPattern.test(columns[j])) {
            columns[j] = columns[j].split("+")[0];
            columns[j] = dayjs(columns[j]).format("YYYY-MMM-DD HH:mm:ss");
          } else if (columns[j] && !isNaN(columns[j])) {
            // Check if cell value is numeric
            columns[j] = parseFloat(columns[j]);
          }
        }

        rows.push(columns);
      }
    }

    if (rows.length == 0) {
      throw new ApiError(httpStatus.BAD_REQUEST, "No record found");
    }
    /*
      startDate = await convertToClientTime(startDate, user.timezone);
      endDate = await convertToClientTime(endDate, user.timezone);
      */
    let csv = rows.join("\n");
    let updatedCsv;

    // console.log("reportsMapping->", reportsMapping["Network Reports"]);

    const responseFields = reportsMapping[payload.reportName].responseFields;

    // Parse the CSV into an array of objects
    const parsedCsv = csv.split("\n").map((line) => {
      const rowValues = line.split(",");

      // Validate row data
      if (
        rowValues.length < Object.keys(responseFields).length ||
        rowValues.includes("")
      ) {
        return null; // Skip invalid or empty rows
      }

      // Map row values to responseFields dynamically
      const mappedRow = {};
      Object.keys(responseFields).forEach((key, index) => {
        const headerName = responseFields[key];
        mappedRow[headerName] = isNaN(rowValues[index])
          ? rowValues[index]
          : parseInt(rowValues[index], 10); // Convert numeric fields to integers
      });

      return mappedRow;
    });

    // Filter out invalid rows
    const validCsvRows = parsedCsv.filter((row) => row !== null);

    // Add sequence numbers
    updatedCsv = addSequenceNumbers(validCsvRows);

    // Convert JSON to CSV using json2csv
    const fields = Object.keys(updatedCsv[0]); // Extract headers dynamically
    const parser = new Parser({ fields });
    csv = parser.parse(updatedCsv);

    const flepath = await saveNetworkReports(
      payload,
      csv,
      startDate,
      endDate,
      payload.timezone
    );


    return Buffer.from(csv);

  } catch (error) {
    logger.error(`Error during network report ${error}`)
    console.error("error in network reports->", error)
    throw error
  }
};


module.exports = {
  downloadNetworkReports,
};