const auditLogService = require("./audit-log.service");
const { offline_downloads } = require("../config/config");
const logger = require("../config/logger");
const { columnMapping, rawCdr, restapiUri } = require("../config/reportConfig");
const { auditLogEvents } = require("../config/roles");
const ApiError = require("../utils/ApiError");
const { getCdrQuery, gerCdrDownloadQuery } = require("../utils/constructGrapghqlQuery");
const constructCSV = require("../utils/generateCsv");
const { saveCsvOffline, sendTarGzFromBuffer, createTarGzBuffer, createZipFile, createZipBuffer } = require("../utils/saveCsvOffline");
const graphqlService = require("./graphql.service");
const offlineDownloadService = require("./offlineDownload.service");
const dayjs = require("dayjs");
const httpStatus = require("http-status");
const { default: axios } = require("axios");
const constructPDF = require("../utils/generatePdf");
const constructExcel = require("../utils/generateExcelFile");
const path = require("path");
const { handleRegularFilters } = require("../utils/build-advanced-filter");
const { maskNumber, maskDownloadedRows } = require("../utils/maskNumber");
const config = require("../config/config");
const { PaddingDownloadedRows, padding } = require("../utils/padding");
const streamCsvToZipBuffer = require("../utils/streamCsvToZipBuffer");
const streamPdfToZipBuffer = require("../utils/streamPdfToZipBuffer");
const streamExcelToZipBuffer = require("../utils/streamExcelToZipBuffer");


function parseDownloadedData(rawData) {
  const lines = rawData.split("\n");
  if (lines.length < 2)
    throw new Error("No data available in downloadable response.");


  const headingResponse = lines[0].split("\t");
  let headers = [];
  for (let i = 0; i < headingResponse.length; i++) {
    const columnkey = Object.keys(columnMapping).find(
      (ckey) => columnMapping[ckey] === headingResponse[i]
    );
    headers.push(columnkey);
  }

  // const arrivaltime = headingResponse.indexOf("time_of_arrival");
  // const deliverytime = headingResponse.indexOf("time_of_delivery");
  // const eventdate = headingResponse.indexOf("event_date");

  // console.log("lines->", lines.slice(0, 3))

  const rows = [];
  for (let i = 1; i < lines.length; i++) {
    lines[i] = lines[i].replace(/\\N/g, "-");
    const columns = lines[i].split("\t");
    if (columns.length > 0) {
      rows.push(columns);
    }
  }
  
  if (rows.length === 0) throw new Error("Parsed rows are empty.");
  return { headers, rows };
}


const handleOfflineDownload = async (user, payload, filterClause, totalCount, res) => {
  if (payload.type !== "CSV") {
    return res.status(httpStatus.BAD_REQUEST).send({
      message: "Only CSV download is supported for raw cdrs",
    });
  }

  let startDate = payload.startDate;
  let endDate = payload.endDate;
  const fileName = payload.fileName || `CDR_Report_${startDate}_${endDate}`;
  let offlineDataRes = null;

  try {
    if (payload.timezone) user.timezone = payload.timezone;

    logger.info(`Creating download entry for ${fileName}`);

    const filePath = path.resolve(
      offline_downloads.download_directory,
      `${fileName}.csv`
    );

    const offlineFileData = {
      userId: user.id,
      initiatedBy: user.name,
      fileName,
      reportName: fileName,
      category: offline_downloads.category.cdr,
      filePath: filePath,
      status: offline_downloads.status.INITIATED,
    };

    offlineDataRes = await offlineDownloadService.createOfflineDownload(offlineFileData);

    // Send response immediately
    res.send({
      isOfflineDownload: true,
      message: offline_downloads.message,
    });
    logger.info("response sent", offline_downloads.message);

        const pageSize = config.NO_OF_DATA_PER_QUERY;
    const totalPages = Math.ceil(totalCount / pageSize);

    const payloadArray = []
    // If the total count exceeds the maximum rows for fetching reports, create multiple queries
    if (totalCount > pageSize) {
      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        payloadArray.push({
          ...payload,
          page: pageNumber,
          limit: Number(pageSize),
          download: 1,
        });
      }
    } else {
      payloadArray.push({
        ...payload,
        page: 1,
        limit: Number(totalCount),
        download: 1,
      });
    }
    logger.info(`Total pages to process: ${totalPages}`);
    // Process each page
    for (let i = 0; i < payloadArray.length; i++) {
      const payload = payloadArray[i];

      const data = gerCdrDownloadQuery({ filterClause, payload, rawCdr, user, startDate, endDate });

      if (filterClause?.length > 0) {
        data.data.where = filterClause;
      }

      logger.info(`Request to download CDR : ${JSON.stringify(data)}`);

      const response = await axios(data);

      let { headers, rows } = parseDownloadedData(response.data);

      console.log(`rows for page ${i+1}-> ${rows.length}`)

      rows = PaddingDownloadedRows(headers, rows)

      // Mask row values
      if (!user.isSuperAdmin && user.reportMasking) {
        rows = maskDownloadedRows(headers, rows);
      }

      const csv = await constructCSV(headers, rows, payload)

      // await saveCsvOffline(csv, offlineDataRes, true);
      await saveCsvOffline(csv, offlineDataRes, i === payloadArray.length - 1);  
    }


    // const data = gerCdrDownloadQuery({ filterClause, payload, rawCdr, user, startDate, endDate });

    // if (filterClause?.length > 0) {
    //   data.data.where = filterClause;
    // }

    // logger.info(`Request to download CDR : ${JSON.stringify(data)}`);

    // const response = await axios(data);

    // let { headers, rows } = parseDownloadedData(response.data);

    // // Mask row values
    // if(!user.isSuperAdmin && user.reportMasking) {
    //   rows = maskDownloadedRows(headers, rows);
    // }

    // const csv = await constructCSV(headers, rows, payload)

    // // await saveCsvOffline(csv, offlineDataRes, true);
    // await saveCsvOffline(csv, offlineDataRes, true);

  } catch (err) {
    logger.error(`Offline download failed: ${err.message}`);

    if (offlineDataRes) {
      await offlineDownloadService.updateOfflineDownload(offlineDataRes.id, {
        status: offline_downloads.status.FAILED,
      });
    }

    // await auditLogService.addAuditLog({
    //   username: user.email,
    //   userId: user.id,
    //   roleName: user.role.name,
      // event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading ${err.message}`,
    // });
  }

  return null;
};


const downloadRawCdrs = async (user, payload, filterClause, totalCount) => {
  try {
    if (payload.timezone) user.timezone = payload.timezone;

    if (payload.startDate) {
      startDate = payload.startDate;
    }

    if (payload.endDate) {
      endDate = payload.endDate;
    }

    payload = {
      ...payload,
      page: 1,
      limit: totalCount
    }

    let whereClause = filterClause;

    const data = gerCdrDownloadQuery({ filterClause, payload, rawCdr, user, startDate, endDate });

    // const data = {
    //   method: "post",
    //   url: `${restapiUri}/api/v1/fetch_raw_cdr`,
    //   data: {
    //     start_time: startDate.trim(),
    //     end_time: endDate.trim(),
    //     fields: rawCdr.responseFields,
    //     time_zone: user.timezone,
    //     cdr_type: payload.cdrType || 'single'
    //   },
    // };

    if (whereClause.length > 0) {
      data.data.where = whereClause;
    }

    logger.info(`Request to download CDR : ${JSON.stringify(data)}`);

    const response = await axios(data);

    let { headers, rows } = parseDownloadedData(response.data);

    rows = PaddingDownloadedRows(headers, rows)

    // Mask row values
    if(!user.isSuperAdmin && user.reportMasking) {
      rows = maskDownloadedRows(headers, rows);
    }

    switch (payload.type) {
      case "EXCEL":
        // return constructExcel(headers, rows, payload);
        return streamExcelToZipBuffer(headers, rows, payload);
      case "PDF":
        // return await constructPDF(headers, rows, payload);
        return await streamPdfToZipBuffer(headers, rows, payload);
      default:
        // return await constructCSV(headers, rows, payload);
        return await streamCsvToZipBuffer(headers, rows, payload);
    }

  } catch (error) {
    logger.error(`Download CDR error: ${error.message}`);

    if (error.response && error.response.data) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        JSON.stringify(error.response.data.detail)
      );
    }

    // let auditLogPayload = {
    //   username: user.email,
    //   userId: user.id,
    //   roleName: user.role.name,
    //   event: auditLogEvents.DOWNLOAD_FAILED,
    //   action: `Failed downloading raw cdr ${error.message}`,
    // };
    // auditLogService.addAuditLog(auditLogPayload);

    throw new ApiError(
      error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Something went wrong. Please try again later"
    );
  }
};


const getrawcdrs = async (user, payload, options, res) => {
  let limit = 10, page = 1;

  if (options) {
    limit = options.limit || 100;
    page = options.page || 1;
  }

  if(!user.timezone) user.timezone = config.DEFAULT_TIMEZONE

  if (payload.timezone) user.timezone = payload.timezone;

  payload.startDate = payload.startDate ? payload.startDate.trim() : dayjs().tz(user.timezone).startOf("day").format("YYYY-MM-DD HH:mm:ss");
  payload.endDate = payload.endDate ? payload.endDate.trim() : dayjs().tz(user.timezone).endOf("day").format("YYYY-MM-DD HH:mm:ss");
  
  // let filterClause = `(customer_bind!='0' and customer_bind!='-1' and supplier_bind!='0' and supplier_bind!='-1')`;
  let filterClause = '';
  
  // let filterParts = [];

  // for (const { field, value } of filters) {
  //   const mappedField = columnMapping[field];
  //   if (!mappedField) continue;
  
  //   if (field === "Transaction Type" && Array.isArray(value)) {
  //     const values = value.map(val => `({${mappedField}}='${val}')`).join(" or ");
  //     filterParts.push(`(${values})`);
  //   } else {
  //     filterParts.push(`({${mappedField}}='${value}')`);
  //   }
  // }
  
  // filterClause = filterParts.join(' and ');


  // for (const { field, value } of payload.filters) {
  //   if(payload.cdrType === "multiple" && field === "event_id") {
  //     onlyCdrTypeFields = true
  //   }
  // }

  const andGroups = handleRegularFilters(payload.filters, rawCdr)

  filterClause = andGroups.length > 0 ? andGroups.join(" and ") : null;

  console.log("filterClause->", filterClause);


  try {
    // Offline download flow
    if (payload?.download == 1) {

      const query = getCdrQuery({ filterClause, payload, rawCdr, user, page:1, limit:1 });

      const response = await graphqlService.getGraphqlData({
        name: rawCdr.name,
        payload: query,
      });

      const totalCount = response.page_meta.total_items;

      console.log("totalCount->", totalCount);

      if (!totalCount) {
        throw new ApiError(httpStatus.BAD_REQUEST, "Total count is required");
      }

      console.log("Number(totalCount) > offline_downloads.initiate_offline_download_rows->", Number(totalCount) > offline_downloads.initiate_offline_download_rows)

      if (Number(totalCount) > offline_downloads.initiate_offline_download_rows) {
        return await handleOfflineDownload(user, payload, filterClause, totalCount, res);
      }

      const file = await downloadRawCdrs(user, payload, filterClause, totalCount);

      logger.info(`cdr file processed, ready for compress`)

      res.setHeader("Content-Type", "application/zip");
      res.setHeader("Content-Disposition", `attachment; filename=${file.filename}`);
      res.send(file.buf);

      // const compressedFile = await sendTarGzFromBuffer(file.buf, file.filename, res);
      // Create a zipbuffer from the file

      // const compressedFile = await createZipBuffer([file]);

      // // Send the zipbuffer as a response
      // console.log("compressed buffer received")
      // res.setHeader("Content-Type", "application/zip");
      // res.setHeader("Content-Disposition", `attachment; filename=${file.filename}.zip`);
      // res.send(compressedFile);

      return
    }

    // Regular query and response flow
    const query = getCdrQuery({ filterClause, payload, rawCdr, user, page, limit });

    const response = await graphqlService.getGraphqlData({
      name: rawCdr.name,
      payload: query,
    });

    let data = response.items.map((obj) => {
      const newObj = {};

      for (const key in obj) {
        let value = obj[key];

        const isDateTime = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/.test(value);
        if (isDateTime) {
          value = dayjs(value.split("+")[0]).format("YYYY-MMM-DD HH:mm:ss");
        }

        const columnKey = Object.keys(columnMapping).find(
          (ckey) => columnMapping[ckey] === key
        );

        if (columnKey) {
          newObj[columnKey] = value;
        }
      }

      return newObj;
    });

    data = padding(data)

    //Masking the number
    if (!user.isSuperAdmin && user.reportMasking && data) {
        data = maskNumber(data);
    }

    return {
      totalCount: response.page_meta.total_items,
      pageNo: response.page_meta.current_page,
      pageSize: response.page_meta.page_size,
      data,
    };
  } catch (err) {
    logger.error("Failed in getrawcdrs:", err.message);
    throw err; // Let caller handle final error response
  }
};



module.exports = {
  getrawcdrs,
};