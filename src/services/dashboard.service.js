const httpStatus = require("http-status");
const { Op, ValidationError } = require("sequelize");
const { models } = require("../models");
const ApiError = require("../utils/ApiError");
const { columnMapping } = require("../config/reportConfig");
const graphqlService = require("./graphql.service");
const { Dashboard, Panel, Card, User } = models;
const {
  convertToClientTime,
  getCustomerSupplierFilter,
  convertToUTC,
  addCustomerBindClause,
} = require("../utils/misc");
const logger = require("../config/logger");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const config = require("../config/config");
const { adminUser } = require("../config/roles");
const findFilterFlag = require("../utils/findFilterFlag");
dayjs.extend(utc);
dayjs.extend(timezone);
const roleConfig = require("../config/roles");

const createDashboard = async (payload) => {
  let dashboard;
  try {
    dashboard = await Dashboard.create(payload);
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        logger.error(`Sql error : ${error}`);
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Dashboard name used before"
            );

          case "is_null":
          case "notEmpty":
            if (error.path == "name") {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                "Name cannot be empty"
              );
            }
        }
      });
    }
  }
  return dashboard;
};

const getDashboard = async (id) => {
  const dashboard = await Dashboard.findOne({
    where: { id },
    raw: true,
  });
  if (!dashboard) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Dashboard does not exists");
  }
  let panels = [],
    cards = [];
  if (dashboard.panels) {
    for (let i = 0; i < dashboard.panels.length; i++) {
      if (dashboard.panels[i].id) {
        let tmpPanel = await Panel.findOne({
          where: { id: dashboard.panels[i].id },
        });
        if (tmpPanel) {
          panels.push({
            panelDetails: tmpPanel,
            order: dashboard.panels[i].order,
          });
        }
      }
    }
  }
  if (dashboard.cards) {
    for (let i = 0; i < dashboard.cards.length; i++) {
      if (dashboard.cards[i].id) {
        let tmpcard = await Card.findOne({
          where: { id: dashboard.cards[i].id },
        });
        if (tmpcard) {
          cards.push({
            cardDetails: tmpcard,
            order: dashboard.cards[i].order,
          });
        }
      }
    }
  }
  let response = {
    name: dashboard.name,
    panels,
    cards,
    createdAt: dashboard.createdAt,
  };
  return response;
};

const getDashboardByName = async (createdBy, name) => {
  const dashboard = await Dashboard.findOne({
    where: {
      name,
      createdBy,
    },
  });
  return dashboard;
};

const getDashboards = async (user, filter, options) => {
  let dashboards, limit, offset, page, totalCount, userName;
  if (options) {
    limit = Number(options.limit) || null;
    page = Number(options.page) || null;
    offset = (page - 1) * limit;
  }
  let createdBy;
  if (user.isSuperAdmin && filter && filter.createdBy) {
    createdBy = filter.createdBy;
    let createdUserDetails = await User.findByPk(createdBy);
    if (!createdUserDetails) {
      return {
        totalCount: 0,
        pageNo: page,
        pageSize: limit,
        data: [],
      };
    }
    userName = createdUserDetails.name;
    delete filter.createdBy;
  } else {
    createdBy = user.id;
    userName = user.name;
  }
  if (Object.keys(filter).length == 0) {
    dashboards = await Dashboard.findAndCountAll({
      limit,
      offset,
      where: { createdBy },
      order: [["createdAt", "DESC"]],
    });
  } else if (filter.search) {
    let searchQuery = filter.search;
    dashboards = await Dashboard.findAndCountAll({
      limit,
      offset,
      where: {
        [Op.or]: {
          name: { [Op.like]: `%${searchQuery}%` },
        },
        createdBy,
      },
      order: [["createdAt", "DESC"]],
    });
  }

  let data = [];
  for (let i = 0; i < dashboards.rows.length; i++) {
    let panelCount = 0,
      cardCount = 0,
      panels = [],
      cards = [];
    if (dashboards.rows[i].panels) {
      for (let j = 0; j < dashboards.rows[i].panels.length; j++) {
        let panelData = await Panel.findByPk(dashboards.rows[i].panels[j].id);
        if (panelData) {
          panelCount++;
          panels.push(dashboards.rows[i].panels[j]);
        }
      }
    }

    if (dashboards.rows[i].cards) {
      for (let j = 0; j < dashboards.rows[i].cards.length; j++) {
        if (dashboards.rows[i].cards[j]) {
          let cardData = await Card.findByPk(dashboards.rows[i].cards[j].id);
          if (cardData) {
            cardCount++;
            cards.push(dashboards.rows[i].cards[j]);
          }
        }
      }
    }

    if (
      dashboards.rows[i].cards.length != cards.length ||
      dashboards.rows[i].panels.length != panels.length
    ) {
      await Dashboard.update(
        {
          panels,
          cards,
        },
        {
          where: {
            id: dashboards.rows[i].id,
          },
        }
      );
    }
    data.push({
      id: dashboards.rows[i].id,
      name: dashboards.rows[i].name,
      panelCount,
      cardCount,
      createdAt: await convertToClientTime(
        dashboards.rows[i].createdAt,
        user.timezone
      ),
      createdBy: userName,
      sharedByAdmin: false,
    });
  }
  totalCount = dashboards.count;
  if (!user.isSuperAdmin) {
    let dashboardsShared = user.role.dataValues.dynamicDashboard;
    if (dashboardsShared) {
      let sharedD = [];
      Object.keys(dashboardsShared).forEach((key) => {
        if (dashboardsShared[key] > 0) {
          sharedD.push(key);
        }
      });

      totalCount += sharedD.length;
      let sharedCount,
        startPoint = 0;
      if (dashboards.rows.length < limit) {
        sharedCount = limit - dashboards.rows.length;
      }
      if (dashboards.rows.length == 0) {
        startPoint = offset - dashboards.count;
      }

      for (let i = startPoint; i < sharedD.length && i < sharedCount; i++) {
        if (filter.search) {
          if (!sharedD[i].includes(filter.search)) {
            totalCount -= 1;
            continue;
          }
        }
        let dd = await Dashboard.findAll({
          where: { name: sharedD[i] },
          raw: true,
        });
        if (dd && dd.length > 0) {
          for (let k = 0; k < dd.length; k++) {
            createdUserDetails = await User.findOne({
              where: { id: dd[k].createdBy },
              raw: true,
            });

            if (createdUserDetails && createdUserDetails.isSuperAdmin) {
              dd = dd[k];
              userName = createdUserDetails.name;

              let panelCount = 0,
                cardCount = 0,
                panels = [],
                cards = [];
              if (dd.panels) {
                for (let j = 0; j < dd.panels.length; j++) {
                  let panelData = await Panel.findByPk(dd.panels[j].id);
                  if (panelData) {
                    panelCount++;
                    panels.push(dd.panels[j]);
                  }
                }
              }
              if (dd.cards) {
                for (let j = 0; j < dd.cards.length; j++) {
                  let cardData = await Card.findByPk(dd.cards[j].id);
                  if (cardData) {
                    cardCount++;
                    cards.push(dd.cards[j]);
                  }
                }
              }
              if (
                dd.cards.length != cards.length ||
                dd.panels.length != panels.length
              ) {
                await Dashboard.update(
                  {
                    panels,
                    cards,
                  },
                  {
                    where: {
                      id: dd.id,
                    },
                  }
                );
              }
              data.push({
                id: dd.id,
                name: dd.name,
                panelCount,
                cardCount,
                createdAt: await convertToClientTime(
                  dd.createdAt,
                  user.timezone
                ),
                createdBy: userName,
                sharedByAdmin: true,
              });
            }
          }
        }
      }
    }
  }

  let responseTobeSent = {
    totalCount,
    pageNo: page || null,
    pageSize: limit || null,
    data,
  };
  return responseTobeSent;
};

const updateDashboard = async (id, payload) => {
  let dashboard = await Dashboard.findOne({ where: { id } });
  if (!dashboard) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Dashboard does not exists");
  }
  try {
    Object.assign(dashboard, payload);
    await dashboard.save();
    return dashboard;
  } catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          case "not_unique":
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "Dashboard name used before"
            );
          default:
            throw new ApiError(httpStatus.BAD_REQUEST, error.message);
        }
      });
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, e);
    }
  }
};

const deleteDashboard = async (id) => {
  let dashboard = await getDashboard(id);
  await Dashboard.destroy({ where: { id } });
  return dashboard;
};

const getDashboardView = async (user, id, filter) => {

  const dashboard = await Dashboard.findOne({
    where: { id },
    raw: true,
  });
  if (!dashboard) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Dashboard does not exists");
  }
  const panels = [];
  const cards = [];
  if (dashboard.panels) {
    for (let i = 0; i < dashboard.panels.length; i++) {
      if (dashboard.panels[i].id) {
        const tmpPanel = await Panel.findOne({
          where: { id: dashboard.panels[i].id },
        });
        if (tmpPanel) {
          panels.push({
            panelDetails: tmpPanel,
            order: dashboard.panels[i].order,
          });
        }
      }
    }
  }
  if (dashboard.cards) {
    for (let i = 0; i < dashboard.cards.length; i++) {
      if (dashboard.cards[i].id) {
        let tmpcard = await Card.findOne({
          where: { id: dashboard.cards[i].id },
          raw: true,
        });
        if (tmpcard) {
          cards.push({
            cardDetails: tmpcard,
            order: dashboard.cards[i].order,
          });
        }
      }
    }
    for (let j = 0; j < cards.length; j++) {
      const cardsService = require("./cards.service");
      let card = cards[j].cardDetails;
      const cardDetails = await cardsService.fetchCardWithValue(user, card, filter);
      cards[j].cardDetails = cardDetails;
    }
  }

  let response = {
    name: dashboard.name,
    panels,
    cards,
    createdAt: dashboard.createdAt,
  };
  return response;
};

module.exports = {
  createDashboard,
  getDashboard,
  getDashboardByName,
  getDashboards,
  updateDashboard,
  deleteDashboard,
  getDashboardView,
};
