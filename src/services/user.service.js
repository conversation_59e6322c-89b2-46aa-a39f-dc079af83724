const httpStatus = require('http-status');
const { Op,ValidationError } = require('sequelize');
const { models } = require('../models');
const ApiError = require('../utils/ApiError');
const roleService = require('./role.service');
const { convertToClientTime } = require('../utils/misc')
const { defaultUserRole,auditLogEvents } = require('../config/roles');
const reportsService = require('./reports.service');
const auditLogService = require('./audit-log.service'),

   { User,Role } = models,
  /**
   * create a user
   * @param {Object} userBody
   * @returns {Promise<User>}
   */

  createUser = async (userBody,ldapUser = false) => {
    if (await User.isEmailTaken(userBody.email)) {
      throw new ApiError(httpStatus.BAD_REQUEST,'Email already taken');
    }
    if (!ldapUser && (!userBody.password || userBody.password.length <= 0)) {
      throw new ApiError(httpStatus.BAD_REQUEST,'Password is required');
    }
    if (userBody.role) {
      userBody.roleId = userBody.role;
    }
    else {
      let default_role = await roleService.getRoleByName(defaultUserRole.name);
      // console.log("default role is ", default_role)
      if (default_role) {
        userBody.roleId = default_role.id;
      }
    }
    if (userBody.customerList && userBody.customerList.length > 0) {
      try {
        userBody.customerList = JSON.parse(userBody.customerList);
      }
      catch (err) {
        userBody.customerList = [];
      }
    }
    if (userBody.supplierList && userBody.supplierList.length > 0) {
      try {
        userBody.supplierList = JSON.parse(userBody.supplierList);
      }
      catch (err) {
        userBody.customerList = [];
      }
    }


    let user = null;
    try {
      if(userBody.isLdapUser == true || userBody.isLdapUser == 'true')
      {
        userBody.isEmailVerified = true
      }
      user = await User.create(userBody);
    }
    catch (e) {
      if (e instanceof ValidationError) {
        e.errors.forEach((error) => {

          switch (error.validatorKey) {

            default:
              throw new ApiError(httpStatus.BAD_REQUEST,`Error ${error.message}`);
          }
        });
      }
      else if (e.name == "SequelizeForeignKeyConstraintError") {

        throw new ApiError(httpStatus.BAD_REQUEST,'Not a valid role ID');
      }
    }

    return user;
  },


  /**
   * query for users
   * @param {Object} filter - Mongo filter
   * @param {Object} options - Query options
   * @param {string} [options.sortBy] - Sort option in the format: sortField:(desc|asc)
   * @param {number} [options.limit] - Maximum number of results per page (default = 10)
   * @param {number} [options.page] - Current page (default = 1)
   * @returns {Promise<QueryResult>}
   */
  queryUsers = async (user,filter,options) => {
    let limit = null;
    let offset,page,users;
    if (options) {
      limit = options.limit || null;
      page = options.page || null;
      offset = (page - 1) * limit;
    }
    if (Object.keys(filter).length == 0) {
      users = await User.findAndCountAll({
        limit,
        offset,
        where: { isSuperAdmin: false },
        include: {
          model: Role,
          attributes: [
            'name',
            'resources'
          ]
        },
        order: [
          [
            'createdAt',
            'DESC'
          ]
        ]

      });
    }
    else if (filter.search) {
      let searchQuery = filter.search;
      filter = `{[Op.or]: [name: { [Op.like]: '%' ${searchQuery}'%' },description: { [Op.like]: '%' + searchQuery2 + '%' }]}`
      users = await User.findAndCountAll({
        limit,
        offset,
        where: {
          isSuperAdmin: false,
          [Op.or]: {
            name: { [Op.like]: `%${searchQuery}%` },
            '$role.name$': { [Op.like]: `%${searchQuery}%` }
          }
        },
        include: {
          model: Role,
          attributes: ['name']
        },
        order: [
          [
            'createdAt',
            'DESC'
          ]
        ]
      });
    }
    else {
      filter.isSuperAdmin = false;
      users = await User.findAndCountAll({
        limit,
        offset,
        where: filter,
        include: {
          model: Role,
          attributes: ['name']
        },
        order: [
          [
            'createdAt',
            'DESC'
          ]
        ]
      });
    }
      // console.log("users->", users)

    for (let i = 0; i < users.rows.length; i++) {      
      users.rows[i].dataValues.createdAt = await convertToClientTime(users.rows[i].createdAt,user.timezone);
      users.rows[i].dataValues.updatedAt = await convertToClientTime(users.rows[i].updatedAt,user.timezone);
      if(users.rows[i].dataValues.lastLoggedin) {
        users.rows[i].dataValues.lastLoggedin = await convertToClientTime(users.rows[i].dataValues.lastLoggedin,user.timezone); 
      }

      // console.log("users.rows[i].dataValues->", users.rows[i])
      // if(users.rows[i].dataValues.allCustomerList) {
      //   console.log("Inside all allCustomerList")
      //   const customerList = await reportsService.getCustomerList(users.rows[i])
        // users.rows[i].customerList = customerList
      // }

      // if(users.rows[i].dataValues.allSupplierList) {
        // const supplierList = await reportsService.getSupplierList(users.rows[i].dataValues)
        // users.rows[i].supplierList = supplierList
      // }

      let custList = [],
      suppList = [];

      if (users.rows[i].customerList && users.rows[i].customerList.length > 0) {
        for (let j = 0; j < users.rows[i].customerList.length; j++) {
          custList.push(users.rows[i].customerList[j].id) 
        }
      }

      if (users.rows[i].supplierList && users.rows[i].supplierList.length > 0) {
        for (let j = 0; j < users.rows[i].supplierList.length; j++) {
          suppList.push(users.rows[i].supplierList[j].id) 
        }
      }
      users.rows[i].customerList = custList;
      users.rows[i].supplierList = suppList;
    }
    let responseTobeSent = {
      totalCount: users.count,
      pageNo: page,
      pageSize: limit,
      data: users.rows
    }
    return responseTobeSent;
  },

  /**
   * get user by id
   * @param {ObjectId} id
   * @returns {Promise<User>}
   */
  getUserById = async (id) => User.findByPk(id),

  /**
   * get user by email
   * @param {string} email
   * @returns {Promise<User>}
   */

  getUserByEmail = async (email) => {
    try {
      return await User.findOne({
        where: { email },
        include: {
          model: Role
        }
      })
    }
    catch (err) {
      let auditLogPayload = {
        username: email,
        userId: "",
        roleName: "",
        event: auditLogEvents.MYSQL_FAILURE,
        action: `Mysql error ${err.message}`
      }
      auditLogService.addAuditLog(auditLogPayload)
      throw new ApiError(httpStatus.UNAUTHORIZED,'User is not active');
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR,'Something went wrong. Pls try again after sometime');

    }
  },


/**
 * update user by id
 * @param {ObjectId} userId
 * @param {Object} updateBody
 * @returns {Promise<User>}
 */
updateUserById = async (userId,updateBody) => {
  const user = await getUserById(userId);
  let userBody = {};
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND,'User not found');
  }
  if (updateBody.email && await User.isEmailTaken(updateBody.email,userId)) {
    throw new ApiError(httpStatus.BAD_REQUEST,'Email already taken');
  }
  if (updateBody.customerList && updateBody.customerList.length > 0) {
    try {
      userBody.customerList = JSON.parse(userBody.customerList);
    }
    catch (err) {
      userBody.customerList = [];
    }
  }
  if (updateBody.supplierList && updateBody.supplierList.length > 0) {
    try {
      userBody.supplierList = JSON.parse(userBody.supplierList);
    }
    catch (err) {
      userBody.supplierList = [];
    }
  }
  try {
    Object.assign(user,updateBody);
    if (updateBody.password) {
      user.password = await user.generatePasswordHash();
    }
    await user.save();
    return user;
  }
  catch (e) {
    if (e instanceof ValidationError) {
      e.errors.forEach((error) => {
        let message;
        switch (error.validatorKey) {
          default:
            throw new ApiError(httpStatus.BAD_REQUEST,`Error ${error.validatorKey}`);
        }
      });
    }
    else {
      throw new ApiError(httpStatus.BAD_REQUEST,`Error ${e.message}`);
    }
  }
},

  /**
   * update user
   * @param {Object} user
   * @param {Object} updateBody
   * @returns {Promise<User>}
   */
  updateUser = async (userOld,updateBody) => {

    if (updateBody.email && userOld.email != updateBody.email && await User.isEmailTaken(updateBody.email,user.id)) {
      throw new ApiError(httpStatus.BAD_REQUEST,'Email already taken');
    }
    if (updateBody.role) {
      updateBody.roleId = updateBody.role;
    }

    if (updateBody.customerList && updateBody.customerList.length > 0) {
      try {
        updateBody.customerList = JSON.parse(updateBody.customerList);
      }
      catch (err) {
        updateBody.customerList = [];
      }
    } 

    if (updateBody.supplierList && updateBody.supplierList.length > 0) {
      try {
        updateBody.supplierList = JSON.parse(updateBody.supplierList);
      }
      catch (err) {
        updateBody.customerList = [];
      }
    }

    // if(!updateBody.supplierList || updateBody.supplierList.length === 0) {
    //   updateBody.allSupplierList = false
    // }

    // if(!updateBody.customerList || updateBody.customerList.length === 0) {
    //   updateBody.allCustomerList = false
    // }


    Object.assign(userOld,updateBody);
    if (updateBody.password) {
      userOld.password = await userOld.generatePasswordHash();
    }

    try {
      await userOld.save();
    }
    catch (e) {
      if (e instanceof ValidationError) {
        e.errors.forEach((error) => {
          switch (error.validatorKey) {
            default:
              throw new ApiError(httpStatus.BAD_REQUEST,`Error ${error.validatorKey}`);
          }
        });
      }
      else {
        throw new ApiError(httpStatus.BAD_REQUEST,`Error ${e.message}`);
      }
    }
    return userOld;
  },

  /**
   * delete user by id
   * @param {ObjectId} userId
   * @returns {Promise<User>}
   */
  deleteUserById = async (userId) => {
    await User.destroy({ where: { id: userId } });
    return 1;
  };

module.exports = {
  createUser,
  queryUsers,
  getUserById,
  getUserByEmail,
  updateUserById,
  updateUser,
  deleteUserById,
};
