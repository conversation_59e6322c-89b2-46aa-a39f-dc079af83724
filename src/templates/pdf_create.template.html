<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'Franklin Gothic Medium', '<PERSON>l Narrow', <PERSON>l, sans-serif;
            margin: 5px;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: auto; /* Set table width to auto */
            border-collapse: collapse;
            white-space: nowrap; /* Prevent text wrapping in cells */
        }

        th, td {
            border: 1px solid black;
            padding: 2px;
            text-align: left;
            min-width: 100px; /* Set minimum width for columns */
        }

        th {
            background-color: #f2f2f2;
            position: sticky; /* Sticky header */
            top: 0;
            z-index: 1;
        }
    </style>
</head>

<body>
    <h2>Dynamic Table in PDF</h2>
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    {{#each headers}}
                    <th>{{this}}</th>
                    {{/each}}
                </tr>
            </thead>
            <tbody>
                {{#each rows}}
                <tr>
                    {{#each this}}
                    <td>{{this}}</td>
                    {{/each}}
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>
</body>

</html>