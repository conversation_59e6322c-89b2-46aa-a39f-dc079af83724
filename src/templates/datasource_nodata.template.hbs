<!DOCTYPE html>
<html>
<head>
  <title>Datasource No Data Notification</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333333;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: #ffc107; /* Yellow for warning */
      color: #333333;
      padding: 10px 20px;
      text-align: center;
      border-radius: 8px 8px 0 0;
    }
    .content {
      margin: 20px 0;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777777;
      text-align: center;
    }
    .alert-list {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }
    .alert-list th, .alert-list td {
      text-align: left;
      padding: 10px;
      border-bottom: 1px solid #ddd;
    }
    .alert-list th {
      background-color: #f2f2f2;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <h1>Datasource Returned No Data</h1>
    </div>
    <div class="content">
      <p>Dear Admin Team,</p>
      <p>
        The following alert rules executed successfully but returned no data from the data source. This could mean the data is missing or the query conditions are no longer valid.
      </p>
      <p>The following alert rules are affected:</p>
      <table class="alert-list">
        <thead>
          <tr>
            <th>Alert Rule with No Data</th>
          </tr>
        </thead>
        <tbody>
          {{#each data}}
            <tr>
              <td>{{this.[No data Alert Rule]}}</td>
            </tr>
          {{/each}}
        </tbody>
      </table>
      <p>Please verify that the data pipeline is functioning correctly and that the queries for these rules are still relevant.</p>
      <p>Thank you,</p>
      <p>The Monitoring Team</p>
    </div>
    <!-- Footer -->
    <p class="footer">
      Thanks & Regards,
      <br />
      {{teamName}}
    </p>
  </div>
</body>
</html>
