<html>
<head>
  <title>Volume Dip Notification</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      background-color: #ffffff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 20px;
      margin: auto;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    }

    .header {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .highlight {
      font-style: italic;
      color: #d61e3c;
    }

    .table-container {
      width: 100%;
      margin-top: 20px;
      border-collapse: collapse;
      font-size: small;
    }

    .table-container th {
      background-color: #d61e3c;
      color: white;
      padding: 8px;
      border: 1px solid #ddd;
      text-align: left;
    }

    .table-container td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
      word-wrap: break-word;
    }

    .table-container tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .table-container tr:hover {
      background-color: #f1f1f1;
    }

    .blue-header {
      background-color: #0074d9;
      color: #fff;
      text-align: center;
      padding: 10px;
      font-size: 16px;
      margin-bottom: 20px;
      border-radius: 3px;
    }

    .footer {
      margin-top: 20px;
      font-size: 14px;
      color: #555;
    }
  </style>
</head>

<body>
  <div class="container">
    <p>Hi Team,</p>
    <p>
      We have noticed
      <span class="highlight">{{notificationType}}</span>
      for
      <strong>{{groupKey}} {{groupValue}}</strong>
      towards the below-mentioned destination. Kindly review and take action.
    </p>

    <!-- Blue Header -->
    <div class="blue-header">{{groupValue}} {{startTime}} - {{endTime}}</div>

    <!-- Table -->
    <table class="table-container">
      <thead>
        <tr>
          {{#if data}}
            {{#if (length data)}}
              {{#each (keys (lookup data 0)) as |key|}}
                <th>{{key}}</th>
              {{/each}}
            {{/if}}
          {{/if}}
        </tr>
      </thead>
      <tbody>
        {{#each data}}
          <tr>
            {{#each (values this) as |value|}}
              <td
                {{#if (eq key "volumeVariation")}}
                  style="background-color: {{#if (gt value 0)}}lightgreen{{else}}lightcoral{{/if}};"
                {{/if}}>
                {{value}}
              </td>
            {{/each}}
          </tr>
        {{/each}}
      </tbody>
    </table>

    <!-- Footer -->
    <p class="footer">
      Thanks & Regards,
      <br />
      {{teamName}}
    </p>
  </div>
</body>
</html>
