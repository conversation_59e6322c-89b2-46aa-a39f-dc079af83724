<!DOCTYPE html>
<html>
<head>
  <title>Datasource Error Notification</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333333;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: #dc3545; /* Red for error */
      color: #ffffff;
      padding: 10px 20px;
      text-align: center;
      border-radius: 8px 8px 0 0;
    }
    .content {
      margin: 20px 0;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777777;
      text-align: center;
    }
    .alert-list {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }
    .alert-list th, .alert-list td {
      text-align: left;
      padding: 10px;
      border-bottom: 1px solid #ddd;
    }
    .alert-list th {
      background-color: #f2f2f2;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <h1>Datasource Connection Error</h1>
    </div>
    <div class="content">
      <p>Dear Admin Team,</p>
      <p>
        An error was encountered while trying to query the data source. This may indicate an issue with the database connection, a malformed query, or a timeout.
      </p>
      <p>The following alert rules are affected:</p>
      <table class="alert-list">
        <thead>
          <tr>
            <th>Failing Alert Rule</th>
          </tr>
        </thead>
        <tbody>
          {{#each data}}
            <tr>
              <td>{{this.[Failing Alert Rule]}}</td>
            </tr>
          {{/each}}
        </tbody>
      </table>
      <p>Please investigate the health of the data source and the configuration of these alerts.</p>
      <p>Thank you,</p>
      <p>The Monitoring Team</p>
    </div>
    <!-- Footer -->
    <p class="footer">
      Thanks & Regards,
      <br />
      {{teamName}}
    </p>
  </div>
</body>
</html>
