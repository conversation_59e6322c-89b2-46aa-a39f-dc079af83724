const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { where } = require("sequelize");
const { reportClassification } = require("../config/reportConfig");
const logger = require("../config/logger");
const { buildAdvancedFilter } = require("./build-advanced-filter");
dayjs.extend(utc);
dayjs.extend(timezone);
const roleConfig = require("../config/roles");

// Helper: Format start and end dates
function getStartAndEndDates(payload, user) {
  const startDate = payload.startDate
    ? payload.startDate
    : dayjs.utc().format("YYYY-MM-DD 00:00:00");

  const endDate = payload.endDate
    ? payload.endDate
    : dayjs.utc().format("YYYY-MM-DD HH:mm:ss");

  return { startDate, endDate };
}

const getWhereClause = async ({ user, payload, reportReqData, options }) => {
  let limit = 10;
  let page = 1;
  if (options) {
    limit = options.limit || 10;
    page = options.page || 1;
  }

  const { startDate, endDate } = getStartAndEndDates(payload, user);

  payload.startDate = startDate;
  payload.endDate = endDate;
  // payload.page = page;
  // payload.limit = limit;

  let searchClause = "";
  let whereClause = "";

  if (payload.timezone) user.timezone = payload.timezone;

  whereClause = await buildAdvancedFilter(payload, user, reportReqData);

  // console.log("whereClause->", whereClause);

  if (reportReqData.responseFields) {
    let bindClause = await addCustomerBindClause(
      user,
      Object.keys(reportReqData.responseFields)
    );
    if (bindClause) {
      if (whereClause) {
        whereClause = whereClause + " AND " + bindClause;
      } else {
        whereClause = bindClause;
      }
    }
  }

  if (payload.search) {
    let fields = reportReqData.searchFields || [];

    for (let k = 0; k < fields.length; k++) {
      if (searchClause) {
        searchClause = `${searchClause} OR `;
      }
      searchClause = `${searchClause + fields[k]} ILIKE '%${payload.search}%' `;
    }

    if (searchClause) {
      if (whereClause) {
        whereClause = `(${whereClause}) AND (${searchClause})`;
      } else {
        whereClause = searchClause;
      }
    }
  }

  const viewBy = await determineViewBy(payload, reportReqData);

  return { whereClause, searchClause, viewBy };
};

// Helper: Determine viewBy value
async function determineViewBy(payload, reportReqData) {
  try {
    let viewBy = "day";
    const { startDate, endDate } = payload;

    // Check if payload has defaultViewBy
    if (payload?.defaultViewBy) {
      viewBy = payload.defaultViewBy;
      logger.info(`Using payload defaultViewBy: ${viewBy}`);
    }
    if (reportReqData?.defaultFilters?.useDefaultViewBy) {
      // Check if reportReqData has default viewBy
      viewBy = reportReqData.defaultFilters.viewBy;
    } else {
      // Check if we should use dynamic viewBy based on date range
      viewBy = (await getViewByValueforReports(startDate, endDate)) || viewBy;
      logger.info(`Calculated dynamic viewBy: ${viewBy}`);
    }

    // Check if calculated viewBy is in the allowed list otherwise set to defaultViewBy or 1st value in the allowed list
    for (const category of Object.keys(reportClassification)) {
      for (const report of reportClassification[category]) {
        if (report.name === payload.reportName) {
          logger.info(
            `Allowed viewBy for report ${payload.reportName}: ${report.viewBy}`
          );
          if (!report.viewBy.includes(viewBy)) {
            viewBy = payload?.defaultViewBy || report.viewBy[0];
            logger.info(`Adjusted viewBy to ${viewBy} based on report config`);
          }
          break; // early exit once found
        }
      }
    }

    logger.info(`Assigned viewBy: ${viewBy}`);
    return viewBy;
  } catch (error) {
    console.log("Error in determineViewBy:", error);
    logger.error("Error in determineViewBy:", error);

    // If something goes wrong, return a safe default
    const defaultViewBy = "day";
    logger.info(`Falling back to default viewBy: ${defaultViewBy}`);
    return defaultViewBy;
  }
}

const getViewByValueforReports = async (
  startDate,
  endDate,
  megaAPI = false
) => {
  const dateEnd = dayjs(endDate);
  let timediff = dateEnd.diff(startDate, "d");
  let viewBy = null;

  // if (timediff > 1) {
  //   viewBy = "day";
  //   if (megaAPI) {
  //     viewBy = "day";
  //   } else {
  //     if (timediff > 15) {
  //       viewBy = "week";
  //     }
  //     if (timediff >= 30) {
  //       viewBy = "month";
  //     }
  //   }
  // } else
  if (timediff === 1) {
    timediff = dateEnd.diff(startDate, "h");
    if (timediff <= 24) {
      viewBy = "hour";
    }
  }

  if (timediff < 1) {
    timediff = dateEnd.diff(startDate, "m");
    console.log("timediff in min ");
    if (timediff <= 60) {
      viewBy = "minute";
    } else {
      viewBy = "hour";
    }
  }
  console.log("viewBy in getViewByValue->", viewBy);
  return viewBy;
};

const getViewByValue = async (startDate, endDate, megaAPI = false) => {
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  const diffDays = end.diff(start, "day");
  const diffHours = end.diff(start, "hour");
  const diffMinutes = end.diff(start, "minute");

  console.log("startDate, endDate ->", startDate, endDate);
  console.log(
    "diffDays:",
    diffDays,
    "| diffHours:",
    diffHours,
    "| diffMinutes:",
    diffMinutes
  );

  if (diffDays >= 30) return "month";
  if (diffDays > 15) return "week";
  // if (diffDays > 1 || megaAPI) return "day";
  if (diffDays > 1) return "day";
  if (diffHours >= 1) return "hour";
  return "minute";
};

// const getViewByValue = async (startDate, endDate, megaAPI = false) => {

//   let dateEnd = dayjs(endDate);
//   let timediff = dateEnd.diff(startDate, 'd')

//   console.log("startDate, endDate->", startDate, endDate )
//   console.log("dateEnd->", dateEnd)
//   console.log("timediff->", timediff)

//     viewBy = "day";

//   if (timediff > 1) {
//     viewBy = 'day';
//     if (megaAPI) {
//       viewBy = 'day';
//     }
//     else {
//       if (timediff > 15) {
//         viewBy = 'week';
//       }
//       if (timediff >= 30) {
//         viewBy = 'month';
//       }
//     }
//   }
//   else if (timediff == 1) {
//     timediff = dateEnd.diff(startDate, 'h')
//     console.log("timediff for date diff less than one->", timediff)
//     if (timediff < 24) {
//       viewBy = 'hour';
//     }
//     else {
//       viewBy = 'day';
//     }
//   }
//   else {
//     timediff = dateEnd.diff(startDate, 'm')
//     console.log("timediff in min ",)
//     if (timediff <= 60) {
//       viewBy = 'minute';
//     }
//     else {
//       viewBy = 'hour'
//     }
//   }
//   return viewBy;
// };

const getCustomerSupplierFilter = async ({
  user,
  reportFields,
  megaAPI = false,
  dynamicAPI = false,
  filterFlag,
}) => {
  let whereClause = "";


  const userDetails = { ...user.dataValues }

  if (megaAPI) {
    // If user type is Both then assign type based on the filterFlag
    if (userDetails.type === roleConfig.userTypes.BOTH && filterFlag) {
      logger.info(`For userDetails Type Both we use Filter flag: ${filterFlag} `);

      if (filterFlag === roleConfig.userTypes.CUSTOMER) {
        userDetails.supplierList = [];
      }

      if (filterFlag === roleConfig.userTypes.SUPPLIER) {
        userDetails.customerList = [];
      }
    }

    let customerNames = userDetails.customerList.map((c) => `'${c.name}'`);
    let supplierNames = userDetails.supplierList.map((s) => `'${s.name}'`);

    let conditions = [];

    if (customerNames.length > 0) {
      conditions.push(`{customer_name} IN (${customerNames.join(", ")})`);
    }

    if (supplierNames.length > 0) {
      conditions.push(`{supplier} IN (${supplierNames.join(", ")})`);
    }

    whereClause = conditions.join(" AND ");
  } else {
    if (dynamicAPI) {
      for (let i = 0; i < userDetails.customerList.length; i++) {
        if (whereClause.length > 0) {
          whereClause = `${whereClause} or `;
        }
        whereClause = `${whereClause} customer_name ='${userDetails.customerList[i].name}'`;
      }
      for (let i = 0; i < userDetails.supplierList.length; i++) {
        if (whereClause.length > 0) {
          whereClause = `${whereClause} or `;
        }
        whereClause = `${whereClause} supplier='${userDetails.supplierList[i].name}'`;
      }
    } else {
      if (reportFields.includes("customer_name")) {
        for (let i = 0; i < userDetails.customerList.length; i++) {
          if (whereClause.length > 0) {
            whereClause = `${whereClause} or `;
          }
          whereClause = `${whereClause} customer_name ='${userDetails.customerList[i].name}'`;
        }
      }
      if (reportFields.includes("supplier")) {
        for (let i = 0; i < userDetails.supplierList.length; i++) {
          if (whereClause.length > 0) {
            whereClause = `${whereClause} or `;
          }
          whereClause = `${whereClause} supplier='${userDetails.supplierList[i].name}'`;
        }
      }
    }
  }
  if (whereClause.length > 0) return "(" + whereClause + ")";
  else return null;
};

const addCustomerBindClause = async (
  user,
  reportFields,
  megaAPI = false,
  dynamicAPI = false
) => {
  let whereClause = "";
  // if (megaAPI) {
  //   whereClause = `${whereClause} ({customer_bind} !='0' and {supplier_bind} != '-1')`;
  // } else {
  //   if (reportFields.includes("customer_bind")) {
  //     whereClause = `${whereClause} (customer_bind !='0')`;
  //   }
  //   if (reportFields.includes("supplier_bind")) {
  //     if (whereClause.length > 0) whereClause = `${whereClause} and `;
  //     whereClause = `${whereClause} (supplier_bind != '-1')`;
  //   }
  // }
  if (whereClause && whereClause.length > 0) return "(" + whereClause + ")";
  else return null;
};

const convertToClientTime = async (datetime, clientTimeZone) => {
  const formattedDate = dayjs(datetime) // Don't pass format string
    .tz(clientTimeZone)
    .format("DD-MMM-YYYY HH:mm:ss");

  return formattedDate;
};
const formatReportDate = async (datetime, type = "minute") => {
  let formattedDate;
  datetime = datetime.split("+")[0];

  if (type == "day" || type == "month")
    formattedDate = dayjs(datetime).format("DD-MMM-YYYY");
  else if (type == "hour")
    formattedDate = dayjs(datetime).format("DD-MMM-YYYY HH:00:00");
  else formattedDate = dayjs(datetime).format("DD-MMM-YYYY HH:mm:ss");
  return formattedDate;
};

const convertToUTC = async (datetime, clientTimeZone) => {
  console.log(clientTimeZone);
  let formattedDate = dayjs
    .tz(datetime, clientTimeZone)
    .utc()
    .format("YYYY-MM-DD HH:mm:ss");
  return formattedDate;
};

const addFormulas = (ws, headerRow, data) => {
  const numDataRows = data.length, // exclude header row
    startRow = 1,
    endRow = startRow + numDataRows - 1;

  headerRow.forEach((header, colIndex) => {
    const columnLetter = String.fromCharCode(65 + colIndex), // convert index to column letter (A, B, C, etc.)
      columnData = data.slice(1).map((row) => row[colIndex]), // extract column data excluding header
      isNumeric = columnData.every((value) => !isNaN(value));

    if (isNumeric) {
      // add SUM formula to a cell
      const sumCell = `${columnLetter}${endRow + 2}`;
      ws[sumCell] = {
        t: "n",
        f: `SUM(${columnLetter}${startRow}:${columnLetter}${endRow})`,
      };

      // add AVERAGE formula to a cell
      const averageCell = `${columnLetter}${endRow + 3}`;
      ws[averageCell] = {
        t: "n",
        f: `AVERAGE(${columnLetter}${startRow}:${columnLetter}${endRow})`,
      };

      // add labels for the formulas
      ws[`A${endRow + 2}`] = {
        t: "s",
        v: `${header} Sum`,
      };
      ws[`A${endRow + 3}`] = {
        t: "s",
        v: `${header} Average`,
      };
    }
  });
};

// const buildAdvancedFilter = async(payload = {}, user = {}, reportReqData) => {
//   const andGroups = [];

//   const { filters = {}, otherFilters = {} } = reportReqData || {};
//   const filterFields = Object.keys(filters);

//   const otherFilterFields = Object.keys(otherFilters);
//   const payloadFilters = payload.filters || {}

//   // Step 1: Clean payload and separate out otherFilterFields
//   const cleanedPayload = {};
//   const cleanedOtherPayload = {};

//   for (const [key, value] of Object.entries(payloadFilters)) {
//     if (
//       (Array.isArray(value) && value.length === 0) ||
//       value === null || value === undefined || value === ''
//     ) {
//       continue; // skip empty
//     }

//     if (otherFilterFields.includes(key)) {
//       cleanedOtherPayload[key] = value;
//     } else {
//       cleanedPayload[key] = value;
//     }
//   }

//   // Step 2: Handle filters from cleanedPayload
//   for (const [field, value] of Object.entries(cleanedPayload)) {
//     if (!filterFields.includes(field)) {
//       throw new Error(`Invalid field: ${field}`);
//     }

//     let queryName = field

//     // For custom reports, use the field name from filters
//     if(reportReqData.isCustomReport) {
//       queryName = reportReqData.filters[field] ? "'" + reportReqData.filters[field] + "'" : field;
//     }

//     let values = value;

//     if (typeof value === "string") {
//       const trimmed = value.trim();
//       if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
//         try {
//           values = JSON.parse(trimmed.replace(/'/g, '"'));
//         } catch {
//           values = trimmed
//             .slice(1, -1)
//             .split(",")
//             .map((val) => val.trim());
//         }
//       }
//     }

//     if (!Array.isArray(values)) values = [values];

//     const orGroup = values.map((val) => `${queryName}='${val}'`).join(" OR ");
//     andGroups.push(`(${orGroup})`);
//   }

//   console.log("cleanedOtherPayload->", cleanedOtherPayload)

//   // Step 3: Handle otherFilters separately
//   for (const [field, filterExpression] of Object.entries(cleanedOtherPayload)) {
//     let condition = otherFilters[field];

//     console.log("field->", field)

//     if (field === 'bilateral') {
//       const filter = {
//         search: 'bilateral_flag=1',
//       };

//       // const destinationDetails = await reportsService.getDestinationDetails(user, filter);
//       console.log("destinationDetails->", destinationDetails);

//       for (const field of reportReqData.otherFilters.bilateral) {
//         // Optionally process destinationDetails here...
//       }
//     }

//     if (field === "both" && otherFilters["only_roaming"] && otherFilters["only_direct"]) {
//       condition = `${otherFilters["only_roaming"]} OR ${otherFilters["only_direct"]}`;
//     }

//     andGroups.push(`(${condition})`);
//   }

//   // Step 4: Apply customer/supplier restriction if not super admin
//   if (!user.isSuperAdmin) {
//     if (filterFields.includes("customer_name") && Array.isArray(user.customerList) && user.customerList.length > 0) {
//       const customerGroup = user.customerList
//         .map((cust) => `customer_name='${cust.name}'`)
//         .join(" OR ");
//       andGroups.push(`(${customerGroup})`);
//     }

//     if (filterFields.includes("supplier") && Array.isArray(user.supplierList) && user.supplierList.length > 0) {
//       const supplierGroup = user.supplierList
//         .map((supp) => `supplier='${supp.name}'`)
//         .join(" OR ");
//       andGroups.push(`(${supplierGroup})`);
//     }
//   }

//   return andGroups.length > 0 ? andGroups.join(" AND ") : null;
// };

module.exports = {
  getWhereClause,
  determineViewBy,
  getViewByValue,
  getCustomerSupplierFilter,
  convertToClientTime,
  addFormulas,
  convertToUTC,
  formatReportDate,
  addCustomerBindClause,
  // buildAdvancedFilter
};
