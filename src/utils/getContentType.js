const path = require("path");

function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();

  if (filePath.toLowerCase().endsWith('.tar.gz')) {
    return 'application/gzip';
  }

  switch (ext) {
    case '.csv':
      return 'text/csv';
    case '.zip':
      return 'application/zip';
    case '.gz':
      return 'application/gzip';
    case '.json':
      return 'application/json';
    case '.pdf':
      return 'application/pdf';
    case '.txt':
      return 'text/plain';
    case '.xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case '.xls':
      return 'application/vnd.ms-excel';
    default:
      return 'application/octet-stream'; // fallback for unknown types
  }
}

module.exports = getContentType;
