const ldap = require('ldap-authentication');
const { ldap_config } = require('../config/config');
/*
const ldap_auth = async (username, password) => {

    let user_dc = ldap_config.user_dc.split(',');
    let dc_string = '';
    for (let i = 0; i < user_dc.length; i++) {
        dc_string += ', dc = ' + user_dc[i];
    }

    let options = {
        ldapOpts: {
            url: ldap_config.host,
            // tlsOptions: { rejectUnauthorized: false }
        },
        userDn: 'cn=' + username + ',ou=' + ldap_config.user_group + dc_string,
        userPassword: password
    }
    console.log("options is ",options)
    try {
        let user = await authenticate(options)
        return true;
    }
    catch (err) {
       if(err.code =="ECONNREFUSED")
            {
                return -61
            }
        else  {  
            console.log("auth error");
        return 0;
        }
    }

}
*/

const ldap_auth = async (username, password) => {
    try {
        let user_dc = ldap_config.user_dc.split(',');
        let dc_string = '';
        for (let i = 0; i < user_dc.length; i++) {
            dc_string += ' dc = ' + user_dc[i] +',';
        }
        dc_string = dc_string.substring(0, dc_string.length - 1)

        let user_orgunit = ldap_config.user_group.split(',');
        let ou_string = '';
        for (let i = 0; i < user_orgunit.length; i++) {
            ou_string += ' ou = ' + user_orgunit[i] +',';
        }
        ou_string = ou_string.substring(0, ou_string.length - 1)
        username=  username.replace(/@airtel\.com$/i, ''); 
        const adminOptions = {
            ldapOpts: {
                url: ldap_config.host, // Your LDAP server URL
            },
            adminDn: `cn=${ldap_config.admin_user},${ou_string}, ${dc_string}`, // Admin Distinguished Name
            adminPassword: ldap_config.admin_password, // Admin password
            userSearchBase:   dc_string, // Base DN to search for users
            usernameAttribute: ldap_config.usernameAttribute, // Attribute used to identify the username
            username: username, // Username to search
            userPassword: password,
        };  
        // console.log("admin opts ",adminOptions)
        // Verify if the user exists (admin bind and search)
       
        try {
            let authuser = await ldap.authenticate(adminOptions)
            return true;
        }
        catch (err) {
            if (err.code == "ECONNREFUSED") {
                return -61
            }
            else {
                console.log("auth error",err);
                return 0;
            }
        }
    }
    catch (error) {
        console.error('Error during authentication:', error.message);
        return 0
    }

}

/*
async function ldap_auth(username, password) {
    const client = ldap.createClient({
        url: ldap_config.host
    });

    let user_dc = ldap_config.user_dc.split(',');
    let dc_string = '';
    for (let i = 0; i < user_dc.length; i++) {
        dc_string += ', dc = ' + user_dc[i];
    }



    return new Promise((resolve, reject) => {
        // Step 1: Admin bind
        admin_dn = "cn=" + ldap_config.admin_user + dc_string
        console.log("admin dn is ", admin_dn, ldap_config.admin_password)
        client.bind(admin_dn, ldap_config.admin_password, (err) => {
            if (err) {
                console.error('Admin bind failed:', err.message);
                client.unbind();
                return -61
            }

            console.log('Admin bind successful');

            // Step 2: Search for the user's DN
            const searchOptions = {
                scope: 'sub',
                filter: `(uid=${username})` // Change the attribute if needed, e.g., `sAMAccountName`
            };

            client.search(dc_string, searchOptions, (searchErr, res) => {
                if (searchErr) {
                    console.error('Search error:', searchErr.message);
                    client.unbind();
                    return 0;
                }

                let userDn = null;
                res.on('searchEntry', (entry) => {
                    userDn = entry.object.dn; // Capture the user's DN
                });

                res.on('error', (resErr) => {
                    console.error('Search response error:', resErr.message);
                    client.unbind();
                    return 0;
                });

                res.on('end', () => {
                    if (!userDn) {
                        console.error('User DN not found');
                        client.unbind();
                        return 0;
                    }

                    console.log('User DN:', userDn);

                    // Step 3: Attempt to bind with user's credentials
                    client.bind(userDn, password, (userErr) => {
                        if (userErr) {
                            console.error('User bind failed:', userErr.message);
                            client.unbind();
                            return 0;
                        }

                        console.log('User authenticated successfully');
                        client.unbind();
                        return true;
                    });
                });
            });
        });
    });
}
*/

module.exports = ldap_auth;