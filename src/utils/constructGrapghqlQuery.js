const config = require("../config/config");
const logger = require("../config/logger");
const {
  reportClassification,
  restapiUri,
  maxPageLimit,
  columnMapping,
} = require("../config/reportConfig");
const { getReportValues } = require("./helper");

const getStaticRepQuery = ({
  payload,
  whereClause,
  viewBy,
  user,
  reportReqData,
}) => {
  const { startDate, endDate, page, limit, selectedColumns, reportName, selectAll = false } =
    payload;
  let pagemetaData =
    "{ \
    current_page, \
    last_page, \
    page_size, \
    total_items \
  }";

  let tempQuery;
  const fieldsToUse = selectedColumns
    ? getReportValues(reportReqData, selectedColumns)
    : Object.keys(reportReqData.responseFields || {});

  // Validate that we have fields to query, if not, provide default fields
  const validFields =
    fieldsToUse && fieldsToUse.length > 0 ? fieldsToUse : ["timestamp"];

  const validFieldsString = validFields.toString();

  let orderBy = [config.GRAPH_CONFIG.BAR.name, config.GRAPH_CONFIG.PIE.name].includes(payload.graphFilters?.visualizationType) ? validFieldsString : "timestamp";

  logger.info(`Fields to use in GraphQL query: ${validFieldsString}`);

  // If whereClause is not empty, add it to the query
  // Otherwise, use the default query without whereClause
  if (whereClause?.length > 0) {
    tempQuery = `${reportReqData.name
      }(viewBy :${viewBy},pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", selectAll: ${selectAll}, endTime : "${endDate}",timeZone :"${user.timezone
      }"  where: " ${whereClause}", orderBy: "${orderBy}"){page_meta${pagemetaData}, items{${validFieldsString}}}`;
  } else {
    tempQuery = `${reportReqData.name
      }(viewBy :${viewBy},pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", selectAll: ${selectAll}, endTime : "${endDate}", timeZone :"${user.timezone
      }", orderBy: "${orderBy}"){page_meta${pagemetaData}, items{${validFieldsString}}}`;
  }

  logger.info(`Generated GraphQL query: ${tempQuery}`);
  return tempQuery;
};

const getCdrQuery = ({ filterClause, payload, rawCdr, user, page, limit }) => {
  let pagemetaData =
    "{ \
    current_page, \
    last_page, \
    page_size, \
    total_items \
  }";

  let tempQuery;
  const cdrType = payload.cdrType || "single";
  const { startDate, endDate } = payload;

  let responseFields = rawCdr.responseFields;

  // For Multi liner need to only multiliner fields
  if (payload.withinSingle && !payload.download && !payload.save) {
    responseFields = rawCdr.cdrTypeMultiResponseFields;
  }

  // If filterClause is not empty, add it to the query
  // Otherwise, use the default query without filterClause
  if (filterClause.length > 0) {
    tempQuery = `${rawCdr.name
      }(pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}", cdrType: "${cdrType}", timeZone:"${user.timezone
      }",  where: "${filterClause}"){page_meta${pagemetaData} items{${responseFields.toString()}}}`;
  } else {
    tempQuery = `${rawCdr.name
      }(pageNumber: ${page}, pageSize : ${limit}, startTime :"${startDate}", endTime : "${endDate}" cdrType: "${cdrType}", timeZone:"${user.timezone
      }"){page_meta${pagemetaData} items{${responseFields.toString()}}}`;
  }

  logger.info(`CDR query build successfully`);

  return tempQuery;
};

const gerCdrDownloadQuery = ({
  filterClause,
  payload,
  rawCdr,
  user,
  startDate,
  endDate,
}) => {
  let responseFields = rawCdr.responseFields;

  if (
    payload.cdrType === "multiple" &&
    payload.filters.event_id &&
    !payload.download &&
    !payload.sendEmail
  ) {
    responseFields = rawCdr.cdrTypeMultiResponseFields;
  }

  const tempQuery = {
    method: "post",
    url: `${restapiUri}/api/v1/fetch_raw_cdr`,
    data: {
      start_time: startDate.trim(),
      end_time: endDate.trim(),
      fields: responseFields,
      time_zone: user.timezone,
      cdr_type: payload.cdrType || "single",
      page_number: payload.page,
      page_size: payload.limit,
    },
  };

  logger.info(`CDR download query build successfully`);

  return tempQuery;
};

// const getAggregationQuery = ({
//   payload,
//   whereClause,
//   viewBy,
//   user,
//   reportReqData,
// }) => {
//   const reportName = reportReqData.aggregationFieldMapping.name;
//   logger.info(" from aggregation query build", reportName);
//   // Format the query for aggregation report

//   const aggregateQuery = {
//     reportName: reportName,
//     startDate: payload.startDate,
//     endDate: payload.endDate,
//     timezone: user.timezone,
//     viewBy,
//     whereCondition: whereClause || "",
//     fieldName: reportReqData.aggregationFieldMapping.fieldName
//       ? Object.keys(reportReqData.aggregationFieldMapping.fieldName)
//       : [],
//     derivedFieldName: reportReqData.aggregationFieldMapping.derivedFieldName
//       ? Object.keys(reportReqData.aggregationFieldMapping.derivedFieldName)
//       : [],
//   };

//   if (payload?.selectedColumns) {
//     aggregateQuery.items = getReportValues(reportReqData, payload.selectedColumns);
//   }

//   if (payload.negativeReport) {
//     aggregateQuery.negativeReport = true;
//   }

//   logger.info(`Aggregation query build successfully`);

//   return aggregateQuery;
// };


const getAggregationQuery = ({
  payload,
  whereClause,
  viewBy,
  user,
  reportReqData,
}) => {
  const mapping = reportReqData.aggregationFieldMapping;

  let fieldName = [];
  let derivedFieldName = [];

  if (payload?.selectedColumns) {
    // Filter fieldName keys based on selectedColumns values
    fieldName = Object.entries(mapping.fieldName || {})
      .filter(([_, display]) => payload.selectedColumns.includes(display))
      .map(([key]) => key);

    // Filter derivedFieldName keys based on selectedColumns values
    derivedFieldName = Object.entries(mapping.derivedFieldName || {})
      .filter(([_, display]) => payload.selectedColumns.includes(display))
      .map(([key]) => key);
  } else {
    // fallback to all fields
    fieldName = Object.keys(mapping.fieldName || {});
    derivedFieldName = Object.keys(mapping.derivedFieldName || {});
  }

  if (fieldName.length === 0 && derivedFieldName.length === 0) {
    throw new Error("At least one field must be selected for aggregation");
  }

  const aggregateQuery = {
    reportName: mapping.name,
    startDate: payload.startDate,
    endDate: payload.endDate,
    timezone: user.timezone,
    viewBy,
    whereCondition: whereClause || "",
    fieldName,
    derivedFieldName,
  };


  if (payload?.selectedColumns) {
    aggregateQuery.items = getReportValues(reportReqData, payload.selectedColumns);
  }

  if (payload.negativeReport) {
    aggregateQuery.negativeReport = true;
  }

  logger.info(`Aggregation query build successfully`);

  return aggregateQuery;
};

const getCustomRepQuery = async ({
  payload,
  otherFilterQuery,
  viewBy,
  user,
  reportReqData,
}) => {
  let resolvedReportName;
  let roleSearchFilter;

  // Find the Report name based on the timezone
  // If the timezone is not found in the mapping, use the default report name
  if (
    reportReqData.timeZoneMapping &&
    reportReqData.timeZoneMapping[payload.timezone]
  ) {
    resolvedReportName = reportReqData.timeZoneMapping[payload.timezone];
  } else {
    resolvedReportName = reportReqData.name;
  }

  // If user is not super admin, we need to filter the report based on the customer and supplier list
  if (!user.isSuperAdmin) {
    if (!user.allCustomerList || !user.allSupplierList) {
      const customerList =
        user.customerList?.map((customer) => customer.name) || [];
      const supplierList =
        user.supplierList?.map((supplier) => supplier.name) || [];

      if (reportReqData.defaultFilters?.roleSearch) {
        const { roleSearch } = reportReqData.defaultFilters;
        if (roleSearch === "supplier_bind") {
          const { reportsService } = require("../services");

          const supplierList = await reportsService.getSupplierList(user, false);
          const supplierBindList = supplierList.map(
            (supplier) => supplier.supplier_bind
          );
          console.log("supplierBindList-->", supplierBindList);
          roleSearchFilter = supplierBindList;
        } else {
          roleSearchFilter =
            roleSearch === "customer"
              ? customerList
              : roleSearch === "supplier"
                ? supplierList
                : null;
        }
      }
    }
  }

  // Formulate the query based on the report classification
  const query = {
    reportName: resolvedReportName,
    timezone: user.timezone,
    search: payload.search || "",
    page: payload.page || 1,
    limit: payload.limit || 10,
    download: Number(payload.download)
      ? "1"
      : Number(payload.sendEmail)
        ? "1"
        : "0",
    // type: "",
    roleSearch: roleSearchFilter || [],
    columnFilter: payload.columnFilter || [],
    filterSearch: payload.filters || {},
    // otherFilters: otherFilterQuery || "",
  };

  logger.info(`Custom report query build successfully`);

  if (!reportReqData.noViewBy) {
    query.viewBy = viewBy;
  }

  if (!reportReqData.noStartEndDate) {
    query.startDate = payload.startDate;
    query.endDate = payload.endDate;
  }

  if (payload?.selectedColumns) {
    query.columnFilter = payload.selectedColumns
  }

  console.log("query-->", query)

  return query;
};

const getWeeklyMonthlyRepQuery = ({
  payload,
  whereClause,
  viewBy,
  user,
  reportReqData,
}) => {
  // Format the query for weekly/monthly reports
  console.log("selected columns in weekly/monthly report query:", payload.selectedColumns);
  console.log("reportReqData->", reportReqData);
  const query = {
    reportName: reportReqData.weeklyMonthlyReportName,
    startDate: payload.startDate,
    endDate: payload.endDate,
    viewBy: viewBy,
    timezone: user.timezone,
    whereCondition: whereClause,
    fieldName: payload.selectedColumns
      ? getReportValues(
        reportReqData,
        payload.selectedColumns
      )
      : Object.keys(reportReqData.responseFields),
    page: payload.page || 1,
    limit: payload.limit || 10,
    download: String(payload.download),
  };

  logger.info(`Weakly monthly query build successfully`);

  return query;
};

const getChartApiQuery = ({ payload, whereClause, fields, viewBy, user }) => {
  let { startDate, endDate, page, limit } = payload || {};
  const pagemetaData =
    "{ \
            current_page, \
            last_page, \
            page_size, \
            total_items \
        }";
  let tempQuery;
  if (
    payload.dataColumns["X-Axis"] &&
    payload.dataColumns["X-Axis"].length > 0
  ) {
    if (whereClause.length > 0) {
      tempQuery = `${"getDashboardChartData" + "(viewBy :"
        }${viewBy},pageNumber: ${page}, pageSize : ${limit}  orderBy : "${columnMapping[payload.dataColumns["X-Axis"][0]]
        }" , where: " ${whereClause}",startTime : "${startDate}", endTime : "${endDate}",timeZone:"${user.timezone
        }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
    } else {
      tempQuery = `${"getDashboardChartData" + "(viewBy :"
        }${viewBy},pageNumber: ${page}, pageSize : ${limit}  orderBy : "${columnMapping[payload.dataColumns["X-Axis"][0]]
        }" ,startTime : "${startDate}" ,endTime : "${endDate}", timeZone:"${user.timezone
        }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
    }
  } else if (whereClause.length > 0) {
    tempQuery = `${"getDashboardChartData" + "(viewBy :"
      }${viewBy},pageNumber: ${page} ,pageSize : ${limit} , where: " ${whereClause}",startTime : "${startDate}" ,endTime : "${endDate}", timeZone:"${user.timezone
      }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
  } else {
    tempQuery = `${"getDashboardChartData" + "(viewBy :"
      }${viewBy},pageNumber: ${page} ,pageSize : ${limit} , startTime : "${startDate}" ,endTime : "${endDate}" , timeZone:"${user.timezone
      }" ){page_meta${pagemetaData} items{${fields.toString()}}}`;
  }

  return tempQuery;
};

const getMegaAPIQuery = ({ payload, whereClause, fields, viewBy, user }) => {
  let {
    startDate,
    endDate,
    page,
    limit,
    graph = false,
    negativeReport = false,
  } = payload || {};
  const pagemetaData =
    "{ \
            current_page, \
            last_page, \
            page_size, \
            total_items \
        }";
  let tempQuery;
  if (
    payload.dataColumns["X-Axis"] &&
    payload.dataColumns["X-Axis"].length > 0
  ) {
    if (whereClause.length > 0) {
      tempQuery = `${"getSmsCdr" + "(viewBy :"
        }${viewBy}, graph : ${graph} ,negativeReport : ${negativeReport} ,orderBy : "${columnMapping[payload.dataColumns["X-Axis"][0]]
        }" ,pageNumber: ${page} ,pageSize : ${limit} , where: "${whereClause}",startTime : "${startDate}" ,endTime : "${endDate}", timeZone:"${user.timezone
        }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
    } else {
      tempQuery = `${"getSmsCdr" + "(viewBy :"
        }${viewBy}, graph : ${graph} ,negativeReport : ${negativeReport} ,orderBy : "${columnMapping[payload.dataColumns["X-Axis"][0]]
        }" ,pageNumber: ${page} ,pageSize : ${limit} ,startTime : "${startDate}" ,endTime : "${endDate}", timeZone:"${user.timezone
        }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
    }
  } else if (whereClause.length > 0) {
    tempQuery = `${"getSmsCdr" + "(viewBy :"
      }${viewBy},graph : ${graph} ,negativeReport : ${negativeReport} ,pageNumber: ${page} ,pageSize : ${limit} ,timeZone:"${user.timezone
      }" ,  where: " ${whereClause}",startTime : "${startDate}" ,endTime : "${endDate}"){page_meta${pagemetaData} items{${fields.toString()}}}`;
  } else {
    tempQuery = `${"getSmsCdr" + "(viewBy :"
      }${viewBy},graph : ${graph} ,negativeReport : ${negativeReport} ,pageNumber: ${page} ,pageSize : ${limit} , startTime : "${startDate}" ,endTime : "${endDate}" , timeZone:"${user.timezone
      }"){page_meta${pagemetaData} items{${fields.toString()}}}`;
  }

  return tempQuery;
};

// const getDynamicRepDownloadQuery = ({
//   filterClause,
//   payload,
//   rawCdr,
//   user,
//   startDate,
//   endDate,
// }) => {

//   // let responseFields = rawCdr.responseFields

//   // if (payload.cdrType === "multiple" && payload.filters.event_id && !payload.download && !payload.sendEmail) {
//   //   responseFields = rawCdr.cdrTypeMultiResponseFields
//   // }

//   const tempQuery = {
//     method: "post",
//     url: `${restapiUri}/api/v1/download_sms_cdr`,
//     // data: {
//     //   start_time: startDate.trim(),
//     //   end_time: endDate.trim(),
//     //   fields: responseFields,
//     //   time_zone: user.timezone,
//     //   cdr_type: payload.cdrType || 'single'
//     // },
//     data: {
//     "start_time": "2025-06-05 00:00:00",
//     "end_time": "2025-06-05 23:59:59",
//     "order_by": "",
//     "fields": [
//         "timestamp","a_number","b_number","customer_bind","customer_interface_type","customer_name","traffic_type_customer","dest_imsi","destination_mcc_final","destination_mnc_final","destination_country_name","destination_operator_name","destination_operator_code","orig_imsi","source_mcc","source_mnc","source_country_name","source_mcc_mnc","source_operator_name","supplier_bind","supplier","traffic_type_supplier","traffic_type","destination_country_code","content_category","src_prime","customer_interconnect","supplier_interconnect","visiting_operator","visiting_operator_id","visited_opr_country","unit_new_rate_round","source_ip","destination_ip","source_operator_code","final_delivery_unit_cost_round","error_code","source_mnp_supplier","destination_mnp_supplier","lcr_name","spec_lcr","customer_system_id","supplier_system_id","oracle_account_no","src_nop_id","category","carrier","customer_kam","src_routing_id","src_hub","source_country_code","dest_nop_id","percentage_routing_final","dest_hub","cdr_type_description","cdr_type_supplier","dest_prime","vertical","customer_billing_logic","supplier_billing_logic","total_submissions","final_average_margin_euro","supplier_billing_count_final","supplier_interface_type","status","destination_msg"
//     ],
//     "time_zone": "Asia/Kolkata",
//     "where": "(src_prime ='Airtel UK') or (customer_bind ='Nexmo A2P_CS') or (customer_name ='NEXMO INC') or (dest_prime ='Airtel') or (customer_interconnect ='Airtel') or (customer_billing_logic ='S') or (spec_lcr ='No') or (supplier_interface_type ='SMPP') or (status ='MessageAccepted') or (traffic_type_customer ='A2P') or (dest_imsi ILIKE '%9%') or (destination_msg ILIKE '%AT%')",
//     "view_by": "day"
// }
//   };

//   logger.info(`CDR download query build successfully`);

//   return tempQuery;

// }

module.exports = {
  getStaticRepQuery,
  getCdrQuery,
  gerCdrDownloadQuery,
  getAggregationQuery,
  getCustomRepQuery,
  getWeeklyMonthlyRepQuery,
  getChartApiQuery,
  getMegaAPIQuery,
  // getDynamicRepDownloadQuery
};
