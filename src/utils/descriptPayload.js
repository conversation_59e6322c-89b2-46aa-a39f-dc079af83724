const fs = require('fs');
const crypto = require('crypto');
const path = require('path');
const config = require('../config/config');
// const globalConfig = require('../config/globalConfig');
const ENCRIPTED_FIELDS = [
  'password',
];

// Load private key once
const privateKey = fs.readFileSync(config.PRIVATE_KEY_PATH, 'utf8');

function decryptPayloadMiddleware(req, res, next) {
  for (const key in req.body) {
    if (ENCRIPTED_FIELDS.includes(key)) {
      console.log('Encrypted field found:', key);
      try {
        const encryptedBuffer = Buffer.from(req.body[key], 'base64'); // ✅ use dynamic key

        const decrypted = crypto.privateDecrypt(
          {
            key: privateKey,
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            oaepHash: 'sha256',
          },
          encryptedBuffer
        );

        req.body[key] = decrypted.toString('utf8');
        console.log(`${key} decrypted successfully`);
      } catch (err) {
        console.error(`Decryption failed for ${key}:`, err.message);
        return res.status(400).json({ error: `Invalid encrypted field: ${key}` });
      }
    }
  }

  next(); // Proceed to actual route
}

module.exports = decryptPayloadMiddleware;
