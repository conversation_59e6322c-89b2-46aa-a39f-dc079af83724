const dayjs = require("dayjs");
const { convertToUTC } = require("./misc");
const { reportsMapping } = require("../config/reportConfig");

exports.singleToDoubleQuotes = (value) => {
  let tempValue = value;
  tempValue
    .split("")
    .filter((i) => i === `"`)
    .forEach(() => {
      tempValue = tempValue.replace('"', `'`);
    });
  return tempValue.replace("[", "(").replace("]", ")");
};

exports.decodeHtmlEntities = (str) => {
  return str.replace(/&lt;/g, "<").replace(/&gt;/g, ">");
};

/** sort order array creation function */
exports.getSortOrderList = (sortBy, orderBy, defaultSort, sqlQuerySort) => {
  const attributesIndex = {};

  if (sqlQuerySort) {
    const { attributes = [], replaceFields = [] } = sqlQuerySort || {};

    if (replaceFields.length > 0) {
      attributes.forEach((ele) => {
        if (typeof ele === "object") {
          const [sQ, aliasName] = ele;
          if (replaceFields.includes(aliasName))
            attributesIndex[aliasName] = sQ;
        }
      });
    }
  }

  const sorting = [];
  if (sortBy && orderBy) {
    const sortByList = sortBy.split(",");
    const orderByList = orderBy.split(",");

    for (let i = 0; i < sortByList.length; i += 1) {
      const sortList = [];
      sortList.push(attributesIndex[sortByList[i]] || sortByList[i]);
      sortList.push(orderByList[i]);
      sorting.push(sortList);
    }
  } else if (defaultSort && defaultSort.length > 0) {
    return defaultSort;
  }
  return sorting;
};

exports.usePageLimit = (req) => {
  let limit = 10;
  let page = 1;
  if (req.query.limit) limit = Number(req.query.limit);
  if (req.query.page) page = Number(req.query.page);
  const offset = (page - 1) * limit;

  return { page, limit, offset };
};
exports.getOrDefaultDateRange = async (req) => {
  const { startDate, endDate } = req.query;
  const userTimezone = req.user.timezone;

  let _startDate = dayjs().tz(userTimezone).format("YYYY-MM-DD 00:00:00");
  let _endDate = dayjs().tz(userTimezone).format("YYYY-MM-DD HH:mm:ss");
  if (startDate && endDate) {
    // Convert input dates to ISO format to match database timestamp format
    // Database stores: "2025-07-24T19:07:00" 
    // Frontend sends: "2025-07-24 00:00:00"
    _startDate = dayjs(startDate).format("YYYY-MM-DDTHH:mm:ss");
    _endDate = dayjs(endDate).format("YYYY-MM-DDTHH:mm:ss");
  } else {
    // Convert default dates to ISO format
    _startDate = dayjs().tz(userTimezone).startOf('day').format("YYYY-MM-DDTHH:mm:ss");
    _endDate = dayjs().tz(userTimezone).format("YYYY-MM-DDTHH:mm:ss");
  }

  return { _startDate, _endDate };
};

exports.getReportValues = (reportReqData, items = []) => {
  const result = [];

  if (items?.length > 0 && reportReqData) {
    items.forEach((item) => {
      const key = Object.entries(reportReqData.responseFields)
        .find(([, value]) => value === item)?.[0];
      if (key) result.push(key);
    });
  }

  // if (!result.includes('timestamp')) result.unshift('timestamp');

  console.log("result", result, reportReqData.name, items);
  return result;
};


exports.separateTableAndDerivedFields = (reportConfigData) => {
  const tableFields = Object.values(reportConfigData.responseFields || {}) || [];
  const derivedFields = Object.values(reportConfigData.aggregationFieldMapping.fieldName || {}) || [];

  const filteredTableFields = tableFields.filter(
    (field) => !derivedFields.includes(field)
  );

  return {
    tableFields: filteredTableFields,
    derivedFields,
  };
};
