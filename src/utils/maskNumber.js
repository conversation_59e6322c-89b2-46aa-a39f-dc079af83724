const config = require('../config/config');

const isNumeric = (value) => {
    // Check if the value contains only digits (and possibly decimal points, minus signs)
    const str = String(value).trim();
    // Return true only if the string contains only digits, decimal points, and optional minus sign
    return /^-?\d*\.?\d+$/.test(str) && str !== '';
};


const applyNumberMasking = (number, options = {}) => {
  // If number is null, undefined or empty, return as is
  if (number === null || number === undefined) return number;
  if (typeof number !== 'string' && typeof number !== 'number') return number;
  
  // Convert to string in case a number is passed
  const numStr = String(number).trim();

  
  // Extract only digits from the number
  // const digits = numStr//.replace(/\D/g, '');
  // console.log("digits->", digits)

  // If no digits, return original string
  if (numStr.length === 0) return numStr;

  // Check if the value is purely numeric - if not, return as is without masking
  if (!isNumeric(numStr)) {
    return numStr;
  }
  
  // Get configuration with possible overrides
  const maskChar = options.character || config.maskingOptions.character;
  const position = options.position || config.maskingOptions.position;
  const minPercentage = options.minPercentage || config.maskingOptions.minPercentage;
  
  // Calculate number of digits to mask (at least minPercentage%)
  const maskCount = Math.ceil(numStr.length * (minPercentage / 100));

  // Create masked version based on position
  let maskedDigits;
  switch (position.toLowerCase()) {
    case 'first':
      maskedDigits = maskChar.repeat(maskCount) + numStr.substring(maskCount);
      break;
      
    case 'last':
      maskedDigits = numStr.substring(0, numStr.length - maskCount) + maskChar.repeat(maskCount);
      // console.log("maskedDigits->", maskedDigits);
      break;
      
    case 'middle':
    default:
      // For middle masking, we keep equal parts visible at beginning and end if possible
      const visibleCount = numStr.length - maskCount;
      const firstVisible = Math.floor(visibleCount / 2);
      const lastVisible = visibleCount - firstVisible;
      
      maskedDigits = 
        numStr.substring(0, firstVisible) + 
        maskChar.repeat(maskCount) + 
        numStr.substring(numStr.length - lastVisible);
      break;
  }
  return maskedDigits;
};


const maskNumber = (data) => {
  const chunkSize = config.maskingOptions.chunkSize
  const maskingField = config.maskingOptions.maskingField;

  if (!Array.isArray(data)) {
    // Return primitive or non-array data unchanged
    return data;
  }

  const result = [];

  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize);

    const maskedChunk = chunk.map(item => {
      const maskedItem = { ...item };
      maskingField.forEach(field => {
        if (maskedItem[field]) {
          maskedItem[field] = applyNumberMasking(maskedItem[field]);
        }
      });
      return maskedItem;
    });

    result.push(...maskedChunk);
  }

  return result;
};


const maskDownloadedRows = (headers, rows) => {
  const chunkSize = config.maskingOptions.chunkSize
  const maskingFields = config.maskingOptions.maskingField;

  // Map column names to indices
  const fieldIndexMap = {};
  headers.forEach((header, index) => {
    if (maskingFields.includes(header)) {
      fieldIndexMap[header] = index;
    }
  });

  const maskedRows = [];

  for (let i = 0; i < rows.length; i += chunkSize) {
    const chunk = rows.slice(i, i + chunkSize);

    const maskedChunk = chunk.map(row => {
      const newRow = [...row]; // shallow copy
      for (const field of Object.keys(fieldIndexMap)) {
        const idx = fieldIndexMap[field];
        if (newRow[idx]) {
          newRow[idx] = applyNumberMasking(newRow[idx]);
        }
      }
      return newRow;
    });

    maskedRows.push(...maskedChunk);
  }

  return maskedRows;
};





module.exports = {
 maskNumber,
 maskDownloadedRows
};
