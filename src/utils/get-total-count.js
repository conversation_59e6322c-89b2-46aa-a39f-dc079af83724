const isWeekMonthApi = require("./isWeekMonthAPi");
const constructGrapghqlQuery = require("../utils/constructGrapghqlQuery.js");
const graphqlService = require("../services/graphql.service.js");
const config = require("../config/config.js");



const getTotalCount = async (payload, whereClause, viewBy, user, reportReqData, dataFromMegaAPI = false, fields) => {

  if (reportReqData.name === 'dynamicReport') {
    const queryBuilder = dataFromMegaAPI
      ? constructGrapghqlQuery.getMegaAPIQuery
      : constructGrapghqlQuery.getChartApiQuery;

    const name = dataFromMegaAPI ? 'getSmsCdr' : 'getDashboardChartData';

    let query = queryBuilder({
      payload: { ...payload, page: 1, limit: 1}, whereClause, fields, viewBy, user
    });

    let response = await graphqlService.getGraphqlData({ name, payload: query });

    return response.page_meta.total_items;

  }


  const apiUrl = `${config.dataServiceUrl}${reportReqData?.apiEndpoint}`;

  const updatedPayload = { ...payload, page: 1, limit: 1, download: 0 };

  // Check if the report is a billing report with viewBy as week/month
  if (isWeekMonthApi(viewBy, reportReqData, payload)) {
    // Build weekly and monthly report query for finding total count
    const query = constructGrapghqlQuery.getWeeklyMonthlyRepQuery({
      payload: updatedPayload,
      whereClause,
      viewBy,
      user,
      reportReqData,
    });

    // Get response for weekly and monthly report from FastAPI
    const response = await graphqlService.getWeekMonthyReport(query, user, isDownload = false);

    return response.page_meta.total_items;
  } else if (reportReqData.isCustomReport) {
    const customRepQuery = await constructGrapghqlQuery.getCustomRepQuery({
      payload: updatedPayload,
      otherFilterQuery: whereClause,
      viewBy,
      user,
      reportReqData,
    });

    console.log("customRepQuery->", customRepQuery)


    // To find total count of the Report
    const response = await graphqlService.getGraphqlCustomData({
      apiUrl,
      payload: customRepQuery,
    });

    return response.data.page_meta.total_items || 0;
  } else {
    // Build static report query for finding total count
    const query = constructGrapghqlQuery.getStaticRepQuery({
      payload: updatedPayload,
      whereClause,
      viewBy,
      user,
      reportReqData,
    });

    // Get response for static report from GraphQL
    const response = await graphqlService.getGraphqlData({
      name: reportReqData.name,
      payload: query,
    });

    return response.page_meta.total_items;
  }
};

module.exports = {
  getTotalCount,
};