const logger = require("../config/logger");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");

dayjs.extend(utc);
dayjs.extend(timezone);

const { offline_downloads } = require("../config/config");
const offlineDownloadService = require("../services/offlineDownload.service");
const path = require("path");
const fs = require("fs");
const zlib = require("zlib");
const tarStream = require("tar-stream");
const config = require("../config/config");
const { PassThrough, Readable } = require('stream');
const ApiError = require("./ApiError");
const archiver = require("archiver");
const filePartsCountMap = new Map();
const csvRowCountMap = new Map();
const bufferFileMap = new Map();

  
const deleteTempDir = (dirPath) => {
  if (fs.existsSync(dirPath)) {
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`Cleaned up temp directory: ${dirPath}`);
    } catch (cleanupErr) {
      logger.error("Error cleaning up temp directory in catch:", cleanupErr);
    }
  }
}

/**
 * saveCsvOffline(csv, offlineDataRes, isFinal)
 *
 * This function handles paginated CSV data and writes it to disk in chunks.
 * - Buffers incoming CSV rows in a temporary disk file until reaching a threshold (NO_OF_ROWS_PER_FILE).
 * - Once threshold is met, it writes a part file with a consistent header and resets the buffer.
 * - On final call (isFinal = true), any remaining rows in the buffer are flushed as the last part file.
 * - All part files are archived into a ZIP and saved to the final path.
 * - Cleans up temporary files and updates DB with archive location.
 *
 * Designed for large datasets (millions of rows) to minimize memory usage.
 */

const saveCsvOffline = async (csv, offlineDataRes, isFinal = false) => {
  const tempDir = path.join(offline_downloads.download_directory, `${offlineDataRes.id}_tmp`);
  const finalZipPath = path.join(offline_downloads.download_directory, `${csv.filename}.zip`);

  try {
    if (!fs.existsSync(offline_downloads.download_directory)) {
      fs.mkdirSync(offline_downloads.download_directory, { recursive: true });
    }

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const csvData = csv.buf.toString();
    const rows = csvData.split("\n");

    const header = rows.slice(0, 5).join("\n");
    let dataRows = rows.slice(5).filter(row => row.trim() !== "" && row !== "-");

    const rowsPerFile = config.NO_OF_ROWS_PER_FILE;
    const bufferFilePath = path.join(tempDir, `${csv.filename}_buffer.csv`);

    let currentRowCount = csvRowCountMap.get(offlineDataRes.id) || 0;
    let currentPart = filePartsCountMap.get(offlineDataRes.id) || 1;

    // Write to buffer
    fs.appendFileSync(bufferFilePath, dataRows.join("\n") + "\n", "utf8");
    currentRowCount += dataRows.length;

    while (currentRowCount >= rowsPerFile) {
      const partFilePath = path.join(tempDir, `${csv.filename}_part${currentPart}.csv`);
      const partStream = fs.createWriteStream(partFilePath);

      partStream.write(header + "\n");

      const bufferContent = fs.readFileSync(bufferFilePath, "utf8").split("\n").filter(r => r);
      const rowsToWrite = bufferContent.slice(0, rowsPerFile);
      const remainingRows = bufferContent.slice(rowsPerFile);

      partStream.write(rowsToWrite.join("\n") + "\n");
      partStream.close();

      // Overwrite buffer with remaining rows
      fs.writeFileSync(bufferFilePath, remainingRows.join("\n") + "\n", "utf8");

      currentRowCount = remainingRows.length;
      currentPart++;
    }

    csvRowCountMap.set(offlineDataRes.id, currentRowCount);
    filePartsCountMap.set(offlineDataRes.id, currentPart);
    bufferFileMap.set(offlineDataRes.id, bufferFilePath);

    if (isFinal) {
      // Flush remaining rows
      if (currentRowCount > 0) {
        const finalPartPath = path.join(tempDir, `${csv.filename}_part${currentPart}.csv`);
        const bufferContent = fs.readFileSync(bufferFilePath, "utf8").split("\n").filter(r => r);
        fs.writeFileSync(finalPartPath, header + "\n" + bufferContent.join("\n") + "\n", "utf8");
      }

      // Clean buffer
      csvRowCountMap.delete(offlineDataRes.id);
      filePartsCountMap.delete(offlineDataRes.id);
      bufferFileMap.delete(offlineDataRes.id);
      if (fs.existsSync(bufferFilePath)) fs.unlinkSync(bufferFilePath);

      // Archive all part files
      const output = fs.createWriteStream(finalZipPath);
      const archive = archiver("zip", { zlib: { level: 9 } });

      const zipPromise = new Promise((resolve, reject) => {
        output.on("close", resolve);
        output.on("error", reject);
        archive.on("error", reject);
      });

      archive.pipe(output);

      const finalFiles = fs.readdirSync(tempDir).filter(f => f.includes("_part"));
      for (const file of finalFiles) {
        const filePath = path.join(tempDir, file);
        archive.file(filePath, { name: file });
      }

      archive.finalize();
      await zipPromise;

      await offlineDownloadService.updateOfflineDownload(offlineDataRes.id, {
        status: offline_downloads.status.COMPLETED,
        filePath: finalZipPath,
      });

      logger.info(`All raw CDRs saved as ZIP: ${finalZipPath}`);

      deleteTempDir(tempDir);
    }
  } catch (error) {
    logger.error("Error saving raw CDRs as ZIP:", error);
    if (fs.existsSync(tempDir)) {
      deleteTempDir(tempDir);
    }
    throw error;
  }
};



async function sendTarGzFromBuffer(buffer, fileName, res) {
  console.log("fileName->", fileName);
  console.log("PDF buffer size:", buffer.length);
  const pack = tarStream.pack(); // tar stream
  const gzip = zlib.createGzip(); // gzip stream

  // Pipe tar stream into gzip and then into response
  res.setHeader("Content-Type", "application/gzip");
  res.setHeader("Content-Disposition", `attachment; filename=${fileName}.tar.gz`);
  pack.pipe(gzip).pipe(res);

  // Add file buffer to the tar archive
  pack.entry({ name: fileName }, buffer, (err) => {
    if (err) {
      console.error("Error writing entry to tar:", err);
      res.status(500).send("Failed to create archive.");
      return;
    }
    pack.finalize(); // signal that we're done
  });
}

async function createTarGzBuffer(buffer, fileName) {
  try {
    return new Promise((resolve, reject) => {
      const pack = tarStream.pack();
      const gzip = zlib.createGzip();
  
      const passThrough = new PassThrough();
      const chunks = [];
  
      passThrough.on("data", (chunk) => chunks.push(chunk));
      passThrough.on("end", () => resolve(Buffer.concat(chunks)));
      passThrough.on("error", reject);
  
      // Pipe tar stream into gzip, then into passThrough to capture output
      pack.pipe(gzip).pipe(passThrough);
  
      // Add file entry to tar
      pack.entry({ name: fileName }, buffer, (err) => {
        if (err) return reject(err);
        pack.finalize(); // Finish writing tar
      });
    });
  } catch (error) {
    console.error("error in createTarGzBuffer->", error)
    throw error
  }

}


async function createGzipBuffer(buffer, filename) {

  return new Promise((resolve, reject) => {
    const gzip = zlib.createGzip();
    const passThrough = new PassThrough();
    const chunks = [];

    passThrough.on("data", chunk => chunks.push(chunk));
    passThrough.on("end", () => resolve(Buffer.concat(chunks)));
    passThrough.on("error", reject);

    gzip.pipe(passThrough);
    gzip.end(buffer);
  });
}


async function createZipBuffer(files) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', {forceZip64: true});
    const chunks = [];
    const passThrough = new PassThrough();

    passThrough.on('data', chunk => chunks.push(chunk));
    passThrough.on('end', () => resolve(Buffer.concat(chunks)));
    passThrough.on('error', reject);

    archive.on('error', reject);
    archive.pipe(passThrough);

    // // Add each file buffer to zip
    // for (const { buf, filename } of files) {
    //   archive.append(buf, { name: filename });
    // }

    for (const file of files) {
      if (file.buf) {
        archive.append(file.buf, { name: file.filename });
      } else if (file.stream) {
        archive.append(file.stream, { name: file.filename });
      }
    }

    archive.finalize();
  });
}


/**
 * Compress second-level folders or files inside `parentDir`.
 * 
 * @param {string} parentDir - Root directory (e.g., ./billing_reports or ./fy_reports)
 * @param {1 | 2} mode - Compress second-level folders or files
 */
const zipSubContents = async (parentDir, depth = 1) => {
  console.log(`Zipping contents at depth ${depth} in: ${parentDir}`);

  const zipFile = async (filePath, nameOverride = null) => {
    const fileName = path.basename(filePath);
    const zipName = (nameOverride || fileName.replace(/\.[^/.]+$/, "")) + ".zip";
    const zipPath = path.join(path.dirname(filePath), zipName);

    if (filePath === zipPath) return;
    if (fs.existsSync(zipPath)) fs.unlinkSync(zipPath);

    await new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver("zip", { zlib: { level: 9 } });

      output.on("close", () => {
        console.log(`✔ Zipped file: ${filePath} → ${zipPath} (${archive.pointer()} bytes)`);
        fs.unlinkSync(filePath);
        resolve();
      });

      archive.on("error", reject);
      archive.pipe(output);
      archive.file(filePath, { name: fileName });
      archive.finalize();
    });
  };

  const zipFolderFiles = async (folderPath) => {
    const folderName = path.basename(folderPath);
    const zipPath = path.join(path.dirname(folderPath), folderName + ".zip");

    const files = fs.readdirSync(folderPath)
      .map(f => path.join(folderPath, f))
      .filter(f => fs.statSync(f).isFile() && !f.endsWith(".zip"));

    if (files.length === 0) return;

    if (fs.existsSync(zipPath)) fs.unlinkSync(zipPath);

    await new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver("zip", { zlib: { level: 9 } });

      output.on("close", () => {
        console.log(`✔ Zipped folder files: ${folderPath} → ${zipPath} (${archive.pointer()} bytes)`);
        files.forEach(file => fs.unlinkSync(file));
        resolve();
      });

      archive.on("error", reject);
      archive.pipe(output);

      for (const filePath of files) {
        archive.file(filePath, { name: path.basename(filePath) });
      }

      archive.finalize();
    });
  };

  const entries = [];

  const collectAtDepth = (dir, currentDepth = 1) => {
    const dirents = fs.readdirSync(dir, { withFileTypes: true });
    for (const dirent of dirents) {
      const entryPath = path.join(dir, dirent.name);
      if (entryPath.endsWith(".zip")) continue; // Skip zipped entries

      if (currentDepth === depth) {
        entries.push({ path: entryPath, isDir: dirent.isDirectory() });
      } else if (dirent.isDirectory()) {
        collectAtDepth(entryPath, currentDepth + 1);
      }
    }
  };

  const removeEmptyDirs = (dir) => {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);
    if (files.length === 0) {
      fs.rmdirSync(dir);
      // console.log(`🗑 Removed empty folder: ${dir}`);
      const parent = path.dirname(dir);
      removeEmptyDirs(parent); // Recursively clean up upwards
    }
  };

  collectAtDepth(parentDir);

  for (const entry of entries) {
    if (!fs.existsSync(entry.path)) continue;

    if (entry.isDir) {
      const subItems = fs.readdirSync(entry.path, { withFileTypes: true });
      const onlyFiles = subItems.filter(i => i.isFile());
      const onlyDirs = subItems.filter(i => i.isDirectory());

      const hasNonZipFiles = onlyFiles.some(f => !f.name.endsWith(".zip"));

      if (hasNonZipFiles) {
        await zipFolderFiles(entry.path);
        removeEmptyDirs(entry.path);
      } else if (onlyDirs.length > 0) {
        for (const sub of onlyDirs) {
          const subPath = path.join(entry.path, sub.name);
          await zipFolderFiles(subPath);
          removeEmptyDirs(subPath);
        }
        removeEmptyDirs(entry.path);
      } else {
        removeEmptyDirs(entry.path);
      }
    } else if (!entry.path.endsWith(".zip")) {
      await zipFile(entry.path);
      removeEmptyDirs(path.dirname(entry.path));
    }
  }
};



module.exports = {
  saveCsvOffline,
  sendTarGzFromBuffer,
  createTarGzBuffer,
  createGzipBuffer,
  createZipBuffer,
  zipSubContents
};