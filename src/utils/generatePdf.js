const PDFDocument = require("pdfkit-table");
const { MAX_ROWS_FOR_PDF } = require("../config/globalConfig");
const dayjs = require('dayjs');

const constructPDF = async (headers, rows, payload, aggregateResponse) => {
  console.log("Prepare PDF file");

  try {
    if (rows.length > MAX_ROWS_FOR_PDF) {
      throw new Error("Data size is too large; cannot construct PDF file");
    }

    const pdfBuffer = await new Promise((resolve, reject) => {
      const doc = new PDFDocument({ margin: 30, size: "A4" });
      const buffers = [];

      doc.on("data", (chunk) => buffers.push(chunk));
      doc.on("end", () => resolve(Buffer.concat(buffers)));
      doc.on("error", reject);

      // Title & Date Range
      doc.fontSize(14).text(payload.reportName || "Report", { align: "center" }).moveDown();
      doc
        .fontSize(12)
        .text(`Reports for ${payload.startDate} to ${payload.endDate}`, { align: "center" })
        .moveDown();

      // Aggregate Section
      if (aggregateResponse && Object.keys(aggregateResponse).length > 0) {
        const aggHeader = Object.keys(aggregateResponse).join("  |  ");
        const aggValues = Object.values(aggregateResponse).join("  |  ");

        doc.fontSize(10).text(aggHeader, { align: "left" }).moveDown(0.3);
        doc.fontSize(10).text(aggValues, { align: "left" }).moveDown(1);
      }

      // Table Rows in chunks
      const chunkSize = 5000;
      for (let i = 0; i < rows.length; i += chunkSize) {
        const chunk = rows.slice(i, i + chunkSize);

        const table = {
          headers,
          rows: chunk,
        };

        const startTime = Date.now();
        doc.table(table);
        console.log(
          `Processed rows: ${Math.min(i + chunkSize, rows.length)}/${rows.length} | Time: ${
            (Date.now() - startTime) / 1000
          }s`
        );
      }

      doc.end();
    });

    const startDate = dayjs(payload.startDate).format('YYYY-MM-DD');
    const endDate = dayjs(payload.endDate).format('YYYY-MM-DD');

    return {
      buf: pdfBuffer,
      startDate: payload.startDate,
      endDate: payload.endDate,
      extension: ".pdf",
      filename: payload.fileName ? `${payload.fileName}.pdf` : `${payload.reportName}_${startDate}_${endDate}.pdf`,
    };
  } catch (error) {
    throw error;
  }
};


module.exports = constructPDF;
