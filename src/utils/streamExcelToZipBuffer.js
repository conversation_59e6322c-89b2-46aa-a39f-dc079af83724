const ExcelJS = require('exceljs');
const archiver = require('archiver');
const { PassThrough } = require('stream');
const dayjs = require('dayjs');
const { MAX_ROWS_FOR_EXCEL } = require('../config/globalConfig');

async function streamExcelToZipBuffer(headers, rows, payload, aggregateResponse = {}) {
  if (rows.length > MAX_ROWS_FOR_EXCEL) {
    throw new Error("Data size is too large to stream Excel file");
  }

  const startDate = dayjs(payload.startDate).format('YYYY-MM-DD');
  const endDate = dayjs(payload.endDate).format('YYYY-MM-DD');
  const currentTime = dayjs().format('HH_mm_ss');

  const fileName = payload.fileName
    ? `${payload.fileName}.xlsx`
    : `${payload.reportName}_${startDate}_${endDate}_${currentTime}.xlsx`;

  return new Promise((resolve, reject) => {
    try {
      const zipStream = new PassThrough();
      const archive = archiver('zip', { zlib: { level: 9 }, forceZip64: true });

      const chunks = [];
      zipStream.on('data', chunk => chunks.push(chunk));
      zipStream.on('end', () => {
        const buffer = Buffer.concat(chunks);
        console.log(`ZIP buffer created, size: ${buffer.length} bytes`);
        
        if (buffer.length === 0) {
          reject(new Error('Generated ZIP buffer is empty'));
          return;
        }
        
        resolve({
          buf: buffer,
          filename: fileName.replace('.xlsx', '.zip'),
          extension: '.zip',
          startDate,
          endDate,
        });
      });
      
      zipStream.on('error', (err) => {
        console.error('ZIP stream error:', err);
        reject(err);
      });
      
      archive.on('error', (err) => {
        console.error('Archive error:', err);
        reject(err);
      });
      // Pipe archive to zip stream
      archive.pipe(zipStream);
      // Create Excel stream
      const excelStream = new PassThrough();
      archive.append(excelStream, { name: fileName });
      const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({ stream: excelStream });
      const worksheet = workbook.addWorksheet('Report');
      // Add title row
      worksheet.addRow([`Reports for ${payload.startDate} to ${payload.endDate}`]).commit();
      // Add aggregate data if present
      if (aggregateResponse && Object.keys(aggregateResponse).length > 0) {
        worksheet.addRow(Object.keys(aggregateResponse)).commit();
        worksheet.addRow(Object.values(aggregateResponse)).commit();
        worksheet.addRow([]).commit(); // Empty row
      }
      // Add headers
      worksheet.addRow(headers).commit();
      // Add data rows in chunks
      const chunkSize = 10000; // Reduced chunk size for better memory management
      let processedRows = 0;
      const processNextChunk = async () => {
        try {
          if (processedRows >= rows.length) {
            // All rows processed, finalize workbook
            console.log(`Finalizing workbook with ${processedRows} rows`);
            await workbook.commit();
            console.log('Workbook committed, finalizing archive');
            archive.finalize();
            return;
          }
          const chunk = rows.slice(processedRows, processedRows + chunkSize);
          
          for (const row of chunk) {
            // Validate row data
            if (!Array.isArray(row)) {
              console.warn(`Invalid row at index ${processedRows}:`, row);
              continue;
            }
            worksheet.addRow(row).commit();
          }
          
          processedRows += chunk.length;
          console.log(`Processed ${processedRows}/${rows.length} rows`);
          
          // Process next chunk on next tick to prevent blocking
          setImmediate(processNextChunk);
        } catch (error) {
          console.error('Error processing chunk:', error);
          reject(error);
        }
      };
      // Start processing
      processNextChunk();
    } catch (err) {
      console.error('Setup error:', err);
      reject(err);
    }
  });
}
module.exports = streamExcelToZipBuffer;
