const PDFDocument = require("pdfkit-table");
const archiver = require("archiver");
const { PassThrough } = require("stream");
const dayjs = require("dayjs");
const { MAX_ROWS_FOR_PDF } = require("../config/globalConfig");

async function streamPdfToZipBuffer(headers, rows, payload, aggregateResponse={}) {
  if (rows.length > MAX_ROWS_FOR_PDF) {
    throw new Error("Data size is too large to stream PDF file");
  }

  const startDate = dayjs(payload.startDate).format('YYYY-MM-DD');
  const endDate = dayjs(payload.endDate).format('YYYY-MM-DD');
  const currentTime = dayjs().format('HH_mm_ss');
  const fileName = payload.fileName
    ? `${payload.fileName}.pdf`
    : `${payload.reportName}_${startDate}_${endDate}_${currentTime}.pdf`;

  return new Promise((resolve, reject) => {
    try {
      const zipStream = new PassThrough();
      const archive = archiver("zip", { zlib: { level: 9 }, forceZip64: true });

      const chunks = [];
      zipStream.on("data", chunk => chunks.push(chunk));
      zipStream.on("end", () => {
        resolve({
          buf: Buffer.concat(chunks),
          startDate: payload.startDate,
          endDate: payload.endDate,
          extension: ".zip",
          filename: fileName.replace(".pdf", ".zip"),
        });
      });
      zipStream.on("error", reject);
      archive.on("error", reject);

      archive.pipe(zipStream);

      // PDF stream
      const pdfStream = new PassThrough();
      archive.append(pdfStream, { name: fileName });

      const doc = new PDFDocument({ margin: 30, size: "A4" });
      doc.pipe(pdfStream);

      doc.fontSize(14).text(payload.reportName || "Report", { align: "center" }).moveDown();
      doc
        .fontSize(12)
        .text(`Reports for ${payload.startDate} to ${payload.endDate}`, { align: "center" })
        .moveDown();

      if (aggregateResponse && Object.keys(aggregateResponse).length > 0) {
        const aggHeader = Object.keys(aggregateResponse).join("  |  ");
        const aggValues = Object.values(aggregateResponse).join("  |  ");
        doc.fontSize(10).text(aggHeader, { align: "left" }).moveDown(0.3);
        doc.fontSize(10).text(aggValues, { align: "left" }).moveDown(1);
      }

      const chunkSize = 5000;
      for (let i = 0; i < rows.length; i += chunkSize) {
        const chunk = rows.slice(i, i + chunkSize);
        const table = {
          headers,
          rows: chunk,
        };
        doc.table(table);
        console.log(`Processed rows: ${Math.min(i + chunkSize, rows.length)}/${rows.length}`);
      }

      doc.end();
      archive.finalize(); // finalize after PDF ends
    } catch (err) {
      reject(err);
    }
  });
}

module.exports = streamPdfToZipBuffer;
