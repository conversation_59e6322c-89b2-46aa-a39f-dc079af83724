const e = require("express");
const { reportClassification } = require("../config/reportConfig");
const {columnMapping} = require("../config/reportConfig");

function parseFilterValues(value) {
  if (typeof value === "string") {
    const trimmed = value.trim();
    if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
      try {
        return JSON.parse(trimmed.replace(/'/g, '"'));
      } catch {
        return trimmed.slice(1, -1).split(",").map(val => val.trim());
      }
    }
  }
  return Array.isArray(value) ? value : [value];
}

function handleRegularFilters(cleanedPayload ={}, reportReqData = null) {
  const integerFields = ["destination_mcc_final", "destination_mnc_final"];
  const andGroups = [];
  const { filters = {} } = reportReqData || {};
  const filterFields = Object.keys(filters);

  for (const [field, value] of Object.entries(cleanedPayload)) {
    if (!filterFields.includes(field)) {
      throw new Error(`Invalid field: ${field}`);
    }

    let queryName = field;
    // For custom reports, use the field name from reportReqData.filters with single quotes
    if(reportReqData?.isCustomReport) {
      queryName = reportReqData.filters[field] ? "'" + reportReqData.filters[field] + "'" : field;
    }
        
    const values = parseFilterValues(value);
    let orGroup;

    // For CDR report we need to parse where clause key in curly bracks ({});
    if(['fetchRawCdr', 'dynamicReport'].includes(reportReqData.name)) {
      orGroup = values
      .map((val) => {
        // If value is the integer, do not wrap it in quotes
        const formattedVal = integerFields.includes(queryName) ? val : `'${val}'`;
        return `{${queryName}}=${formattedVal}`;
      })
      .join(" or ");
    } else {
      orGroup = values
        .map((val) => {
          // If value is the integer, do not wrap it in quotes
          const formattedVal = integerFields.includes(queryName) ? val : `'${val}'`;
          return `${queryName}=${formattedVal}`;
        })
        .join(" or ");
    }

    andGroups.push(`((${orGroup}))`);
  }

  return andGroups;
}

async function handleOtherFilters(cleanedOtherPayload, reportReqData, payload, user) {
  const { reportsService } = require("../services");

  const andGroups = [];
  const otherFilters = reportReqData.otherFilters || {};
  let isTierReport = false;

  // Check if the report is a Tier Report
  for (const category in reportClassification) {
    if (category === 'Tier / Slab based billing' && reportClassification[category].some(item => item.name == payload.reportName)) {
      isTierReport = true;
      break;
    }
  }

  for (const [field, filterExpression] of Object.entries(cleanedOtherPayload)) {
    // If Bilateral is true, we need to add the destination details to the filter
    if (field === 'bilateral') {
      const filter = { search: 'bilateral_flag=1' };
      // Fetch destination details based on the filter
      const destinationDetails = await reportsService.getDestinationDetails(user, filter);

      // console.log("destinationDetails->", destinationDetails)

      const bilateralFields = reportReqData.otherFilters.bilateral || [];
      const fieldValueMap = {};

      // Add the destination details to the filter
      for (const item of destinationDetails) {
        for (const bfOriginal of bilateralFields) {
          let bf = bfOriginal;
          let value = null;

          if ([
            'destination_operator_name', 'destination_operator', 'destination', 'Destination Operator'
          ].includes(bfOriginal)) {
            value = item.operator_name;
          } else if ([
            'destination_country_name', 'Destination Country Name', 'Destination Country'
          ].includes(bfOriginal)) {
            value = item.country_name;
          } else if([
            'destination_country_code'
          ].includes(bfOriginal)) {
            value = item.country_code
          } else if ([
            'destination_operator_code'
          ].includes(bfOriginal)) {
            value = item.id;
          }

          // If report is a Tier Report, we need to add single quotes around the field name
          if (isTierReport) {
            bf = `'${bf}'`;
          }

          if (!fieldValueMap[bf]) {
            fieldValueMap[bf] = new Set();
          }

          if (value !== null && value !== undefined) {
            fieldValueMap[bf].add(value);
          }
        }
      }

      // Construct the IN conditions for Bilateral
      const inConditions = Object.entries(fieldValueMap).map(([field, values]) => {
        const valueList = Array.from(values).map(val => `'${String(val).replace(/'/g, "''")}'`).join(", ");
        if(['dynamicReport'].includes(reportReqData.name)) {
          return `{${field}} in (${valueList})`;
        } else {
          return `${field} in (${valueList})`;
        }
      });

      if (inConditions.length > 0) {
        andGroups.push(`(${inConditions.join(" and ")})`);
      }
    } else {
      let condition = otherFilters[field];

      // For other filters, we need to handle the case where the value is an array
      if (field === "both" && otherFilters["only_roaming"] && otherFilters["only_direct"]) {
        condition = `${otherFilters["only_roaming"]} or ${otherFilters["only_direct"]}`;
      }

      andGroups.push(`(${condition})`);
    }
  }
  return andGroups;
}

const buildAdvancedFilter = async(payload = {}, user = {}, reportReqData) => {
  const andGroups = [];

  const { filters = {}, otherFilters = {} } = reportReqData || {};
  const filterFields = Object.keys(filters);
  const otherFilterFields = Object.keys(otherFilters);
  const payloadFilters = payload.filters || {};

  const cleanedPayload = {};
  const cleanedOtherPayload = {};

  for (const [key, value] of Object.entries(payloadFilters)) {
    if (
      (Array.isArray(value) && value.length === 0) ||
      value === null || value === undefined || value === ''
    ) {
      continue;
    }

    if (otherFilterFields.includes(key)) {
      cleanedOtherPayload[key] = value;
    } else {
      cleanedPayload[key] = value;
    }
  }

  // Construct Raw query for Filters
  andGroups.push(...handleRegularFilters(cleanedPayload, reportReqData));
  // Construct Raw query for Other Filters
  andGroups.push(...await handleOtherFilters(cleanedOtherPayload, reportReqData, payload, user));

  if (!user.isSuperAdmin) {
    if (filterFields.includes("customer_name") && Array.isArray(user.customerList) && user.customerList.length > 0) {
      if(reportReqData.isCustomReport) {
              const customerGroup = user.customerList
        .map((cust) => `'Customer Name'='${cust.name}'`)
        .join(" or ");
      andGroups.push(`(${customerGroup})`);
      } else {
              const customerGroup = user.customerList
        .map((cust) => `customer_name='${cust.name}'`)
        .join(" or ");
      andGroups.push(`(${customerGroup})`);
      }

    }

    if (filterFields.includes("supplier") && Array.isArray(user.supplierList) && user.supplierList.length > 0) {
      if(reportReqData.isCustomReport) {
              const supplierGroup = user.supplierList
        .map((supp) => `'Supplier'='${supp.name}'`)
        .join(" or ");
      andGroups.push(`(${supplierGroup})`);
      } else {
              const supplierGroup = user.supplierList
        .map((supp) => `supplier='${supp.name}'`)
        .join(" or ");
      andGroups.push(`(${supplierGroup})`);
      }
    }
  }

  return andGroups.length > 0 ? andGroups.join(" and ") : null;
};

const buildWhereClauseFromFilters = async ({ filters }) => {
  console.log("filters->", filters);
  let whereClause = "";

  let filterClause = "";
  let nextOperator = "or";

  for (let j = 0; j < filters.length; j++) {
    // if (columnMapping[filters[j].field] == "customer_name" || columnMapping[filters[j].field] == "supplier")
    if (!columnMapping.hasOwnProperty(filters[j].field)) {
      console.warn(`Warning: field '${filters[j].field}' not found in columnMapping, skipping filter.`);
      continue;
    }
    if (filters[j].condition == "Equal") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} ='${filters[j].value}') `;
      }
    }

    if (filters[j].condition == "Not Equal") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} !='${filters[j].value}') `;
      }
    }
    if (filters[j].condition == "Like") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} ILIKE '%${filters[j].value}%') `;
      }
    }
    if (filters[j].condition == "Not Like") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ({${columnMapping[filters[j].field]
          }} NOT LIKE '%${filters[j].value}%') `;
      }
    }
    if (filters[j].condition == "Less than equal to") {
      console.log("columnMapping[filters[j].field] ->", columnMapping[filters[j].field]);
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ${columnMapping[filters[j].field]
          } <= ${filters[j].value}`;
      }
    }
    if (filters[j].condition == "Greater than equal to") {
      if (columnMapping[filters[j].field]) {
        if (filterClause.length > 0) {
          filterClause += " " + nextOperator + " ";
        }
        filterClause = `${filterClause} ${columnMapping[filters[j].field]
          } >= ${filters[j].value}`;
      }
    }
    nextOperator = filters[j].operator || "and";
  }

  if (filterClause.length > 0) {
    if (whereClause.length > 0) {
      whereClause = `${whereClause} and (${filterClause})`;
    } else {
      whereClause = `${filterClause}`;
    }
  }
  return whereClause;
};

module.exports = {buildAdvancedFilter, handleOtherFilters, handleRegularFilters, buildWhereClauseFromFilters} ;
