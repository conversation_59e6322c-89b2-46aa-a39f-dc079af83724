const logger = require("../config/logger");
const { sequelize } = require("../models");

exports.useTablesRollback =
  (fn) =>
  async (req = {}, rem) => {
    let transaction;

    try {
      transaction = await sequelize.transaction();
      req._rollbackTxn = transaction;
      const res = await Promise.resolve(fn(req, rem));
      await transaction.commit();
      return res;
    } catch (err) {
      // Rollback the transaction in case of an error
      if (transaction) {
        await transaction.rollback();
      }
      throw err;
    }
  };
