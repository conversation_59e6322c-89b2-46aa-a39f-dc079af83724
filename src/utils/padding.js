const { paddingConfig } = require("../config/config");

function padding(data) {
  if (!Array.isArray(data)) {
    console.error("Error: Input data must be an array.");
    return;
  }

  data.forEach(item => {
    if (typeof item === 'object' && item !== null) {
      paddingConfig.fields.forEach(fieldName => {
        if (item.hasOwnProperty(fieldName)) {
          const value = item[fieldName];

          if (fieldName.endsWith('MCCMNC')) {
            // Special case: MCC-MNC format (e.g., "123-2")
            if (typeof value === 'string' && value.includes('-')) {
              const [mcc, mnc] = value.split('-');
              const paddedMnc = String(mnc).padStart(paddingConfig.paddingSize, '0');
              item[fieldName] = `${mcc}-${paddedMnc}`;
            }
          } else {
            // Normal numeric or numeric-string field
            if (typeof value === 'number' || (typeof value === 'string' && /^[0-9]+$/.test(value))) {
              item[fieldName] = String(value).padStart(paddingConfig.paddingSize, '0');
            }
          }
        }
      });
    }
  });

  return data;
}



const PaddingDownloadedRows = (headers, rows) => {
  const chunkSize = paddingConfig.chunkSize;
  const maskingFields = paddingConfig.fields;

  const fieldIndexMap = {};
  headers.forEach((header, index) => {
    if (maskingFields.includes(header)) {
      fieldIndexMap[header] = index;
    }
  });

  const paddingRows = [];

  for (let i = 0; i < rows.length; i += chunkSize) {
    const chunk = rows.slice(i, i + chunkSize);

    const paddingChunk = chunk.map(row => {
      const newRow = [...row]; // shallow copy

      for (const field of Object.keys(fieldIndexMap)) {
        const idx = fieldIndexMap[field];
        const cellValue = newRow[idx];

        if (cellValue != null) {
          if (field.endsWith('MCCMNC') && typeof cellValue === 'string' && cellValue.includes('-')) {
            const [mcc, mnc] = cellValue.split('-');
            const paddedMnc = String(mnc).padStart(paddingConfig.paddingSize, '0');
            newRow[idx] = `${mcc}-${paddedMnc}`;
          } else if (/^[0-9]+$/.test(cellValue)) {
            newRow[idx] = String(cellValue).padStart(paddingConfig.paddingSize, '0');
          }
        }
      }

      return newRow;
    });

    paddingRows.push(...paddingChunk);
  }

  return paddingRows;
};




module.exports = {
  padding,
  PaddingDownloadedRows
};
