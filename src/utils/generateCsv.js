const { MAX_ROWS_FOR_CSV } = require("../config/globalConfig");
const dayjs = require('dayjs');



const constructCSV = async (
  headers,
  rows,
  payload,
  aggregateResponse = {},
  options = { includeHeaders: true }
) => {
  try {
    console.log("rows.length->", rows.length, MAX_ROWS_FOR_CSV);
    if (rows.length > MAX_ROWS_FOR_CSV) {
      throw new Error(
        `Data size is too large; Allowed number of rows is ${MAX_ROWS_FOR_CSV}, cannot construct CSV file`
      );
    }

    const { includeHeaders = true } = options;

    console.log("Preparing CSV document");

    let csvParts = [];

    if (includeHeaders) {
      // Add title line
      csvParts.push(`Reports for ${payload.startDate} to ${payload.endDate}`);

      // Add aggregate response
      const aggHeader = Object.keys(aggregateResponse).join(",");
      const aggValues = Object.values(aggregateResponse).join(",");
      if (aggHeader && aggValues) {
        csvParts.push(aggHeader);
        csvParts.push(aggValues);
      }

      // Add a blank line and headers
      csvParts.push("");
      csvParts.push(headers.join(","));
    }

    // Convert rows to CSV
    const rowCSV = rows.map((row) => row.join(",")).join("\n");
    csvParts.push(rowCSV);

    // Final CSV string
    const csv = csvParts.join("\n");

    const startDate = dayjs(payload.startDate.trim()).format("YYYY-MM-DD");
    const endDate = dayjs(payload.endDate.trim()).format("YYYY-MM-DD");

    const buf = Buffer.from(csv, "utf-8");

    return {
      buf,
      startDate: payload.startDate,
      endDate: payload.endDate,
      extension: ".csv",
      filename: payload.fileName
        ? `${payload.fileName}.csv`
        : `${payload.reportName}_${startDate}_${endDate}.csv`,
    };
  } catch (error) {
    throw error;
  }
};



module.exports = constructCSV