const logger = require("../config/logger");
const reportConfig = require("../config/reportConfig");
const roleConfig = require("../config/roles");


const findFilterFlag = (payload, user, card = false) => {
    let filterFlag = roleConfig.userTypes.BOTH;
    let fields = [];
    const { dataColumns } = payload || {};

    if (card) {
        console.log("payload.reportField->", payload.reportField)
        fields = [payload.reportField]
    } else {
        switch (payload.visualizationType) {
            case reportConfig.panelVisualizationTypes.tableReport:
                fields = [...dataColumns.tableFields];
                break;
            case reportConfig.panelVisualizationTypes.barGraph:
                fields = [...dataColumns["X-Axis"]];
                break;

            case reportConfig.panelVisualizationTypes.lineGraph:
            case reportConfig.panelVisualizationTypes.pieChart:
            case reportConfig.panelVisualizationTypes.multiAxisGaph:
                fields = [...dataColumns.derivedFields];
                break;
            default:
                return filterFlag;
        }

    }

    const hasCustomer = fields.some((field) =>
        Object.values(reportConfig.customerFields).includes(field)
    );
    const hasSupplier = fields.some((field) =>
        Object.values(reportConfig.supplierFields).includes(field)
    );
    const hasBoth = fields.some((field) =>
        Object.values(reportConfig.bothFields).includes(field)
    );

    logger.info(`field in filterFlag is ${fields} hasCustomer ${hasCustomer}`)
    logger.info(`field in filterFlag is ${fields} hasSupplier ${hasSupplier}`)

    if (hasBoth) {
        filterFlag = roleConfig.userTypes.BOTH;
    } else if (hasCustomer && hasSupplier) {
        filterFlag = roleConfig.userTypes.BOTH;
    } else if (hasCustomer) {
        filterFlag = roleConfig.userTypes.CUSTOMER;
    } else if (hasSupplier) {
        filterFlag = roleConfig.userTypes.SUPPLIER;
    }

    logger.info(`filter flag for ${payload.name} is: ${filterFlag}`);

    return filterFlag;
};


module.exports = findFilterFlag