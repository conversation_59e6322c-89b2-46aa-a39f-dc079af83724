


function isWeekMonthApi(viewBy, reportReqData, payload) {
  const billingCategoryCheck =
    reportReqData?.category === "billing" &&
    ["week", "month"].includes(viewBy);

  const billingReportNameCheck =
    ["Billing", "Billing Multiple"].includes(payload?.reportName) &&
    ["day", "week", "month"].includes(viewBy);

  return billingCategoryCheck || billingReportNameCheck;
}





module.exports = isWeekMonthApi;