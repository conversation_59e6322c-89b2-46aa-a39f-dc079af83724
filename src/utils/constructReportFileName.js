const dayjs = require('dayjs');


const constructReportFileName = (reportName, payload, ext, streamBuffer = false) => {
  const startDate = dayjs(payload.startDate).format('YYYY-MM-DD');
  const endDate = dayjs(payload.endDate).format('YYYY-MM-DD');
  const currentTime = dayjs().format('HH_mm_ss');

  const normalizedName = reportName.replace(/\s+/g, '_');

  let fileName;
  if (payload.fileName) {
    fileName = `${payload.fileName}.${ext}`;
  } else {
    if (streamBuffer) {
      fileName = `${normalizedName}_${startDate}_${endDate}_${currentTime}.${ext}`;
    } else {
      fileName = `${normalizedName}_${startDate}_${endDate}.${ext}`;
    }
  }

  return fileName;
}



module.exports = constructReportFileName;