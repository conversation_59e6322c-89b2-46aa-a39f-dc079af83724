const logger = require("../config/logger");




const getUserDetails = async (userId) => {
const { userService, reportsService } = require("../services");

  const userDetails = await userService.getUserById(userId);
  logger.info(`for user analysis use user based filters`);

  // Fetch customer and supplier lists only if needed
  if (userDetails.allCustomerList) {
    userDetails.customerList = await reportsService.getCustomerList(
      userDetails,
      true
    );
  }
  if (userDetails.allSupplierList) {
    userDetails.supplierList = await reportsService.getSupplierList(
      userDetails,
      true
    );
  }

  return userDetails
}



module.exports = getUserDetails