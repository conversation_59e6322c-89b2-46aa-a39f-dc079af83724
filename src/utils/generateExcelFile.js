const XLSX = require("xlsx");
const { MAX_ROWS_FOR_EXCEL } = require("../config/globalConfig");
const dayjs = require('dayjs');
const constructReportFileName = require("./constructReportFileName");

const constructExcel = async (headers, rows, payload, aggregateResponse) => {
  try {
    console.log("Preparing Excel document");

    if (rows.length > MAX_ROWS_FOR_EXCEL) {
      throw new Error("Data size is too large; cannot construct Excel file");
    }

    const wb = XLSX.utils.book_new();

    const sheetData = [
      [`Reports for ${payload.startDate} to ${payload.endDate}`],
    ];

    if (aggregateResponse && Object.keys(aggregateResponse).length > 0) {
      sheetData.push(Object.keys(aggregateResponse));   // Aggregate headers
      sheetData.push(Object.values(aggregateResponse)); // Aggregate values
      sheetData.push([]); // Blank row
    }

    sheetData.push(headers); // Header row

    const chunkSize = 10000;
    for (let i = 0; i < rows.length; i += chunkSize) {
      const chunk = rows.slice(i, i + chunkSize);
      sheetData.push(...chunk);
      console.log(`Processed rows: ${Math.min(i + chunkSize, rows.length)}`);
    }

    const ws = XLSX.utils.aoa_to_sheet(sheetData);
    ws['!cols'] = headers.map(() => ({ wch: 20 }));

    XLSX.utils.book_append_sheet(wb, ws, "Report");

    const buf = XLSX.write(wb, { type: "buffer", bookType: "xlsx" });

    console.log("Excel file generated successfully!");

    const filename = constructReportFileName(payload.reportName, payload, 'xlsx');

    return {
      buf,
      startDate: payload.startDate,
      endDate: payload.endDate,
      extension: ".xlsx",
      filename
    };
  } catch (error) {
    throw error;
  }
};

module.exports = constructExcel;
