const { PassThrough } = require('stream');
const { stringify } = require('csv-stringify');
const archiver = require('archiver');
const dayjs = require('dayjs');

/**
 * Creates a zipped CSV stream from headers, rows, and optional aggregate data.
 * Returns a zip Buffer and metadata.
 */
const streamCsvToZipBuffer = async (headers, rows, payload, aggregateResponse = {}) => {
  const csvStream = stringify();
  const zipStream = new PassThrough();
  const chunks = [];

  const archive = archiver('zip', { forceZip64: true });
  archive.pipe(zipStream);

  // Collect ZIP chunks
  zipStream.on('data', (chunk) => chunks.push(chunk));

  const startDate = dayjs(payload.startDate).format('YYYY-MM-DD');
  const endDate = dayjs(payload.endDate).format('YYYY-MM-DD');
  const currentTime = dayjs().format('HH_mm_ss');
  const filename = payload.fileName
    ? `${payload.fileName}.csv`
    : `${payload.reportName}_${startDate}_${endDate}_${currentTime}.csv`;

  // Add CSV stream to zip
  archive.append(csvStream, { name: filename });

  // Metadata rows
  csvStream.write([`Reports for ${payload.startDate} to ${payload.endDate}`]);

  if (aggregateResponse && Object.keys(aggregateResponse).length > 0) {
    csvStream.write(Object.keys(aggregateResponse));
    csvStream.write(Object.values(aggregateResponse));
    csvStream.write([]);
  }

  // Write headers
  csvStream.write(headers);

  // Write data
  const chunkSize = 10000;
  for (let i = 0; i < rows.length; i += chunkSize) {
    const chunk = rows.slice(i, i + chunkSize);
    chunk.forEach(row => {
      csvStream.write(row);
    });
  }

  // Finalize streams
  csvStream.end();
  archive.finalize();

  // Await stream completion
  await new Promise((resolve, reject) => {
    zipStream.on('end', resolve);
    zipStream.on('error', reject);
  });

  return {
    buf: Buffer.concat(chunks),
    startDate: payload.startDate,
    endDate: payload.endDate,
    extension: '.zip',
    filename: filename.replace('.csv', '.zip'),
  };
};

module.exports = streamCsvToZipBuffer;
