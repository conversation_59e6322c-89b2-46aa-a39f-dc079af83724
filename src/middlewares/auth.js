const passport = require('passport');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { resources } = require('../config/roles');
const { reportsService } = require('../services');

const { models } = require('../models'),
 { Role } = models;

const verifyCallback = (req, resolve, reject, resourceName, permission) => (err, user, info) => {
  (async () => {
    if (err || info || !user) {
      return reject(new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate'));
    }

    if (user.allCustomerList) {
      const customerList = await reportsService.getCustomerList(user, true);
      user.customerList = customerList;
    }

    if (user.allSupplierList) {
      const supplierList = await reportsService.getSupplierList(user, true);
      user.supplierList = supplierList;
    }

    req.user = user;
    const role = user.role;

    if (resourceName && resourceName.length > 0) {
      const permissionFlag = role.resources[resourceName] & permission;

      if (resourceName === resources.ROLE_MANAGEMENT && req.params.roleId === role.id) {
        return resolve();
      }

      console.log("in permission check", permissionFlag, permission, role.name);

      if (permissionFlag === permission) {
        return resolve();
      } else {
        console.log("no permission");
        return reject(new ApiError(httpStatus.FORBIDDEN, 'Forbidden'));
      }
    } else {
      return resolve();
    }
  })().catch((err) => {
    console.error("verifyCallback error:", err);
    reject(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Internal Error'));
  });
};


 auth = (resourceName,permission) => async (req,res,next) => new Promise((resolve,reject) => {
    passport.authenticate('jwt',{ session: false },verifyCallback(req,resolve,reject,resourceName,permission))(req,res,next);
  }).
    then(() => next()).
    catch((err) => next(err));

module.exports = auth;