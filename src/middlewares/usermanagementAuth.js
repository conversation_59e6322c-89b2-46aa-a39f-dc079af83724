const passport = require("passport");
const jwt = require("jsonwebtoken");
const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");

const JWT_SECRET = "1223334444"; // Use environment variable in production

const verifyCallback = (req, resolve, reject) => async (err, user, info) => {
  console.log("req.headers.authorization->", req.headers.authorization);
  if (err) {
    console.error("Token verification failed:", err || info);
    return reject(new ApiError(httpStatus.UNAUTHORIZED, "Unauthorized"));
  }

  try {
    const token = req.headers.authorization?.split(" ")[1];
    if (!token) {
      console.error("Missing token in request headers");
      return reject(new ApiError(httpStatus.UNAUTHORIZED, "Token missing"));
    }

    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded; // Attach decoded user info to the request
    resolve(); // Proceed to the next middleware
  } catch (error) {
    console.error("Invalid token:", error.message); // Debug log
    return reject(new ApiError(httpStatus.UNAUTHORIZED, "Invalid token"));
  }
};

const usermanagementAuth = (resourceName, permission) => async (req, res, next) =>
  new Promise((resolve, reject) => {
    passport.authenticate(
      "jwt",
      { session: false },
      verifyCallback(req, resolve, reject, resourceName, permission)
    )(req, res, next);
  })
    .then(() => next())
    .catch((err) => next(err));

module.exports = usermanagementAuth;
