-- Fix duplicate unique constraints on users table
-- This script removes all duplicate email unique constraints and keeps only one

USE smshub_db;

-- Drop all the duplicate unique constraints (keep the first one)
ALTER TABLE users DROP INDEX email_2;
ALTER TABLE users DROP INDEX email_3;
ALTER TABLE users DROP INDEX email_4;
ALTER TABLE users DROP INDEX email_5;
ALTER TABLE users DROP INDEX email_6;
ALTER TABLE users DROP INDEX email_7;
ALTER TABLE users DROP INDEX email_8;
ALTER TABLE users DROP INDEX email_9;
ALTER TABLE users DROP INDEX email_10;
ALTER TABLE users DROP INDEX email_11;
ALTER TABLE users DROP INDEX email_12;
ALTER TABLE users DROP INDEX email_13;
ALTER TABLE users DROP INDEX email_14;
ALTER TABLE users DROP INDEX email_15;
ALTER TABLE users DROP INDEX email_16;
ALTER TABLE users DROP INDEX email_17;
ALTER TABLE users DROP INDEX email_18;
ALTER TABLE users DROP INDEX email_19;
ALTER TABLE users DROP INDEX email_20;
ALTER TABLE users DROP INDEX email_21;
ALTER TABLE users DROP INDEX email_22;
ALTER TABLE users DROP INDEX email_23;
ALTER TABLE users DROP INDEX email_24;
ALTER TABLE users DROP INDEX email_25;
ALTER TABLE users DROP INDEX email_26;
ALTER TABLE users DROP INDEX email_27;
ALTER TABLE users DROP INDEX email_28;
ALTER TABLE users DROP INDEX email_29;
ALTER TABLE users DROP INDEX email_30;
ALTER TABLE users DROP INDEX email_31;
ALTER TABLE users DROP INDEX email_32;
ALTER TABLE users DROP INDEX email_33;
ALTER TABLE users DROP INDEX email_34;
ALTER TABLE users DROP INDEX email_35;
ALTER TABLE users DROP INDEX email_36;
ALTER TABLE users DROP INDEX email_37;
ALTER TABLE users DROP INDEX email_38;
ALTER TABLE users DROP INDEX email_39;
ALTER TABLE users DROP INDEX email_40;
ALTER TABLE users DROP INDEX email_41;
ALTER TABLE users DROP INDEX email_42;
ALTER TABLE users DROP INDEX email_43;
ALTER TABLE users DROP INDEX email_44;
ALTER TABLE users DROP INDEX email_45;
ALTER TABLE users DROP INDEX email_46;
ALTER TABLE users DROP INDEX email_47;
ALTER TABLE users DROP INDEX email_48;
ALTER TABLE users DROP INDEX email_49;
ALTER TABLE users DROP INDEX email_50;
ALTER TABLE users DROP INDEX email_51;
ALTER TABLE users DROP INDEX email_52;
ALTER TABLE users DROP INDEX email_53;
ALTER TABLE users DROP INDEX email_54;
ALTER TABLE users DROP INDEX email_55;
ALTER TABLE users DROP INDEX email_56;
ALTER TABLE users DROP INDEX email_57;
ALTER TABLE users DROP INDEX email_58;
ALTER TABLE users DROP INDEX email_59;
ALTER TABLE users DROP INDEX email_60;
ALTER TABLE users DROP INDEX email_61;
ALTER TABLE users DROP INDEX email_62;

-- Verify that only the original 'email' unique constraint remains
SHOW INDEX FROM users WHERE Column_name = 'email';
