{"info": {"_postman_id": "f4718526-e48c-4e70-8335-abd5c77d374a", "name": "SMSHub", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "25525240"}, "item": [{"name": "REST-APIs", "item": [{"name": "Alert-mgmt", "item": [{"name": "createAlert - <PERSON>rror", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 0,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"node\": [\r\n        \"hub-jump\",\r\n        \"hub-jump2\"\r\n    ],\r\n    \"resultCode\": [\r\n        2,\r\n        1,\r\n        5\r\n    ],\r\n    \"errorDescription\": [\r\n        101,\r\n        104,\r\n        115\r\n    ],\r\n    \"alertName\": \"Error alert 3\",\r\n    \"alertType\": \"Error\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "response": [{"name": "create alert - Error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 0,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"node\": [\r\n        \"hub-jump\",\r\n        \"hub-jump2\"\r\n    ],\r\n    \"resultCode\": [\r\n        2,\r\n        1,\r\n        5\r\n    ],\r\n    \"errorDescription\": [\r\n        101,\r\n        104,\r\n        115\r\n    ],\r\n    \"alertName\": \"Error alert 2\",\r\n    \"alertType\": \"Error\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"message\": \"<PERSON><PERSON> created successfully!\",\n    \"res\": {\n        \"common_filter\": \"customer_name IN ('Wirelsc_C_P2P','Airtel_Srilanka_P2P_Out') AND host_id IN ('hub-jump','hub-jump2') AND result_code IN (2,1,5) AND error_description IN (101,104,115)\",\n        \"compared_filter\": null,\n        \"compared_to\": {\n            \"aggregate_function\": \"SUM\",\n            \"timediff\": 0,\n            \"timerange\": 1\n        },\n        \"contact_point\": \"SMSHub-BE-1734328915771\",\n        \"current_filter\": null,\n        \"evaluation_timeframe\": 60,\n        \"fields\": [\n            \"customer_name\",\n            \"total_submissions\",\n            \"delivery_failure_count_new\",\n            \"result_code\",\n            \"error_description\"\n        ],\n        \"id\": 64,\n        \"metadata\": {\n            \"max_allowed_percentage_change\": \"1%\",\n            \"additionalEmails\": \"\"\n        },\n        \"name\": \"Error alert 3\",\n        \"run_every\": 15,\n        \"timezone\": \"Asia/Kolkata\",\n        \"triggering_expression\": [\n            {\n                \"name\": \"delivery_failure_percentage_today\",\n                \"expression\": \"abs( ({delivery_failure_count_new:current} || 0) / ( {total_submissions:current} || 1) * 100 )\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"trigger_condition\",\n                \"expression\": \"{delivery_failure_percentage_today} > 1\",\n                \"type\": \"math\"\n            }\n        ]\n    }\n}"}]}, {"name": "createAlert - Delivery report drop", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 1,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"Delivery Report Pending Alert\",\r\n    \"alertType\": \"Delivery Report Pending\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "response": [{"name": "create alert - Delivery report drop", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 1,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"Delivery Report Pending Alert\",\r\n    \"alertType\": \"Delivery Report Pending\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"message\": \"<PERSON><PERSON> created successfully!\",\n    \"res\": {\n        \"common_filter\": \"customer_name IN ('Wirelsc_C_P2P','Airtel_Srilanka_P2P_Out')\",\n        \"compared_filter\": null,\n        \"compared_to\": {\n            \"aggregate_function\": \"SUM\",\n            \"timediff\": 1,\n            \"timerange\": 1\n        },\n        \"contact_point\": \"SMSHub-BE-1734328915771\",\n        \"current_filter\": null,\n        \"evaluation_timeframe\": 60,\n        \"fields\": [\n            \"customer_name\",\n            \"total_submissions\",\n            \"delivery_failure_count_new\"\n        ],\n        \"id\": 62,\n        \"metadata\": {\n            \"max_allowed_percentage_change\": \"1%\",\n            \"additionalEmails\": \"\"\n        },\n        \"name\": \"Delivery Report Pending Alert\",\n        \"run_every\": 15,\n        \"timezone\": \"Asia/Kolkata\",\n        \"triggering_expression\": [\n            {\n                \"name\": \"valid_submissions_today\",\n                \"expression\": \"({total_submissions:current} || 0) >= ({total_submissions:current} || 0)\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_success_today\",\n                \"expression\": \"(({total_submissions:current} || 0) - ({delivery_failure_count_new:current} || 0))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_success_yesterday\",\n                \"expression\": \"(({total_submissions:previous} || 0) - ({delivery_failure_count_new:previous} || 0))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"percentage_difference\",\n                \"expression\": \"abs((({delivery_success_today} / ({total_submissions:current} || 1)) * 100) - (({delivery_success_yesterday} / ({total_submissions:previous} || 1)) * 100))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"trigger_alert\",\n                \"expression\": \"{valid_submissions_today} && ({percentage_difference} > 1)\",\n                \"type\": \"math\"\n            }\n        ]\n    }\n}"}]}, {"name": "createAlert - Volume spike or drop", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timerange\": 3,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"testing\",\r\n    \"alertType\": \"Volume Drop or Spike\",\r\n    \"individuals\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"groups\": [],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 10,\r\n    \"volumeType\": \"both\",\r\n    \"category\": \"Major\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/v1/alert", "host": ["{{host}}"], "path": ["v1", "alert"]}}, "response": [{"name": "create alert - Delivery report drop", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 1,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"Volume Drop or Spike Alert\",\r\n    \"alertType\": \"Volume Drop or Spike\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 1000,\r\n    \"volumeType\": \"spike\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"message\": \"<PERSON><PERSON> created successfully!\",\n    \"res\": {\n        \"common_filter\": \"customer_name IN ('Wirelsc_C_P2P','Airtel_Srilanka_P2P_Out')\",\n        \"compared_filter\": null,\n        \"compared_to\": {\n            \"aggregate_function\": \"SUM\",\n            \"timediff\": 1,\n            \"timerange\": 1\n        },\n        \"contact_point\": \"SMSHub-BE-1734328915771\",\n        \"current_filter\": null,\n        \"evaluation_timeframe\": 60,\n        \"fields\": [\n            \"customer_name\",\n            \"total_submissions\",\n            \"delivery_failure_count_new\"\n        ],\n        \"id\": 67,\n        \"metadata\": {\n            \"max_allowed_percentage_change\": \"1%\",\n            \"additionalEmails\": \"\"\n        },\n        \"name\": \"Volume Drop or Spike Alert\",\n        \"run_every\": 15,\n        \"timezone\": \"Asia/Kolkata\",\n        \"triggering_expression\": [\n            {\n                \"name\": \"valid_submissions_today\",\n                \"expression\": \"({total_submissions:current} || 0) >= (1000 || 0)\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_success_today\",\n                \"expression\": \"(({total_submissions:current} || 0) - ({delivery_failure_count_new:current} || 0))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_success_yesterday\",\n                \"expression\": \"(({total_submissions:previous} || 0) - ({delivery_failure_count_new:previous} || 0))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"percentage_difference\",\n                \"expression\": \"abs((({delivery_success_today} / ({total_submissions:current} || 1)) * 100) - (({delivery_success_yesterday} / ({total_submissions:previous} || 1)) * 100))\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"trigger_alert\",\n                \"expression\": \"{valid_submissions_today} && ({percentage_difference} > 1)\",\n                \"type\": \"math\"\n            }\n        ]\n    }\n}"}]}, {"name": "createAlert - Delivery drop", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 1,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"Delivery Drop Alert\",\r\n    \"alertType\": \"Delivery Drop\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"filters\":[],\r\n    \"deviation\": 10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "response": [{"name": "create alert - Delivery drop", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timediff\": 1,\r\n    \"timerange\": 1,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"Delivery Drop Alert\",\r\n    \"alertType\": \"Delivery Drop\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"filters\":[],\r\n    \"deviation\": 10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:9697/v1/alert", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert"]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"message\": \"<PERSON><PERSON> created successfully!\",\n    \"res\": {\n        \"common_filter\": \"customer_name IN ('Wirelsc_C_P2P','Airtel_Srilanka_P2P_Out')\",\n        \"compared_filter\": null,\n        \"compared_to\": {\n            \"aggregate_function\": \"SUM\",\n            \"timediff\": 1,\n            \"timerange\": 1\n        },\n        \"contact_point\": \"SMSHub-BE-1734328915771\",\n        \"current_filter\": null,\n        \"evaluation_timeframe\": 60,\n        \"fields\": [\n            \"customer_name\",\n            \"total_submissions\",\n            \"delivery_failure_count_new\"\n        ],\n        \"id\": 68,\n        \"metadata\": {\n            \"max_allowed_percentage_change\": \"1%\",\n            \"additionalEmails\": \"\"\n        },\n        \"name\": \"Delivery Drop Alert\",\n        \"run_every\": 15,\n        \"timezone\": \"Asia/Kolkata\",\n        \"triggering_expression\": [\n            {\n                \"name\": \"delivery_failure_percentage_today\",\n                \"expression\": \"( ({delivery_failure_count_new:current} || 0) / ({total_submissions:current} || 1) ) * 100\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_failure_percentage_previous\",\n                \"expression\": \"( ({delivery_failure_count_new:previous} || 0) / ({total_submissions:previous} || 1) ) * 100\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"delivery_failure_percentage_difference\",\n                \"expression\": \"abs( {delivery_failure_percentage_today} - {delivery_failure_percentage_previous} )\",\n                \"type\": \"math\"\n            },\n            {\n                \"name\": \"trigger_condition\",\n                \"expression\": \"{delivery_failure_percentage_difference} > 1\",\n                \"type\": \"math\"\n            }\n        ]\n    }\n}"}]}, {"name": "alerts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert", "host": ["{{host}}"], "path": ["v1", "alert"]}}, "response": []}, {"name": "alert by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{host}}/v1/alert/:id", "host": ["{{host}}"], "path": ["v1", "alert", ":id"], "variable": [{"key": "id", "value": "204"}]}}, "response": []}, {"name": "alert by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/:id", "host": ["{{host}}"], "path": ["v1", "alert", ":id"], "variable": [{"key": "id", "value": "205"}]}}, "response": []}, {"name": "update alert by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"timerange\": 3,\r\n    \"evaluationTimeframe\": 60,\r\n    \"runEvery\": 15,\r\n    \"customers\": [\r\n        \"Wirelsc_C_P2P\",\r\n        \"Airtel_Srilanka_P2P_Out\"\r\n    ],\r\n    \"suppliers\": [],\r\n    \"destinations\": [],\r\n    \"alertName\": \"111222233344455566677 testing\",\r\n    \"alertType\": \"Volume Drop or Spike\",\r\n    \"contactPoint\": [\r\n        \"<EMAIL>\"\r\n    ],\r\n    \"threshold\": 1,\r\n    \"minSubmissionCount\": 10,\r\n    \"volumeType\": \"both\",\r\n    \"category\": \"Major\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/v1/alert/:id", "host": ["{{host}}"], "path": ["v1", "alert", ":id"], "variable": [{"key": "id", "value": "198"}]}}, "response": []}]}, {"name": "Alert-mgmt History", "item": [{"name": "history by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/history/199", "host": ["{{host}}"], "path": ["v1", "alert", "history", "199"]}}, "response": []}, {"name": "view history by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/history/view/:alertHistoryId", "host": ["{{host}}"], "path": ["v1", "alert", "history", "view", ":alertHistoryId"], "variable": [{"key": "alertHistoryId", "value": "57"}]}}, "response": []}, {"name": "Bulk update name list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/history/alert-name-list", "host": ["{{host}}"], "path": ["v1", "alert", "history", "alert-name-list"]}}, "response": []}, {"name": "update bulk history", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"status\": \"fixed\",\r\n    \"alertHistoryIds\": [51,52]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/v1/alert/history/", "host": ["{{host}}"], "path": ["v1", "alert", "history", ""]}}, "response": []}, {"name": "history", "event": [{"listen": "test", "script": {"exec": ["var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>ID</th>\r", "        <th>Description</th>\r", "        <th>State</th>\r", "        <th>Timestamp</th>\r", "        <th>Status</th>\r", "        <th>Name</th>\r", "        <th>Category</th>\r", "        <th>Alert Type</th>\r", "    </tr>\r", "    \r", "    {{#each response}}\r", "        <tr>\r", "            <td>{{id}}</td>\r", "            <td>{{description}}</td>\r", "            <td>{{state}}</td>\r", "            <td>{{timestamp}}</td>\r", "            <td>{{status}}</td>\r", "            <td>{{name}}</td>\r", "            <td>{{category}}</td>\r", "            <td>{{alert_type}}</td>\r", "        </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    var csvData = pm.response.text();\r", "    \r", "    // Convert CSV to JSON\r", "    var lines = csvData.split(\"\\\\n\");\r", "    var result = [];\r", "    var headers = lines[0].split(\",\");\r", "    \r", "    for (var i = 1; i < lines.length; i++) {\r", "        var obj = {};\r", "        var currentline = lines[i].split(\",\");\r", "        for (var j = 0; j < headers.length; j++) {\r", "            obj[headers[j].replace(/\"/g, \"\")] = currentline[j].replace(/\"/g, \"\");\r", "        }\r", "        result.push(obj);\r", "    }\r", "    \r", "    return {response: result};\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/history/?startDate=2025-01-11 00:00:00&endDate=2025-01-16 00:00:00&page=1&limit=10&download=1&downloadType=csv", "host": ["{{host}}"], "path": ["v1", "alert", "history", ""], "query": [{"key": "startDate", "value": "2025-01-11 00:00:00"}, {"key": "endDate", "value": "2025-01-16 00:00:00"}, {"key": "status", "value": "open,fixed", "disabled": true}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "test", "disabled": true}, {"key": "download", "value": "1"}, {"key": "downloadType", "value": "csv"}]}}, "response": []}]}, {"name": "Dropdown", "item": [{"name": "result-code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/err-des-mapping/result-code/12", "host": ["{{host}}"], "path": ["v1", "alert", "err-des-mapping", "result-code", "12"], "query": [{"key": "failure_error", "value": "12", "disabled": true}]}}, "response": [{"name": "result-code", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/err-des-mapping/result-code/12", "host": ["{{host}}"], "path": ["v1", "alert", "err-des-mapping", "result-code", "12"], "query": [{"key": "failure_error", "value": "12", "disabled": true}]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "[\n    {\n        \"label\": \"2\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"10\",\n        \"value\": \"10\"\n    },\n    {\n        \"label\": \"4\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"12\",\n        \"value\": \"12\"\n    }\n]"}]}, {"name": "metadata", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9697/v1/alert/metadata/:keys", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert", "metadata", ":keys"], "variable": [{"key": "keys", "value": "alertFilterOptions"}]}}, "response": [{"name": "metadata", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:9697/v1/alert/metadata/:keys", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "alert", "metadata", ":keys"], "variable": [{"key": "keys", "value": "volumeTypesOptions,alertFilterOptions,alertTypesOptions"}]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"volumeTypesOptions\": [\n        {\n            \"label\": \"spike\",\n            \"value\": \"spike\"\n        },\n        {\n            \"label\": \"drop\",\n            \"value\": \"drop\"\n        },\n        {\n            \"label\": \"both\",\n            \"value\": \"both\"\n        }\n    ],\n    \"alertFilterOptions\": [\n        {\n            \"label\": [\n                \"Destination\",\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\",\n                \"Customer-Supplier Destination\"\n            ],\n            \"value\": [\n                \"Destination\",\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\",\n                \"Customer-Supplier Destination\"\n            ]\n        },\n        {\n            \"label\": [\n                \"Destination\",\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\",\n                \"Customer-Supplier Destination\"\n            ],\n            \"value\": [\n                \"Destination\",\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\",\n                \"Customer-Supplier Destination\"\n            ]\n        },\n        {\n            \"label\": [\n                \"Destination\",\n                \"Customer\",\n                \"Customer Destination\"\n            ],\n            \"value\": [\n                \"Destination\",\n                \"Customer\",\n                \"Customer Destination\"\n            ]\n        },\n        {\n            \"label\": [\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\"\n            ],\n            \"value\": [\n                \"Supplier\",\n                \"Customer\",\n                \"Supplier Destination\",\n                \"Customer Destination\"\n            ]\n        }\n    ],\n    \"alertTypesOptions\": [\n        {\n            \"label\": \"Delivery Drop\",\n            \"value\": \"Delivery Drop\"\n        },\n        {\n            \"label\": \"Error\",\n            \"value\": \"Error\"\n        },\n        {\n            \"label\": \"Volume Drop or Spike\",\n            \"value\": \"Volume Drop or Spike\"\n        },\n        {\n            \"label\": \"Delivery Report Pending\",\n            \"value\": \"Delivery Report Pending\"\n        }\n    ]\n}"}]}, {"name": "error-description", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/err-des-mapping/err-description", "host": ["{{host}}"], "path": ["v1", "alert", "err-des-mapping", "err-description"], "query": [{"key": "failure_error", "value": "12", "disabled": true}]}}, "response": [{"name": "error-description", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/err-des-mapping/err-description", "host": ["{{host}}"], "path": ["v1", "alert", "err-des-mapping", "err-description"], "query": [{"key": "failure_error", "value": "12", "disabled": true}]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "[\n    {\n        \"label\": \"101 Absent Subscriber For SM\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"102 Call Barred\",\n        \"value\": \"13\"\n    },\n    {\n        \"label\": \"103 Subscriber Busy for MT\",\n        \"value\": \"31\"\n    },\n    {\n        \"label\": \"104 Facility Not Supported\",\n        \"value\": \"21\"\n    },\n    {\n        \"label\": \"105 SS Incompatible\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"106 SM Delivery Fail\",\n        \"value\": \"32\"\n    },\n    {\n        \"label\": \"107 Message List Full\",\n        \"value\": \"33\"\n    },\n    {\n        \"label\": \"108 Network System Failure\",\n        \"value\": \"34\"\n    },\n    {\n        \"label\": \"109 Data Missing\",\n        \"value\": \"35\"\n    },\n    {\n        \"label\": \"110 Unexpected Data\",\n        \"value\": \"36\"\n    },\n    {\n        \"label\": \"112 Absent Subscriber\",\n        \"value\": \"27\"\n    },\n    {\n        \"label\": \"113 Busy Subscriber\",\n        \"value\": \"45\"\n    },\n    {\n        \"label\": \"111 No Subscriber Reply\",\n        \"value\": \"46\"\n    },\n    {\n        \"label\": \"114 Provider No Response from Peer\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"115 Request Timeout\",\n        \"value\": \"-1\"\n    },\n    {\n        \"label\": \"116 Tele Service Not Provisioned\",\n        \"value\": \"11\"\n    },\n    {\n        \"label\": \"117 SMPP Protocol Error at Redirection\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"118 UCP Protocol Error at Redirection\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"119 AppSMSC Error at Redirection\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"120 CIMD Protocol Error at Redirection\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"121 Timeout at SRI/MT\",\n        \"value\": \"400\"\n    },\n    {\n        \"label\": \"122 HLR/MSC Timeout\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"123 Unknown Subscriber\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"124 Unknown Base Station\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"125 Unknown MSC\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"126 Unidentified Subscriber\",\n        \"value\": \"5\"\n    },\n    {\n        \"label\": \"127 Unknown Equipment\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"128 Roaming Not Allowed\",\n        \"value\": \"8\"\n    },\n    {\n        \"label\": \"129 Illegal Subscriber\",\n        \"value\": \"9\"\n    },\n    {\n        \"label\": \"130 Bearer Service Not Supported\",\n        \"value\": \"10\"\n    },\n    {\n        \"label\": \"131 Error Equipment\",\n        \"value\": \"12\"\n    },\n    {\n        \"label\": \"132 Error Forward Violation\",\n        \"value\": \"14\"\n    },\n    {\n        \"label\": \"133 Error CUG Reject\",\n        \"value\": \"15\"\n    },\n    {\n        \"label\": \"134 Illegal SS Operation\",\n        \"value\": \"16\"\n    },\n    {\n        \"label\": \"135 SS Error Status\",\n        \"value\": \"17\"\n    },\n    {\n        \"label\": \"136 SS Not Available\",\n        \"value\": \"18\"\n    },\n    {\n        \"label\": \"137 SS Subscription Violation\",\n        \"value\": \"19\"\n    },\n    {\n        \"label\": \"138 No Handover Number Available\",\n        \"value\": \"25\"\n    },\n    {\n        \"label\": \"139 Subsequent Handover Failure\",\n        \"value\": \"26\"\n    },\n    {\n        \"label\": \"140 Incompatible Terminal\",\n        \"value\": \"28\"\n    },\n    {\n        \"label\": \"141 Short Term Denial\",\n        \"value\": \"29\"\n    },\n    {\n        \"label\": \"142 Long Term Denial\",\n        \"value\": \"30\"\n    },\n    {\n        \"label\": \"143 PW Registration Failure\",\n        \"value\": \"37\"\n    },\n    {\n        \"label\": \"144 Negative PW Check\",\n        \"value\": \"38\"\n    },\n    {\n        \"label\": \"145 No Roaming Number Available\",\n        \"value\": \"39\"\n    },\n    {\n        \"label\": \"146 Tracing Buffer Full\",\n        \"value\": \"40\"\n    },\n    {\n        \"label\": \"147 Number of PW attempts Violation\",\n        \"value\": \"44\"\n    },\n    {\n        \"label\": \"148 Forwarding Failed\",\n        \"value\": \"47\"\n    },\n    {\n        \"label\": \"149 Not Allowed\",\n        \"value\": \"48\"\n    },\n    {\n        \"label\": \"150 ATI Not Allowed\",\n        \"value\": \"49\"\n    },\n    {\n        \"label\": \"151 No Group Call Number Available\",\n        \"value\": \"50\"\n    },\n    {\n        \"label\": \"152 Resource Limitation\",\n        \"value\": \"51\"\n    },\n    {\n        \"label\": \"153 Unauthorized Requesting Network\",\n        \"value\": \"52\"\n    },\n    {\n        \"label\": \"154 Unathorized LCS Client\",\n        \"value\": \"53\"\n    },\n    {\n        \"label\": \"202 SMPP - Message ID is invalid\",\n        \"value\": \"12\"\n    },\n    {\n        \"label\": \"155 Unknown or Unreachable LCS Client\",\n        \"value\": \"58\"\n    },\n    {\n        \"label\": \"156 Unknown Alphabet\",\n        \"value\": \"71\"\n    },\n    {\n        \"label\": \"157 USSD Busy\",\n        \"value\": \"72\"\n    },\n    {\n        \"label\": \"158 Error Gateway\",\n        \"value\": \"117\"\n    },\n    {\n        \"label\": \"159 Provider Error Duplicate Invoke ID\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"160 Provider Error Service Not Supported\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"161 Provider Error Mistyped Paramter\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"162 Provider Error Resource Limitation\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"163 Provider Error Initiating Release\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"164 Provider Error Unexpected Response\",\n        \"value\": \"5\"\n    },\n    {\n        \"label\": \"165 Provider Error Service Completion Failure\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"166 Provider Error Invalid Response\",\n        \"value\": \"8\"\n    },\n    {\n        \"label\": \"167 Stack/Sig Error Map User specific Reason\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"168 Stack/Sig Error Map User Resource Limitation\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"169 Stack/Sig Error Map Resource Unavailable\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"170 Stack/Sig Error Map Application Procedure Cancellation\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"171 Stack/Sig Error Map Provider Malfunction\",\n        \"value\": \"10\"\n    },\n    {\n        \"label\": \"172 Stack/Sig Error Map Unrecognised Transaction ID\",\n        \"value\": \"11\"\n    },\n    {\n        \"label\": \"173 Stack/Sig Error Dialog Resource Limitation\",\n        \"value\": \"12\"\n    },\n    {\n        \"label\": \"174 Stack/Sig Error Map Maintenance Activity\",\n        \"value\": \"13\"\n    },\n    {\n        \"label\": \"175 Stack/Sig Error Map Version Incompatibility\",\n        \"value\": \"14\"\n    },\n    {\n        \"label\": \"176 Stack/Sig Error Abnormal Map Dialogue\",\n        \"value\": \"15\"\n    },\n    {\n        \"label\": \"177 Stack/Sig Error Abnormal Event Detected by Peer\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"178 Stack/Sig Response Rejected By Peer\",\n        \"value\": \"21\"\n    },\n    {\n        \"label\": \"179 Stack/Sig Abnormal Event Recieved From Peer\",\n        \"value\": \"22\"\n    },\n    {\n        \"label\": \"181 Stack/Sig Application Context Not Supported\",\n        \"value\": \"30\"\n    },\n    {\n        \"label\": \"182 Stack/Sig Dlg Invalid Destination Reference\",\n        \"value\": \"31\"\n    },\n    {\n        \"label\": \"183 Stack/Sig Invalid Origination Reference\",\n        \"value\": \"32\"\n    },\n    {\n        \"label\": \"184 Stack/Sig No Reson\",\n        \"value\": \"33\"\n    },\n    {\n        \"label\": \"185 Stack/Sig Remote Node Not Reachable\",\n        \"value\": \"34\"\n    },\n    {\n        \"label\": \"187 Stack/Sig Secured Transport Not Possible\",\n        \"value\": \"36\"\n    },\n    {\n        \"label\": \"186 Stack/Sig Map Potential Version Incompatibility\",\n        \"value\": \"35\"\n    },\n    {\n        \"label\": \"100 Unknown Errors\",\n        \"value\": \"-1\"\n    },\n    {\n        \"label\": \"188 Stack/Sig Usr Transport Protection Not Adequate\",\n        \"value\": \"37\"\n    },\n    {\n        \"label\": \"189 Redirection SMSC Not Connected\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"191 SMPP - Message Length is invalid\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"192 SMPP - Command Length is invalid\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"193 SMPP - Invalid Command ID\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"194 SMPP - Incorrect BIND Status\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"195 SMPP - ESME Already in Bound State\",\n        \"value\": \"5\"\n    },\n    {\n        \"label\": \"196 SMPP - Invalid Priority Flag\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"197 SMPP - Invalid Registered Delivery Flag\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"198 SMPP - System Error\",\n        \"value\": \"8\"\n    },\n    {\n        \"label\": \"206 SMPP - Cancel SM Failed\",\n        \"value\": \"17\"\n    },\n    {\n        \"label\": \"190 Position Method Failure\",\n        \"value\": \"54\"\n    },\n    {\n        \"label\": \"201 SMPP - Invalid Dest Addr\",\n        \"value\": \"11\"\n    },\n    {\n        \"label\": \"200 SMPP - Invalid Source Address\",\n        \"value\": \"10\"\n    },\n    {\n        \"label\": \"203 SMPP - Bind Failed\",\n        \"value\": \"13\"\n    },\n    {\n        \"label\": \"204 SMPP - Invalid Password\",\n        \"value\": \"14\"\n    },\n    {\n        \"label\": \"205 SMPP - Invalid System ID\",\n        \"value\": \"15\"\n    },\n    {\n        \"label\": \"207 SMPP - Replace SM Failed\",\n        \"value\": \"19\"\n    },\n    {\n        \"label\": \"208 SMPP - Message Queue Full\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"209 SMPP - Invalid Service Type\",\n        \"value\": \"21\"\n    },\n    {\n        \"label\": \"210 SMPP - Invalid number of destinations\",\n        \"value\": \"51\"\n    },\n    {\n        \"label\": \"211 SMPP - Invalid Distribution List name\",\n        \"value\": \"52\"\n    },\n    {\n        \"label\": \"212 SMPP - Destination flag is invalid\",\n        \"value\": \"64\"\n    },\n    {\n        \"label\": \"213 SMPP - Invalid submit with replace request\",\n        \"value\": \"66\"\n    },\n    {\n        \"label\": \"214 SMPP - Invalid esm_class field data\",\n        \"value\": \"67\"\n    },\n    {\n        \"label\": \"215 SMPP - Cannot Submit to Distribution List\",\n        \"value\": \"68\"\n    },\n    {\n        \"label\": \"216 SMPP - submit_sm or submit_multi failed\",\n        \"value\": \"69\"\n    },\n    {\n        \"label\": \"217 SMPP - Invalid Source address TON\",\n        \"value\": \"72\"\n    },\n    {\n        \"label\": \"218 SMPP - Invalid Source address NPI\",\n        \"value\": \"73\"\n    },\n    {\n        \"label\": \"219 SMPP - Invalid Destination address TON\",\n        \"value\": \"80\"\n    },\n    {\n        \"label\": \"220 SMPP - Invalid Destination address NPI\",\n        \"value\": \"81\"\n    },\n    {\n        \"label\": \"221 SMPP - Invalid system_type field\",\n        \"value\": \"83\"\n    },\n    {\n        \"label\": \"222 SMPP - Invalid replace_if_present flag\",\n        \"value\": \"84\"\n    },\n    {\n        \"label\": \"223 SMPP - Invalid number of messages\",\n        \"value\": \"85\"\n    },\n    {\n        \"label\": \"224 SMPP - Throttling error\",\n        \"value\": \"88\"\n    },\n    {\n        \"label\": \"225 SMPP - Invalid Scheduled Delivery Time\",\n        \"value\": \"97\"\n    },\n    {\n        \"label\": \"226 SMPP - Invalid message validity period\",\n        \"value\": \"98\"\n    },\n    {\n        \"label\": \"227 SMPP - Predefined Message Invalid or Not Found\",\n        \"value\": \"99\"\n    },\n    {\n        \"label\": \"228 SMPP - ESME Receiver Temporary App Error Code\",\n        \"value\": \"100\"\n    },\n    {\n        \"label\": \"229 SMPP - ESME Receiver Permanent App Error code\",\n        \"value\": \"101\"\n    },\n    {\n        \"label\": \"230 SMPP - ESME Receiver Reject Message Error code\",\n        \"value\": \"102\"\n    },\n    {\n        \"label\": \"231 SMPP - query_sm request failed\",\n        \"value\": \"103\"\n    },\n    {\n        \"label\": \"232 SMPP - Error in the optional part of the PDU body\",\n        \"value\": \"192\"\n    },\n    {\n        \"label\": \"233 SMPP - Optional Parameter not allowed\",\n        \"value\": \"193\"\n    },\n    {\n        \"label\": \"234 SMPP - Invalid Parameter Length\",\n        \"value\": \"194\"\n    },\n    {\n        \"label\": \"235 SMPP - Expected Optional Parameter missing\",\n        \"value\": \"195\"\n    },\n    {\n        \"label\": \"236 SMPP - Invalid Optional Parameter Value\",\n        \"value\": \"196\"\n    },\n    {\n        \"label\": \"237 SMPP - Delivery Failure\",\n        \"value\": \"254\"\n    },\n    {\n        \"label\": \"238 SMPP - Unknown Error\",\n        \"value\": \"255\"\n    },\n    {\n        \"label\": \"239 CIMD - Unexpected operation\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"240 CIMD - Syntax error\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"241 CIMD - Unsupported parameter error\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"242 CIMD - Connection to MC lost\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"243 CIMD - No response from MC\",\n        \"value\": \"5\"\n    },\n    {\n        \"label\": \"244 CIMD - General system error\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"245 CIMD - Cannot find information\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"246 CIMD - Parameter formatting error\",\n        \"value\": \"8\"\n    },\n    {\n        \"label\": \"247 CIMD - Requested operation failed\",\n        \"value\": \"9\"\n    },\n    {\n        \"label\": \"248 CIMD - Invalid login\",\n        \"value\": \"100\"\n    },\n    {\n        \"label\": \"249 CIMD - Incorrect access type\",\n        \"value\": \"101\"\n    },\n    {\n        \"label\": \"250 CIMD - Too many users with this login id\",\n        \"value\": \"102\"\n    },\n    {\n        \"label\": \"251 CIMD - Login refused by MC\",\n        \"value\": \"103\"\n    },\n    {\n        \"label\": \"252 CIMD - Incorrect destination address\",\n        \"value\": \"300\"\n    },\n    {\n        \"label\": \"253 CIMD - Incorrect number of destination addresses\",\n        \"value\": \"301\"\n    },\n    {\n        \"label\": \"254 CIMD - Syntax error in user data parameter\",\n        \"value\": \"302\"\n    },\n    {\n        \"label\": \"255 CIMD - Incorrect bin/head/normal user data parameter combination\",\n        \"value\": \"303\"\n    },\n    {\n        \"label\": \"256 CIMD - Incorrect dcs parameter usage\",\n        \"value\": \"304\"\n    },\n    {\n        \"label\": \"257 CIMD - Incorrect validity period parameters usage\",\n        \"value\": \"305\"\n    },\n    {\n        \"label\": \"258 CIMD - Incorrect originator address usage\",\n        \"value\": \"306\"\n    },\n    {\n        \"label\": \"259 CIMD - Incorrect pid parameter usage\",\n        \"value\": \"307\"\n    },\n    {\n        \"label\": \"260 CIMD - Incorrect first delivery parameter usage\",\n        \"value\": \"308\"\n    },\n    {\n        \"label\": \"261 CIMD - Incorrect reply path usage\",\n        \"value\": \"309\"\n    },\n    {\n        \"label\": \"262 CIMD - Incorrect status report request parameter usage\",\n        \"value\": \"310\"\n    },\n    {\n        \"label\": \"263 CIMD - Incorrect cancel enabled parameter usage\",\n        \"value\": \"311\"\n    },\n    {\n        \"label\": \"264 CIMD - Incorrect priority parameter usage\",\n        \"value\": \"312\"\n    },\n    {\n        \"label\": \"265 CIMD - Incorrect tariff class parameter usage\",\n        \"value\": \"313\"\n    },\n    {\n        \"label\": \"266 CIMD - Incorrect service description parameter usage\",\n        \"value\": \"314\"\n    },\n    {\n        \"label\": \"267 CIMD - Incorrect transport type parameter usage\",\n        \"value\": \"315\"\n    },\n    {\n        \"label\": \"268 CIMD - Incorrect message type parameter usage\",\n        \"value\": \"316\"\n    },\n    {\n        \"label\": \"269 CIMD - Incorrect mms parameter usage\",\n        \"value\": \"318\"\n    },\n    {\n        \"label\": \"270 CIMD - Incorrect operation timer parameter usage\",\n        \"value\": \"319\"\n    },\n    {\n        \"label\": \"271 CIMD - Incorrect address parameter usage\",\n        \"value\": \"400\"\n    },\n    {\n        \"label\": \"272 CIMD - Incorrect scts parameter usage\",\n        \"value\": \"401\"\n    },\n    {\n        \"label\": \"273 CIMD - Incorrect scts parameter usage\",\n        \"value\": \"500\"\n    },\n    {\n        \"label\": \"274 CIMD - Incorrect mode parameter usage\",\n        \"value\": \"501\"\n    },\n    {\n        \"label\": \"275 CIMD - Incorrect parameter combination\",\n        \"value\": \"502\"\n    },\n    {\n        \"label\": \"276 CIMD - Incorrect scts parameter usage\",\n        \"value\": \"600\"\n    },\n    {\n        \"label\": \"277 CIMD - Incorrect address parameter usage\",\n        \"value\": \"601\"\n    },\n    {\n        \"label\": \"278 CIMD - Incorrect mode parameter usage\",\n        \"value\": \"602\"\n    },\n    {\n        \"label\": \"279 CIMD - Incorrect parameter combination\",\n        \"value\": \"603\"\n    },\n    {\n        \"label\": \"280 CIMD - Changing password failed\",\n        \"value\": \"800\"\n    },\n    {\n        \"label\": \"281 CIMD - Changing password not allowed\",\n        \"value\": \"801\"\n    },\n    {\n        \"label\": \"282 CIMD - Unsupported item requested\",\n        \"value\": \"900\"\n    },\n    {\n        \"label\": \"283 CIMD - Release, call barred\",\n        \"value\": \"750\"\n    },\n    {\n        \"label\": \"284 CIMD - Release, system failure\",\n        \"value\": \"751\"\n    },\n    {\n        \"label\": \"285 CIMD - Release, data missing\",\n        \"value\": \"752\"\n    },\n    {\n        \"label\": \"286 CIMD - Release, unexpected data value\",\n        \"value\": \"753\"\n    },\n    {\n        \"label\": \"288 CIMD - Release, absent subscriber\",\n        \"value\": \"754\"\n    },\n    {\n        \"label\": \"289 CIMD - Release, illegal subscriber\",\n        \"value\": \"755\"\n    },\n    {\n        \"label\": \"290 CIMD - Release, illegal equipment\",\n        \"value\": \"756\"\n    },\n    {\n        \"label\": \"291 CIMD - Release, unknown alphabet\",\n        \"value\": \"757\"\n    },\n    {\n        \"label\": \"292 CIMD - Release, USSD busy\",\n        \"value\": \"758\"\n    },\n    {\n        \"label\": \"293 CIMD - Release, operation timer expired\",\n        \"value\": \"759\"\n    },\n    {\n        \"label\": \"294 CIMD - Release, unexpected primitive\",\n        \"value\": \"760\"\n    },\n    {\n        \"label\": \"295 CIMD - Release, wait timer expired\",\n        \"value\": \"761\"\n    },\n    {\n        \"label\": \"296 CIMD - Release, data error\",\n        \"value\": \"762\"\n    },\n    {\n        \"label\": \"297 CIMD - Release, too long USSD data\",\n        \"value\": \"763\"\n    },\n    {\n        \"label\": \"298 CIMD - Release, unknown MS address\",\n        \"value\": \"764\"\n    },\n    {\n        \"label\": \"299 CIMD - Release, network congestion\",\n        \"value\": \"765\"\n    },\n    {\n        \"label\": \"300 CIMD - Release, internal congestion\",\n        \"value\": \"766\"\n    },\n    {\n        \"label\": \"301 CIMD - Release, no network connection\",\n        \"value\": \"767\"\n    },\n    {\n        \"label\": \"302 CIMD - Release, USSD not supported\",\n        \"value\": \"768\"\n    },\n    {\n        \"label\": \"303 UCP - Checksum error\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"304 UCP - Syntax error\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"305 UCP - Operation not supported by system\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"306 UCP - Operation not allowed\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"307 UCP - Call barring active\",\n        \"value\": \"5\"\n    },\n    {\n        \"label\": \"308 UCP - AdC invalid\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"309 UCP - Authentication failure\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"310 UCP - Legitimisation code for all calls, failure\",\n        \"value\": \"8\"\n    },\n    {\n        \"label\": \"311 UCP - GA not valid\",\n        \"value\": \"9\"\n    },\n    {\n        \"label\": \"312 UCP - Repetition not allowed\",\n        \"value\": \"10\"\n    },\n    {\n        \"label\": \"313 UCP - Legitimisation code for repetition, failure\",\n        \"value\": \"11\"\n    },\n    {\n        \"label\": \"314 UCP - Priority call not allowed\",\n        \"value\": \"12\"\n    },\n    {\n        \"label\": \"315 UCP - Legitimisation code for priority call, failure\",\n        \"value\": \"13\"\n    },\n    {\n        \"label\": \"316 UCP - Urgent message not allowed\",\n        \"value\": \"14\"\n    },\n    {\n        \"label\": \"317 UCP - Legitimisation code for urgent message, failure\",\n        \"value\": \"15\"\n    },\n    {\n        \"label\": \"318 UCP - Reverse charging not allowed\",\n        \"value\": \"16\"\n    },\n    {\n        \"label\": \"319 UCP - Legitimisation code for rev. charging, failure\",\n        \"value\": \"17\"\n    },\n    {\n        \"label\": \"320 UCP - Deferred delivery not allowed\",\n        \"value\": \"18\"\n    },\n    {\n        \"label\": \"321 UCP - New AC not valid\",\n        \"value\": \"19\"\n    },\n    {\n        \"label\": \"322 UCP - New legitimisation code not valid\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"323 UCP - Standard text not valid\",\n        \"value\": \"21\"\n    },\n    {\n        \"label\": \"324 UCP - Time period not valid\",\n        \"value\": \"22\"\n    },\n    {\n        \"label\": \"325 UCP - Message type not supported by system\",\n        \"value\": \"23\"\n    },\n    {\n        \"label\": \"326 UCP - Message too long\",\n        \"value\": \"24\"\n    },\n    {\n        \"label\": \"327 UCP - Requested standard text not valid\",\n        \"value\": \"25\"\n    },\n    {\n        \"label\": \"328 UCP - Message type not valid for the pager type\",\n        \"value\": \"26\"\n    },\n    {\n        \"label\": \"329 UCP - Message not found in smsc\",\n        \"value\": \"27\"\n    },\n    {\n        \"label\": \"330 UCP - Subscriber hang-up\",\n        \"value\": \"30\"\n    },\n    {\n        \"label\": \"331 UCP - Fax group not supported\",\n        \"value\": \"31\"\n    },\n    {\n        \"label\": \"332 UCP - Fax message type not supported\",\n        \"value\": \"32\"\n    },\n    {\n        \"label\": \"333 UCP - Address already in list\",\n        \"value\": \"33\"\n    },\n    {\n        \"label\": \"334 UCP - Address not in list\",\n        \"value\": \"34\"\n    },\n    {\n        \"label\": \"335 UCP - List full, cannot add address to list\",\n        \"value\": \"35\"\n    },\n    {\n        \"label\": \"336 UCP - RPID already in use\",\n        \"value\": \"36\"\n    },\n    {\n        \"label\": \"337 UCP - Delivery in progress\",\n        \"value\": \"37\"\n    },\n    {\n        \"label\": \"338 UCP - Message forwarded\",\n        \"value\": \"38\"\n    },\n    {\n        \"label\": \"339 SMPP - Response Timeout\",\n        \"value\": \"-2\"\n    },\n    {\n        \"label\": \"340 CIMD - Response Timeout\",\n        \"value\": \"-2\"\n    },\n    {\n        \"label\": \"341 UCP - Response Timeout\",\n        \"value\": \"-2\"\n    },\n    {\n        \"label\": \"342 ESME Account Not Connected\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"343 UCP - Redirection Delivery Failed\",\n        \"value\": \"-3\"\n    },\n    {\n        \"label\": \"344 CIMD - Redirection Delivery Failed\",\n        \"value\": \"-3\"\n    },\n    {\n        \"label\": \"345 SMPP - Redirection Delivery Failed\",\n        \"value\": \"-3\"\n    },\n    {\n        \"label\": \"346 AS Send Fail\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"347 AS iError Resp\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"348 AS NO Response\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"349 SMSC Not Connected\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"350 Queue Full Or Throttle Error\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"5118 Account Blackout Time based\",\n        \"value\": \"5118\"\n    },\n    {\n        \"label\": \"5117 Source VMSC Destination SC Barring\",\n        \"value\": \"5117\"\n    },\n    {\n        \"label\": \"5116 Source IMSI Destination SC Barring\",\n        \"value\": \"5116\"\n    },\n    {\n        \"label\": \"5113 Source VMSC Source IMSI Barring\",\n        \"value\": \"5113\"\n    },\n    {\n        \"label\": \"5100 Source MSISDN and Deatination Shortcode Barred\",\n        \"value\": \"5100\"\n    },\n    {\n        \"label\": \"5099 Spoofed Message Error\",\n        \"value\": \"5099\"\n    },\n    {\n        \"label\": \"5085 Source IMSI Barred\",\n        \"value\": \"5085\"\n    },\n    {\n        \"label\": \"5076 UDH Barred\",\n        \"value\": \"5076\"\n    },\n    {\n        \"label\": \"5075 Source VMSE Barred\",\n        \"value\": \"5075\"\n    },\n    {\n        \"label\": \"5071 Source Destination MSISDN Barred\",\n        \"value\": \"5071\"\n    },\n    {\n        \"label\": \"5063 Multipart Messaged Barred\",\n        \"value\": \"5063\"\n    },\n    {\n        \"label\": \"5060 PID Barred\",\n        \"value\": \"5060\"\n    },\n    {\n        \"label\": \"5056 Account Black Listed\",\n        \"value\": \"5056\"\n    },\n    {\n        \"label\": \"5051 Destination MSISDN Barred\",\n        \"value\": \"5051\"\n    },\n    {\n        \"label\": \"5050 Source MSISDN Barred\",\n        \"value\": \"5050\"\n    },\n    {\n        \"label\": \"5012 DBill Failure Error\",\n        \"value\": \"5012\"\n    },\n    {\n        \"label\": \"5011 Message Forward Failure Error\",\n        \"value\": \"5011\"\n    },\n    {\n        \"label\": \"5010 Transaction Failure Error\",\n        \"value\": \"5010\"\n    },\n    {\n        \"label\": \"5009 Invalid Multipart Errorr\",\n        \"value\": \"5009\"\n    },\n    {\n        \"label\": \"5008 Rejected International TON For Shortcode Error\",\n        \"value\": \"5008\"\n    },\n    {\n        \"label\": \"5007 Unknown TON or NPI Error\",\n        \"value\": \"5007\"\n    },\n    {\n        \"label\": \"5006 Drop or Check Message Type Failed Error\",\n        \"value\": \"5006\"\n    },\n    {\n        \"label\": \"5005 Address Translation Failed Error\",\n        \"value\": \"5005\"\n    },\n    {\n        \"label\": \"5004 Barred For Destination Length Error\",\n        \"value\": \"5004\"\n    },\n    {\n        \"label\": \"5003 Invalid Destination Address Error\",\n        \"value\": \"5003\"\n    },\n    {\n        \"label\": \"5002 License Capacity Exceded Error\",\n        \"value\": \"5002\"\n    },\n    {\n        \"label\": \"5001 MO Validation Failed Error\",\n        \"value\": \"5001\"\n    },\n    {\n        \"label\": \"351 As Subscriber Not Found\",\n        \"value\": \"100\"\n    },\n    {\n        \"label\": \"371 Sig Error Internal resource not available\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"372 Sig Error MAF create pdu failed\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"373 Sig Error SEncode function fails\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"374 Sig Error maf get pdu param for error info failed\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"375 Sig Error Internal Timeout\",\n        \"value\": \"7\"\n    },\n    {\n        \"label\": \"375 Sig Error Open Request failued from Maf to ccpu\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"380 Sig Error Map Invalid Response from peer\",\n        \"value\": \"23\"\n    },\n    {\n        \"label\": \"381 Sig Error Map Dialogue Does not Exist\",\n        \"value\": \"24\"\n    },\n    {\n        \"label\": \"382 Sig Error Map Max evoke reached\",\n        \"value\": \"25\"\n    },\n    {\n        \"label\": \"383 Sig Error Map guard Timer Expired\",\n        \"value\": \"26\"\n    },\n    {\n        \"label\": \"384 Sig Error Map SPCAUSE mask\",\n        \"value\": \"27\"\n    },\n    {\n        \"label\": \"385 Sig Error MAP No Translation Basd Address\",\n        \"value\": \"28\"\n    },\n    {\n        \"label\": \"386 Sig Error MAP No Translation specific address\",\n        \"value\": \"29\"\n    },\n    {\n        \"label\": \"387 Sig Error MAP Subsystem congested\",\n        \"value\": \"40\"\n    },\n    {\n        \"label\": \"388 Sig Error MAP Subsystem failure\",\n        \"value\": \"41\"\n    },\n    {\n        \"label\": \"389 Sig Error MAP Un Equipped User\",\n        \"value\": \"42\"\n    },\n    {\n        \"label\": \"390 Sig Error MAP Network Failure\",\n        \"value\": \"43\"\n    },\n    {\n        \"label\": \"391 Sig Error MAP Network Conjestion\",\n        \"value\": \"44\"\n    },\n    {\n        \"label\": \"392 Sig Error MAP un Qualified\",\n        \"value\": \"45\"\n    },\n    {\n        \"label\": \"393 Sig Error MAP HOP Counter violaion ANS92\",\n        \"value\": \"46\"\n    },\n    {\n        \"label\": \"394 Sig Error MAP Error in message transport CCIT92\",\n        \"value\": \"47\"\n    },\n    {\n        \"label\": \"395 Sig Error MAP Error in local processing CCIT92\",\n        \"value\": \"48\"\n    },\n    {\n        \"label\": \"396 Sig Error MAP Re assembly failure\",\n        \"value\": \"49\"\n    },\n    {\n        \"label\": \"397 Sig Error MAP SCCP failure\",\n        \"value\": \"50\"\n    },\n    {\n        \"label\": \"398 Sig Error MAP HOP counter violation ITU\",\n        \"value\": \"51\"\n    },\n    {\n        \"label\": \"399 Sig Error MAP segmentation not supported\",\n        \"value\": \"52\"\n    },\n    {\n        \"label\": \"400 Sig Error MAP Segmentation failure\",\n        \"value\": \"53\"\n    },\n    {\n        \"label\": \"352 SMPP - Account not connected\",\n        \"value\": \"60\"\n    },\n    {\n        \"label\": \"353 CIMD - Account not connected\",\n        \"value\": \"60\"\n    },\n    {\n        \"label\": \"354 UCP - Account not connected\",\n        \"value\": \"60\"\n    },\n    {\n        \"label\": \"355 SMPP - Account Based List Full\",\n        \"value\": \"61\"\n    },\n    {\n        \"label\": \"356 CIMD - Account Based List Full\",\n        \"value\": \"61\"\n    },\n    {\n        \"label\": \"357 UCP - Account Based List Full\",\n        \"value\": \"61\"\n    },\n    {\n        \"label\": \"9996 Insufficient Balance MT\",\n        \"value\": \"5119\"\n    },\n    {\n        \"label\": \"9997 Insufficient Balance MT\",\n        \"value\": \"5200\"\n    },\n    {\n        \"label\": \"9998 Insufficient Balance AO\",\n        \"value\": \"5201\"\n    },\n    {\n        \"label\": \"9999 Insufficient Balance MO\",\n        \"value\": \"5202\"\n    },\n    {\n        \"label\": \"5013 MO Abort\",\n        \"value\": \"5013\"\n    },\n    {\n        \"label\": \"5014 Message Spammed\",\n        \"value\": \"5014\"\n    },\n    {\n        \"label\": \"5058 SMSP Barred\",\n        \"value\": \"5058\"\n    },\n    {\n        \"label\": \"9995 Content Filtering Error\",\n        \"value\": \"5000\"\n    },\n    {\n        \"label\": \"21101 Src IMSI Barred\",\n        \"value\": \"21101\"\n    },\n    {\n        \"label\": \"21102 Src MSISDN Barred\",\n        \"value\": \"21102\"\n    },\n    {\n        \"label\": \"21104 Src VMSC Barred\",\n        \"value\": \"21104\"\n    },\n    {\n        \"label\": \"21105 Src ShortCode Barred\",\n        \"value\": \"21105\"\n    },\n    {\n        \"label\": \"21106 Dest IMSI Barred\",\n        \"value\": \"21106\"\n    },\n    {\n        \"label\": \"21107 Dest MSISDN Barred\",\n        \"value\": \"21107\"\n    },\n    {\n        \"label\": \"21109 Dest VMSC Barred\",\n        \"value\": \"21109\"\n    },\n    {\n        \"label\": \"21110 Dest ShortCode Barred\",\n        \"value\": \"21110\"\n    },\n    {\n        \"label\": \"21113 PID Barred\",\n        \"value\": \"21113\"\n    },\n    {\n        \"label\": \"21114 AlphaNumeric Barred\",\n        \"value\": \"21114\"\n    },\n    {\n        \"label\": \"21115 System ID Barred\",\n        \"value\": \"21115\"\n    },\n    {\n        \"label\": \"21116 Src Cell ID Barred\",\n        \"value\": \"21116\"\n    },\n    {\n        \"label\": \"21117 Dest Cell ID Barred\",\n        \"value\": \"21117\"\n    },\n    {\n        \"label\": \"21118 Source Point Code Barred\",\n        \"value\": \"21118\"\n    },\n    {\n        \"label\": \"21122 SC Adress Barred\",\n        \"value\": \"21122\"\n    },\n    {\n        \"label\": \"21123 SCLP Address Barred\",\n        \"value\": \"21123\"\n    },\n    {\n        \"label\": \"21124 SCDP Address Barred\",\n        \"value\": \"21124\"\n    },\n    {\n        \"label\": \"21125 DCS Barred\",\n        \"value\": \"21125\"\n    },\n    {\n        \"label\": \"22101 Src IMSI-Src MSISDN Barred\",\n        \"value\": \"22101\"\n    },\n    {\n        \"label\": \"22102 Src IMSI-Dest MSISDN Barred\",\n        \"value\": \"22102\"\n    },\n    {\n        \"label\": \"22103 Src IMSI-Src VMSC Barred\",\n        \"value\": \"22103\"\n    },\n    {\n        \"label\": \"22104 Src VMSC-Src MSISDN Barred\",\n        \"value\": \"22104\"\n    },\n    {\n        \"label\": \"22105 Src VMSC-Dest MSISDN Barred\",\n        \"value\": \"22105\"\n    },\n    {\n        \"label\": \"22106 Src MSISDN-Dest MSISDN Barred\",\n        \"value\": \"22106\"\n    },\n    {\n        \"label\": \"22107 Dest IMSI-Dest VMSC Barred\",\n        \"value\": \"22107\"\n    },\n    {\n        \"label\": \"22108 System ID-Dest MSISDN Barred\",\n        \"value\": \"22108\"\n    },\n    {\n        \"label\": \"22109 System ID-Dest IMSI Barred\",\n        \"value\": \"22109\"\n    },\n    {\n        \"label\": \"22110 System ID-Dest VMSC Barred\",\n        \"value\": \"22110\"\n    },\n    {\n        \"label\": \"22111 Src ShortCode-Dest MSISDN Barred\",\n        \"value\": \"22111\"\n    },\n    {\n        \"label\": \"22112 Src ShortCode-Dest IMSI Barred\",\n        \"value\": \"22112\"\n    },\n    {\n        \"label\": \"22113 Src ShortCode-Dest VMSC Barred\",\n        \"value\": \"22113\"\n    },\n    {\n        \"label\": \"22114 Src IMSI-Dest ShortCode Barred\",\n        \"value\": \"22114\"\n    },\n    {\n        \"label\": \"22115 Src VMSC-Dest ShortCode Barred\",\n        \"value\": \"22115\"\n    },\n    {\n        \"label\": \"22116 Src MSISDN-Dest ShortCode Barred\",\n        \"value\": \"22116\"\n    },\n    {\n        \"label\": \"22117 Src ShortCode-Dest ShortCode Barred\",\n        \"value\": \"22117\"\n    },\n    {\n        \"label\": \"22118 Src MSISDN-System ID Barred\",\n        \"value\": \"22118\"\n    },\n    {\n        \"label\": \"22119 Src IMSI-System ID Barred\",\n        \"value\": \"22119\"\n    },\n    {\n        \"label\": \"22120 Src VMSC-System ID Barred\",\n        \"value\": \"22120\"\n    },\n    {\n        \"label\": \"22121 Source PC-Dest MSISDN Barred\",\n        \"value\": \"22121\"\n    },\n    {\n        \"label\": \"22122 Source PC-Src MSISDN Barred\",\n        \"value\": \"22122\"\n    },\n    {\n        \"label\": \"22123 Source PC-Src IMSI Barred\",\n        \"value\": \"22123\"\n    },\n    {\n        \"label\": \"22124 Source PC-Src VMSC Barred\",\n        \"value\": \"22124\"\n    },\n    {\n        \"label\": \"22125 Source PC-Dest IMSI Barred\",\n        \"value\": \"22125\"\n    },\n    {\n        \"label\": \"22126 Source PC-Dest VMSC Barred\",\n        \"value\": \"22126\"\n    },\n    {\n        \"label\": \"22127 SC Address-Src MSISDN Barred\",\n        \"value\": \"22127\"\n    },\n    {\n        \"label\": \"22128 SC Address-Dest MSISDN Barred\",\n        \"value\": \"22128\"\n    },\n    {\n        \"label\": \"22129 SC Address-Src IMSI Barred\",\n        \"value\": \"22129\"\n    },\n    {\n        \"label\": \"22130 SC Address-Src VMSC Barred\",\n        \"value\": \"22130\"\n    },\n    {\n        \"label\": \"22131 SC Address-SCLP Barred\",\n        \"value\": \"22131\"\n    },\n    {\n        \"label\": \"22132 SC Address-Dest Short Code Barred\",\n        \"value\": \"22132\"\n    },\n    {\n        \"label\": \"22133 SC Address-System ID Barred\",\n        \"value\": \"22133\"\n    },\n    {\n        \"label\": \"22134 Src PC-SC Address Barred\",\n        \"value\": \"22134\"\n    },\n    {\n        \"label\": \"22135 SCLP ADDRESS-Src MSISDN Barred\",\n        \"value\": \"22135\"\n    },\n    {\n        \"label\": \"22136 SCLP ADDRESS-Dest MSISDN Barred\",\n        \"value\": \"22136\"\n    },\n    {\n        \"label\": \"22137 SCLP ADDRESS-Src IMSI Barred\",\n        \"value\": \"22137\"\n    },\n    {\n        \"label\": \"22138 SCLP ADDRESS-Src VMSC Barred\",\n        \"value\": \"22138\"\n    },\n    {\n        \"label\": \"22139 SCLP Address-Dest Short Code Barred\",\n        \"value\": \"22139\"\n    },\n    {\n        \"label\": \"22140 SCLP Address-System ID Barred\",\n        \"value\": \"22140\"\n    },\n    {\n        \"label\": \"22141 Src PC-SCLP Address Barred\",\n        \"value\": \"22141\"\n    },\n    {\n        \"label\": \"22142 Source MSISDN-Destination VMSC Barred\",\n        \"value\": \"22142\"\n    },\n    {\n        \"label\": \"22143 Source MSISDN-Destination IMSI Barred\",\n        \"value\": \"22143\"\n    },\n    {\n        \"label\": \"22144 SC Address-Destination IMSI Barred\",\n        \"value\": \"22144\"\n    },\n    {\n        \"label\": \"22145 SC Address-Destination VMSC Barred\",\n        \"value\": \"22145\"\n    },\n    {\n        \"label\": \"22146 SCLP Address-Destination VMSC Barred\",\n        \"value\": \"22146\"\n    },\n    {\n        \"label\": \"22147 SCLP Address-Destination IMSI Barred\",\n        \"value\": \"22147\"\n    },\n    {\n        \"label\": \"22148 SC Addr-Src Shortcode Barred\",\n        \"value\": \"22148\"\n    },\n    {\n        \"label\": \"22149 SCLP-Src Shortcode Barred\",\n        \"value\": \"22149\"\n    },\n    {\n        \"label\": \"22150 Alphanumeric-Dest MSISDN Barred\",\n        \"value\": \"22150\"\n    },\n    {\n        \"label\": \"22151 Alphanumeric-Dest IMSI Barred\",\n        \"value\": \"22151\"\n    },\n    {\n        \"label\": \"22152 Alphanumeric-Dest VMSC Barred\",\n        \"value\": \"22152\"\n    },\n    {\n        \"label\": \"22153 Alphanumeric-SCLP Barred\",\n        \"value\": \"22153\"\n    },\n    {\n        \"label\": \"22154 Alphanumeric-SC Address Barred\",\n        \"value\": \"22154\"\n    },\n    {\n        \"label\": \"22181 System ID-Dest IMSI-Dest VMSC Barred\",\n        \"value\": \"22181\"\n    },\n    {\n        \"label\": \"22182 Src ShortCode-Dest IMSI-Dest VMSC Barred\",\n        \"value\": \"22182\"\n    },\n    {\n        \"label\": \"4999 CMA Failure\",\n        \"value\": \"4999\"\n    },\n    {\n        \"label\": \"5015 Content Failure\",\n        \"value\": \"5015\"\n    },\n    {\n        \"label\": \"358 SMPP - Generic Error\",\n        \"value\": \"117\"\n    },\n    {\n        \"label\": \"199 SMPP - DOP Maximum Message Limit Reached\",\n        \"value\": \"1072\"\n    },\n    {\n        \"label\": \"500 Internal Error - DOP Max Message Limit Reached\",\n        \"value\": \"1\"\n    },\n    {\n        \"label\": \"501 Internal Error - Hub Route Not Found\",\n        \"value\": \"2\"\n    },\n    {\n        \"label\": \"21126 DOP Barred\",\n        \"value\": \"21126\"\n    },\n    {\n        \"label\": \"21127 SOP Barred\",\n        \"value\": \"21127\"\n    },\n    {\n        \"label\": \"21128 Customer Barred\",\n        \"value\": \"21128\"\n    },\n    {\n        \"label\": \"21129 Supplier Barred\",\n        \"value\": \"21129\"\n    },\n    {\n        \"label\": \"22165 SOP-DOP Barred\",\n        \"value\": \"22165\"\n    },\n    {\n        \"label\": \"22166 Customer-SOP Barred\",\n        \"value\": \"22166\"\n    },\n    {\n        \"label\": \"22167 Customer-SrcAlnum Barred\",\n        \"value\": \"22167\"\n    },\n    {\n        \"label\": \"22168 Customer-DOP Barred\",\n        \"value\": \"22168\"\n    },\n    {\n        \"label\": \"22169 SrcAlnum-Supplier Barred\",\n        \"value\": \"22169\"\n    },\n    {\n        \"label\": \"22170 SCLP Addr-Customer Barred\",\n        \"value\": \"22170\"\n    },\n    {\n        \"label\": \"22171 SC Addr-Customer Barred\",\n        \"value\": \"22171\"\n    },\n    {\n        \"label\": \"22172 SC Addr-SOP Barred\",\n        \"value\": \"22172\"\n    },\n    {\n        \"label\": \"22173 SCLP Addr-SOP Barred\",\n        \"value\": \"22173\"\n    },\n    {\n        \"label\": \"22174 SrcAlnum-DOP Barred\",\n        \"value\": \"22174\"\n    },\n    {\n        \"label\": \"407 SM Delivery Fail-Memory Capacity Exceeded\",\n        \"value\": \"32\"\n    },\n    {\n        \"label\": \"408 Absent Subscriber For SM - IMSI Detach\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"409 Absent Subscriber For SM - Restricted Area\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"410 Absent Subscriber For SM - No Page Response\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"411 Absent Subscriber For SM - Purge MS\",\n        \"value\": \"6\"\n    },\n    {\n        \"label\": \"0 Success\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"22184 DOP not found\",\n        \"value\": \"23\"\n    },\n    {\n        \"label\": \"22183 Route Not Found\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"502 Route Not Found\",\n        \"value\": \"20\"\n    },\n    {\n        \"label\": \"503 DOP not found\",\n        \"value\": \"23\"\n    },\n    {\n        \"label\": \"504 Test Quota Exceeded\",\n        \"value\": \"3\"\n    },\n    {\n        \"label\": \"505 MNP Failure\",\n        \"value\": \"19\"\n    },\n    {\n        \"label\": \"506 DND Timeout\",\n        \"value\": \"24\"\n    },\n    {\n        \"label\": \"507 MNP Timeout\",\n        \"value\": \"22\"\n    },\n    {\n        \"label\": \"508 DND Blocked Error\",\n        \"value\": \"25\"\n    },\n    {\n        \"label\": \"513 Internal error - Mars Barred with Nack\",\n        \"value\": \"185\"\n    },\n    {\n        \"label\": \"512 Internal error - Mars Barred with Ack\",\n        \"value\": \"186\"\n    },\n    {\n        \"label\": \"515 Internal error - Mars check fail\",\n        \"value\": \"187\"\n    },\n    {\n        \"label\": \"511 Internal error - Mars Throttle block\",\n        \"value\": \"188\"\n    },\n    {\n        \"label\": \"514 Internal error - Mars Timeout\",\n        \"value\": \"198\"\n    },\n    {\n        \"label\": \"516 Internal error - HLR Temporary error\",\n        \"value\": \"168\"\n    },\n    {\n        \"label\": \"517 Internal error - HLR Permanent error\",\n        \"value\": \"167\"\n    },\n    {\n        \"label\": \"518 Internal error - HLR Timeout\",\n        \"value\": \"170\"\n    },\n    {\n        \"label\": \"519 Internal error - HLR Check fail\",\n        \"value\": \"169\"\n    },\n    {\n        \"label\": \"520 Internal error - Mars Cafe check fail\",\n        \"value\": \"196\"\n    },\n    {\n        \"label\": \"413 MAP P ABORT\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"414 Stack/Sig Error Map Unrecognised Transaction ID\",\n        \"value\": \"11\"\n    },\n    {\n        \"label\": \"415 MTP Failure\",\n        \"value\": \"4\"\n    },\n    {\n        \"label\": \"416 SM Delivery Fail - Equipment Protocol Err\",\n        \"value\": \"32\"\n    },\n    {\n        \"label\": \"417 MAP P Abort - SUPPORTING_DIALOG_TRAN_RELEASED\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"418 MAP P Abort - DLG_RESOURCE_LIMITATION\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"419 MAP P Abort - MAINTENANCE_ACTIVITY\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"420 MAP P Abort - PROVIDER_MALFUNCTION\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"421 MAP P Abort\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"422 MAP P Abort - VERSION_INCOMPATIBILITY\",\n        \"value\": \"0\"\n    },\n    {\n        \"label\": \"521 Internal error - Sender Content Request Not approved\",\n        \"value\": \"165\"\n    },\n    {\n        \"label\": \"531 DBill HTTP Error - Service Unavailable\",\n        \"value\": \"503\"\n    },\n    {\n        \"label\": \"530 DBill HTTP Error - Internet Server Error\",\n        \"value\": \"500\"\n    },\n    {\n        \"label\": \"529 DBill HTTP Error - Invalid Input\",\n        \"value\": \"405\"\n    },\n    {\n        \"label\": \"528 DBill HTTP Error - Forbidden\",\n        \"value\": \"403\"\n    },\n    {\n        \"label\": \"527 DBill HTTP Error - Unauthorized\",\n        \"value\": \"401\"\n    },\n    {\n        \"label\": \"526 DBill HTTP Error - Mandatory Fields Missing\",\n        \"value\": \"400\"\n    },\n    {\n        \"label\": \"525 Internal Error - Dbill time out at MARS side\",\n        \"value\": \"10006\"\n    },\n    {\n        \"label\": \"524 Internal Error - Rate sheet not found for MCC MNC\",\n        \"value\": \"200\"\n    },\n    {\n        \"label\": \"523 Internal Error - MARS timeout at application side\",\n        \"value\": \"198\"\n    },\n    {\n        \"label\": \"522 Internal Error - DBill Down\",\n        \"value\": \"110\"\n    }\n]"}]}, {"name": "node-list", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3NmQ3MThmNy00MGYxLTRlNTMtODliMC03YjhkMmE2YzYwMzgiLCJ0aW1lem9uZSI6IkFzaWEvS29sa2F0YSIsImlhdCI6MTczNzcwNDc4MCwiZXhwIjoxNzM3NzA2NTgwLCJ0eXBlIjoiYWNjZXNzIn0.SHsaxSjZATTQchyH8DGeFfB6j2kDhBlP-yFG8TEc7pQ", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{localhost}}/v1/alert/node-list", "host": ["{{localhost}}"], "path": ["v1", "alert", "node-list"]}}, "response": [{"name": "node-list", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{localhost}}/v1/alert/node-list", "host": ["{{localhost}}"], "path": ["v1", "alert", "node-list"]}}, "_postman_previewlanguage": null, "header": null, "cookie": [], "body": null}]}]}, {"name": "grafana-webhook", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:9697/v1/grafana-webhook", "protocol": "http", "host": ["localhost"], "port": "9697", "path": ["v1", "grafana-webhook"]}}, "response": []}, {"name": "grafana-webhook dev", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"test\": \"hello\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://smshub.openturf.dev/smshub_dev/v1/grafana-webhook", "protocol": "https", "host": ["smshub", "openturf", "dev"], "path": ["smshub_dev", "v1", "grafana-webhook"]}}, "response": []}, {"name": "auth/login", "event": [{"listen": "test", "script": {"exec": ["pm.environment.set(\"smshub_AT\", pm.response.json().token.token);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Password@123\",\r\n    \"timezone\": \"Asia/Kolkata\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/v1/auth/login", "host": ["{{host}}"], "path": ["v1", "auth", "login"]}}, "response": []}, {"name": "users/config", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkNjM0ZjA3Ni1mY2FlLTRlNzktOGQ2YS1lYjkyYzFiMWIxOWYiLCJ0aW1lem9uZSI6IkFzaWEvS29sa2F0YSIsImlhdCI6MTczNjMzMDYwOSwiZXhwIjoxNzM2MzQ4NjA5LCJ0eXBlIjoiYWNjZXNzIn0.lzVBMhXoHEsfwZgfZI-mg8slCYSqblUO0J0XpwT_UF0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://smshub.openturf.dev/smshub_be/v1/users/config", "protocol": "https", "host": ["smshub", "openturf", "dev"], "path": ["smshub_be", "v1", "users", "config"]}}, "response": []}, {"name": "Alert notification", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{smshub_AT}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{host}}/v1/alert/notifications", "host": ["{{host}}"], "path": ["v1", "alert", "notifications"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "alert rule", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/v1/provisioning/alert-rules/45", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "v1", "provisioning", "alert-rules", "45"]}}, "response": []}, {"name": "all rules", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [{"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "graphql", "graphql": {"query": "", "variables": ""}}, "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/v1/provisioning/alert-rules", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "v1", "provisioning", "alert-rules"]}}, "response": []}, {"name": "Alert history", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [{"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/alerts", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "alerts"], "query": [{"key": "state", "value": "Alerting", "disabled": true}, {"key": "from", "value": "1704067200000", "disabled": true}, {"key": "to", "value": "1736121600000", "disabled": true}]}}, "response": []}, {"name": "folders", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [{"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "graphql", "graphql": {"query": "", "variables": ""}}, "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/folders", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "folders"]}}, "response": []}, {"name": "datasources", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/datasources", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "datasources"]}}, "response": []}, {"name": "annotations", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/annotations?alertId=103", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "annotations"], "query": [{"key": "alertId", "value": "103"}]}}, "response": []}, {"name": "contact points", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/alert-notifications", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "alert-notifications"]}}, "response": []}, {"name": "contact points policies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/v1/provisioning/policies", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "v1", "provisioning", "policies"]}}, "response": []}, {"name": "contact points policies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://hub-release.eastus.cloudapp.azure.com:3000/api/alerting/policies/:policyId", "protocol": "http", "host": ["hub-release", "eastus", "cloudapp", "azure", "com"], "port": "3000", "path": ["api", "alerting", "policies", ":policyId"], "variable": [{"key": "policyId", "value": "ed87d10d-1026-48ed-a78f-7be3b65475e4"}]}}, "response": []}]}, {"name": "GraphQL", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "DeliveryReportDrop-Customer Supplier", "event": [{"listen": "test", "script": {"exec": ["// 7 - 7 days\r", "// 1 - one day\r", "pm.collectionVariables.set(\"time_period\", 7); "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "    mutation {\r\n      alertCreate(\r\n        comparedTo: { timediff: 1, timerange: 1, aggregate_function: \"sum\" }\r\n        contactPoint: \"operations_team\"\r\n        evaluationTimeframe: 60\r\n        fields: [\"customer_name\", \"supplier\", \"total_submitted\"]\r\n        name: \"Submitted Drop V2\"\r\n        runEvery: 15\r\n        timezone: \"Asia/Kolkata\"\r\n        triggeringExpressions: [\r\n          {\r\n            name: \"percentage_change\"\r\n            type: \"math\"\r\n            expression: \"abs({total_submitted:current} - {total_submitted:previous})/{total_submitted:previous} * 100\"\r\n          }\r\n          {\r\n            name: \"trigger\"\r\n            type: \"math\"\r\n            expression: \"{percentage_change} > 20\"\r\n          }\r\n        ]\r\n        commonFilter: \"{customer_name} = 'Wirelsc_C_P2P' AND {supplier} = 'Airtel_Africa'\"\r\n        currentFilter: null\r\n        comparedFilter: null\r\n        metadata: { max_allowed_percentage_change: \"20%\" }\r\n      ) {\r\n        common_filter\r\n        compared_filter\r\n        compared_to {\r\n          aggregate_function\r\n          timediff\r\n          timerange\r\n        }\r\n        contact_point\r\n        current_filter\r\n        evaluation_timeframe\r\n        fields\r\n        id\r\n        metadata\r\n        name\r\n        run_every\r\n        timezone\r\n        triggering_expression\r\n      }\r\n    }", "variables": "{\r\n    \"//timediff - time period\": \"Yesterday,Last 3 days,Last 5 days,last weekdays\",\r\n    \"timediff\": 1,\r\n    \"//runEvery\": \"15 mins, 30 mins, 45 min, 60 mins\",\r\n    \"runEvery\": 15,\r\n    \"//evaluationTimeframe\": \"system will evaluate the data collected in the last 60 minutes at the time of each check.\",\r\n    \"evaluationTimeframe\": 60,\r\n    \"// timerange\": \"group of previous days\",\r\n    \"timerange\": 1\r\n}"}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "DeliveryReportDrop-Customer Supplier Copy", "event": [{"listen": "test", "script": {"exec": ["// 7 - 7 days\r", "// 1 - one day\r", "pm.collectionVariables.set(\"time_period\", 7); "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "    mutation {\r\n      alertCreate(\r\n        comparedTo: { timediff: 1, timerange: 1, aggregate_function: \"sum\" }\r\n        contactPoint: \"dev-team\"\r\n        evaluationTimeframe: 60\r\n        fields: [\"customer_name\", \"total_submitted\"]\r\n        name: \"2-customers\"\r\n        runEvery: 15\r\n        timezone: \"Asia/Kolkata\"\r\n        triggeringExpressions: [\r\n          {\r\n            name: \"percentage_change\"\r\n            type: \"math\"\r\n            expression: \"abs({total_submitted:current} - ({total_submitted:previous} || 0)) / ({total_submitted:previous} || 1) * 100\"\r\n\r\n          }\r\n          {\r\n            name: \"trigger\"\r\n            type: \"math\"\r\n            expression: \"{percentage_change} > 1\"\r\n          }\r\n        ]\r\n        commonFilter: \"{customer_name} in ('Wirelsc_C_P2P','Airtel_Srilanka_P2P_Out')\"\r\n        currentFilter: null\r\n        comparedFilter: null\r\n        metadata: { max_allowed_percentage_change: \"1%\" }\r\n      ) {\r\n        common_filter\r\n        compared_filter\r\n        compared_to {\r\n          aggregate_function\r\n          timediff\r\n          timerange\r\n        }\r\n        contact_point\r\n        current_filter\r\n        evaluation_timeframe\r\n        fields\r\n        id\r\n        metadata\r\n        name\r\n        run_every\r\n        timezone\r\n        triggering_expression\r\n      }\r\n    }", "variables": "{\r\n    \"//timediff - time period\": \"Yesterday,Last 3 days,Last 5 days,last weekdays\",\r\n    \"timediff\": 1,\r\n    \"//runEvery\": \"15 mins, 30 mins, 45 min, 60 mins\",\r\n    \"runEvery\": 15,\r\n    \"//evaluationTimeframe\": \"system will evaluate the data collected in the last 60 minutes at the time of each check.\",\r\n    \"evaluationTimeframe\":60\r\n}"}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "DeliveryReportDrop-Customer Destination", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "    mutation {\r\n      alertCreate(\r\n        comparedTo: { timediff: 7, timerange: 1, aggregate_function: \"sum\" }\r\n        contactPoint: \"operations_team\"\r\n        evaluationTimeframe: 60\r\n        fields: [\"customer_name\", \"destination\", \"total_submitted\"]\r\n        name: \"Submitted Delivery report drop\"\r\n        runEvery: 15\r\n        timezone: \"Asia/Kolkata\"\r\n        triggeringExpressions: [\r\n          {\r\n            name: \"percentage_change\"\r\n            type: \"math\"\r\n            expression: \"abs({total_submitted:current} - {total_submitted:previous})/{total_submitted:previous} * 100\"\r\n          }\r\n          {\r\n            name: \"trigger\"\r\n            type: \"math\"\r\n            expression: \"{percentage_change} > 20\"\r\n          }\r\n        ]\r\n        commonFilter: \"{customer_name} = 'Wirelsc_C_P2P' AND {destination} = 'Airtel Kenya'\"\r\n        currentFilter: null\r\n        comparedFilter: null\r\n        metadata: { max_allowed_percentage_change: \"20%\" }\r\n      ) {\r\n        common_filter\r\n        compared_filter\r\n        compared_to {\r\n          aggregate_function\r\n          timediff\r\n          timerange\r\n        }\r\n        contact_point\r\n        current_filter\r\n        evaluation_timeframe\r\n        fields\r\n        id\r\n        metadata\r\n        name\r\n        run_every\r\n        timezone\r\n        triggering_expression\r\n      }\r\n    }", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "Delete alert Rule by id", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation MyMutation {\r\n  alertDelete(alertId: 85)\r\n}", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}]}, {"name": "Contact-point", "item": [{"name": "get-contact-point", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query MyQuery {\r\n  getContactPoints {\r\n    description\r\n    emails\r\n    id\r\n    name\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "update-contact-point", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation MyMutation {\r\n  updateContactPoint(\r\n    emails: \"<EMAIL>,<EMAIL>\"\r\n    id: \"aef076df-bc3d-4731-a935-a4914be6fc1b\"\r\n  ) {\r\n    description\r\n    emails\r\n    id\r\n    name\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "delete-contact-point", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "glsa_1S3huAr1OhWWjrL1BYoT8uRJykSwrDQw_8c6957fd", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation MyMutation {\r\n  deleteContactPoint(name: \"webhook\")\r\n}", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "Create-contact-points", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation MyMutation {\r\n      createContactPoint(\r\n        name: \"webhook\"\r\n        description: \"Contains webhook url\"\r\n        emails: \"<EMAIL>\"\r\n        type: \"webhook\"\r\n        url: \"https://smshub.openturf.dev/smshub_dev/v1/grafana-webhook\"\r\n      ) {\r\n        description\r\n        emails\r\n        id\r\n      }\r\n    }\r\n", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}]}, {"name": "fetchRawCdr", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query MyQuery {\r\n  fetchRawCdr(startTime: \"2024-03-30 19:00:00\",endTime: \"2024-03-30 19:00:05\" ) {\r\n    items {\r\n      customer_name\r\n      status\r\n      a_number\r\n      b_number\r\n      destination\r\n      destination_country_name\r\n    #   destination_mcc_mnc\r\n    #   error_code\r\n      event_date\r\n      event_id\r\n      lcr_name\r\n      result_code\r\n      source_protocol\r\n      supplier\r\n      time_of_arrival\r\n      time_of_delivery\r\n      transaction_type\r\n      visiting_operator\r\n    }\r\n    page_meta {\r\n      current_page\r\n      last_page\r\n      page_size\r\n      total_items\r\n    }\r\n  }\r\n}\r\n\r\n", "variables": ""}}, "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}, {"name": "Strawberry UI", "request": {"method": "GET", "header": [], "url": {"raw": "http://***************:8000/graphql", "protocol": "http", "host": ["172", "190", "137", "244"], "port": "8000", "path": ["graphql"]}}, "response": []}]}], "variable": [{"key": "time_period", "value": ""}]}