{"name": "SMShub_report", "version": "1.0.0", "description": "SMShub reporting", "main": "src/index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"start": "pm2 start ecosystem.config.json --no-daemon", "dev": "cross-env NODE_ENV=development nodemon src/index.js", "demo": "cross-env NODE_ENV=demo nodemon src/index.js", "test": "jest -i --verbose false", "test:watch": "jest -i --watchAll", "coverage": "jest -i --coverage", "coverage:coveralls": "jest -i --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check **/*.js", "prettier:fix": "prettier --write **/*.js", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up"}, "keywords": ["node", "node.js", "boilerplate", "generator", "express", "rest", "api", "es6", "es7", "es8", "es9", "jest", "travis", "docker", "passport", "joi", "eslint", "prettier"], "dependencies": {"app-root-path": "^3.1.0", "archiver": "^7.0.1", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "cross-env": "^7.0.0", "csv-stringify": "^6.5.2", "dotenv": "^8.2.0", "exceljs": "^4.4.0", "express": "^4.17.1", "express-handlebars": "^7.1.3", "express-rate-limit": "^5.0.0", "fast-csv": "^5.0.2", "generate-password": "^1.7.1", "graphql": "^15.8.0", "graphql-request": "^6.1.0", "helmet": "^4.1.0", "http-status": "^1.8.1", "joi": "^17.3.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^8.5.1", "juice": "^8.1.0", "ldap-authentication": "^3.0.3", "ldapjs": "^3.0.7", "moment": "^2.24.0", "morgan": "^1.9.1", "multer": "^1.4.5-lts.1", "mysql2": "^2.3.3", "nodemailer": "^6.3.1", "nodemailer-express-handlebars": "^6.1.0", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "pdfkit-table": "^0.1.99", "pg": "^8.7.1", "pm2": "^4.5.6", "sequelize": "^6.6.5", "swagger-jsdoc": "4.2.0", "swagger-ui-express": "4.1.4", "tar": "^7.4.3", "tar-fs": "^3.0.8", "tar-stream": "^3.1.7", "uuid": "^11.0.3", "validator": "^13.0.0", "winston": "^3.2.1", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5", "xss-clean": "^0.1.1", "zlib": "^1.0.5"}, "devDependencies": {"coveralls": "^3.0.7", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^7.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^24.0.1", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-security": "^1.4.0", "faker": "^5.1.0", "husky": "^4.2.3", "jest": "^26.0.1", "lint-staged": "^10.0.7", "node-mocks-http": "^1.8.0", "nodemon": "^2.0.0", "prettier": "^2.0.5", "supertest": "^6.0.1"}}