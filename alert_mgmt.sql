USE smshub_db;

CREATE TABLE alerts (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    -- uid VARCHAR(255) NOT NULL,
    name VA<PERSON>HA<PERSON>(255),
    alert_timezone VARCHAR(255),
    evaluation_timeframe INT(11),
    run_every INT(11),
    filters <PERSON><PERSON><PERSON>,
    contact_point JSO<PERSON>,
    compared_timediff INT(11),
    compared_timerange INT(11),
    triggering_expression JSON,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    category VARCHAR(50),
    status VARCHAR(10),
    -- PRIMARY KEY (id, uid)
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
